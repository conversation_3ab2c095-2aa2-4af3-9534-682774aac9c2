var InteractionTag, InteractionStatus, TransactionRole, InteractionResolverKind, SubscriptionTopic, ixModule, ROLES, transaction_pb, require$$0, event_pb, hasRequiredEvent_pb, hasRequiredTransaction_pb, transaction_pbExports, access_pb, account_pb, hasRequiredAccount_pb, block_header_pb, require$$10, hasRequiredBlock_header_pb, block_pb, collection_pb, hasRequiredCollection_pb, block_seal_pb, hasRequiredBlock_seal_pb, execution_result_pb, hasRequiredExecution_result_pb, hasRequiredBlock_pb, metadata_pb, hasRequiredMetadata_pb, node_version_info_pb, hasRequiredNode_version_info_pb, hasRequiredAccess_pb, access_pbExports;
import { v4 } from "uuid";
import { Buffer as Buffer$1 } from "node:buffer";
import fetchTransport from "cross-fetch";
import EventEmitter$1, { EventEmitter } from "node:events";
import _WebSocket from "isomorphic-ws";
import * as googleProtobuf from "google-protobuf";
import * as timestamp_pb from "google-protobuf/google/protobuf/timestamp_pb.js";
function sansPrefix(address) {
  return null == address ? null : address.replace(/^0x/, "").replace(/^Fx/, "");
}
function withPrefix$1(address) {
  return null == address ? null : "0x" + sansPrefix(address);
}
let config$1 = null;
let LEVELS = function(LEVELS) {
  return LEVELS[LEVELS.debug = 5] = "debug", LEVELS[LEVELS.info = 4] = "info", LEVELS[LEVELS.log = 3] = "log", 
  LEVELS[LEVELS.warn = 2] = "warn", LEVELS[LEVELS.error = 1] = "error", LEVELS;
}({});
const log = async options => {
  const {title: title, message: message, level: level, always: always} = options, configLoggerLevel = await (config$1?.()?.get("logger.level")) ?? LEVELS.warn;
  if (!always && configLoggerLevel < level) return;
  const loggerMessageArgs = (options => {
    const {title: title, message: message} = options;
    return [ `\n    %c${title}\n    ============================\n\n    ${message}\n\n    ============================\n    `.replace(/\n[^\S\r\n]+/g, "\n").trim(), "font-weight:bold;font-family:monospace;" ];
  })({
    title: title,
    message: message
  });
  switch (level) {
   case LEVELS.debug:
    console.debug(...loggerMessageArgs);
    break;
   case LEVELS.info:
    console.info(...loggerMessageArgs);
    break;
   case LEVELS.warn:
    console.warn(...loggerMessageArgs);
    break;
   case LEVELS.error:
    console.error(...loggerMessageArgs);
    break;
   default:
    console.log(...loggerMessageArgs);
  }
};
function invariant$1(fact, msg) {
  if (!fact) {
    const error = new Error(`INVARIANT ${msg}`);
    error.stack = error.stack?.split("\n")?.filter((d => !/at invariant/.test(d)))?.join("\n");
    for (var _len = arguments.length, rest = new Array(_len > 2 ? _len - 2 : 0), _key = 2; _key < _len; _key++) rest[_key - 2] = arguments[_key];
    throw console.error("\n\n---\n\n", error, "\n\n", ...rest, "\n\n---\n\n"), error;
  }
}
log.deprecate = options => {
  const {pkg: pkg, subject: subject, transition: transition, level: level = LEVELS.warn, message: message = "", callback: callback = null} = options, logMessage = () => {
    return log({
      title: (pkg ? pkg + " " : "") + "Deprecation Notice",
      message: `\n      ${subject ? `${str = subject, str.charAt(0).toUpperCase() + str.slice(1)} is deprecated and will cease to work in future releases${pkg ? " of " + pkg : ""}.` : ""}${message ? "\n" + message : ""}${transition ? `\nYou can learn more (including a guide on common transition paths) here: ${transition}` : ""}\n    `.trim(),
      level: level
    });
    var str;
  };
  return "function" == typeof callback ? async function() {
    return await logMessage(), await callback(...arguments);
  } : logMessage();
}, InteractionTag = function(InteractionTag) {
  return InteractionTag.UNKNOWN = "UNKNOWN", InteractionTag.SCRIPT = "SCRIPT", InteractionTag.TRANSACTION = "TRANSACTION", 
  InteractionTag.GET_TRANSACTION_STATUS = "GET_TRANSACTION_STATUS", InteractionTag.GET_ACCOUNT = "GET_ACCOUNT", 
  InteractionTag.GET_EVENTS = "GET_EVENTS", InteractionTag.PING = "PING", InteractionTag.GET_TRANSACTION = "GET_TRANSACTION", 
  InteractionTag.GET_BLOCK = "GET_BLOCK", InteractionTag.GET_BLOCK_HEADER = "GET_BLOCK_HEADER", 
  InteractionTag.GET_COLLECTION = "GET_COLLECTION", InteractionTag.GET_NETWORK_PARAMETERS = "GET_NETWORK_PARAMETERS", 
  InteractionTag.SUBSCRIBE_EVENTS = "SUBSCRIBE_EVENTS", InteractionTag.GET_NODE_VERSION_INFO = "GET_NODE_VERSION_INFO", 
  InteractionTag;
}({}), InteractionStatus = function(InteractionStatus) {
  return InteractionStatus.BAD = "BAD", InteractionStatus.OK = "OK", InteractionStatus;
}({}), TransactionRole = function(TransactionRole) {
  return TransactionRole.AUTHORIZER = "authorizer", TransactionRole.PAYER = "payer", 
  TransactionRole.PROPOSER = "proposer", TransactionRole;
}({}), InteractionResolverKind = function(InteractionResolverKind) {
  return InteractionResolverKind.ARGUMENT = "ARGUMENT", InteractionResolverKind.ACCOUNT = "ACCOUNT", 
  InteractionResolverKind;
}({}), SubscriptionTopic = function(SubscriptionTopic) {
  return SubscriptionTopic.BLOCKS = "blocks", SubscriptionTopic.BLOCK_HEADERS = "block_headers", 
  SubscriptionTopic.BLOCK_DIGESTS = "block_digests", SubscriptionTopic.ACCOUNT_STATUSES = "account_statuses", 
  SubscriptionTopic.TRANSACTION_STATUSES = "transaction_statuses", SubscriptionTopic.EVENTS = "events", 
  SubscriptionTopic;
}({});
const mailbox = () => {
  const queue = [];
  let next;
  return {
    async deliver(msg) {
      queue.push(msg), next && (next(queue.shift()), next = void 0);
    },
    receive: () => new Promise((function(resolve) {
      const msg = queue.shift();
      if (msg) return resolve(msg);
      next = resolve;
    }))
  };
};
let promise;
const SUBSCRIBE = "SUBSCRIBE", UNSUBSCRIBE = "UNSUBSCRIBE", root = "object" == typeof self && self.self === self && self || "object" == typeof global && global.global === global && global || "object" == typeof window && window.window === window && window || {
  FCL_REGISTRY: null
};
root.FCL_REGISTRY = null == root.FCL_REGISTRY ? {} : root.FCL_REGISTRY;
const FCL_REGISTRY = root.FCL_REGISTRY;
let pid = 0;
function send$2(addr, tag, data) {
  let opts = arguments.length > 3 && void 0 !== arguments[3] ? arguments[3] : {
    expectReply: !1
  };
  return new Promise(((resolve, reject) => {
    const expectReply = opts.expectReply || !1, timeout = null != opts.timeout ? opts.timeout : 5e3;
    expectReply && timeout && setTimeout((() => reject(new Error(`Timeout: ${timeout}ms passed without a response.`))), timeout);
    const payload = {
      to: addr,
      from: opts.from,
      tag: tag,
      data: data,
      timeout: timeout,
      reply: resolve,
      reject: reject
    };
    try {
      FCL_REGISTRY[addr] && FCL_REGISTRY[addr].mailbox.deliver(payload), expectReply || resolve(!0);
    } catch (error) {
      console.error("FCL.Actor -- Could Not Deliver Message", payload, FCL_REGISTRY[addr], error), 
      reject(error);
    }
  }));
}
const spawn = function(fnOrHandlers) {
  const addr = (addr => {
    if (null == addr) {
      for (;FCL_REGISTRY[String(pid)]; ) pid++;
      return String(pid);
    }
    return String(addr);
  })(arguments.length > 1 && void 0 !== arguments[1] ? arguments[1] : null);
  if (null != FCL_REGISTRY[addr]) return addr;
  FCL_REGISTRY[addr] = {
    addr: addr,
    mailbox: mailbox(),
    subs: new Set,
    kvs: {},
    error: null
  };
  const ctx = createCtx(addr);
  let fn;
  var handlers, cb;
  return "object" == typeof fnOrHandlers ? (handlers = fnOrHandlers, fn = async ctx => {
    "function" == typeof handlers.INIT && await handlers.INIT(ctx);
    __loop: for (;;) {
      const letter = await ctx.receive();
      try {
        if ("EXIT" === letter.tag) {
          "function" == typeof handlers.TERMINATE && await handlers.TERMINATE(ctx, letter, letter.data || {});
          break __loop;
        }
        await (handlers[letter.tag]?.(ctx, letter, letter.data || {}));
      } catch (error) {
        console.error(`${ctx.self()} Error`, letter, error);
      } finally {
        continue __loop;
      }
    }
  }) : fn = fnOrHandlers, cb = async () => {
    await fn(ctx), (addr => {
      delete FCL_REGISTRY[addr];
    })(addr);
  }, (promise || (promise = Promise.resolve())).then(cb).catch((err => setTimeout((() => {
    throw err;
  }), 0))), addr;
}, createCtx = addr => ({
  self: () => addr,
  receive: () => FCL_REGISTRY[addr].mailbox.receive(),
  send: function(to, tag, data) {
    let opts = arguments.length > 3 && void 0 !== arguments[3] ? arguments[3] : {};
    if (null != to) return opts.from = addr, send$2(to, tag, data, opts);
  },
  sendSelf: function(tag, data) {
    let opts = arguments.length > 2 && void 0 !== arguments[2] ? arguments[2] : {};
    FCL_REGISTRY[addr] && send$2(addr, tag, data, opts);
  },
  broadcast: function(tag, data) {
    let opts = arguments.length > 2 && void 0 !== arguments[2] ? arguments[2] : {};
    opts.from = addr;
    for (const to of FCL_REGISTRY[addr].subs) send$2(to, tag, data, opts);
  },
  subscribe: sub => null != sub && FCL_REGISTRY[addr].subs.add(sub),
  unsubscribe: sub => null != sub && FCL_REGISTRY[addr].subs.delete(sub),
  subscriberCount: () => FCL_REGISTRY[addr].subs.size,
  hasSubs: () => !!FCL_REGISTRY[addr].subs.size,
  put: (key, value) => {
    null != key && (FCL_REGISTRY[addr].kvs[key] = value);
  },
  get: function(key) {
    let fallback = arguments.length > 1 && void 0 !== arguments[1] ? arguments[1] : void 0;
    const value = FCL_REGISTRY[addr].kvs[key];
    return null == value ? fallback : value;
  },
  delete: key => {
    delete FCL_REGISTRY[addr].kvs[key];
  },
  update: (key, fn) => {
    null != key && (FCL_REGISTRY[addr].kvs[key] = fn(FCL_REGISTRY[addr].kvs[key]));
  },
  keys: () => Object.keys(FCL_REGISTRY[addr].kvs),
  all: () => FCL_REGISTRY[addr].kvs,
  where: pattern => Object.keys(FCL_REGISTRY[addr].kvs).reduce(((acc, key) => pattern.test(key) ? {
    ...acc,
    [key]: FCL_REGISTRY[addr].kvs[key]
  } : acc), {}),
  merge: function() {
    let data = arguments.length > 0 && void 0 !== arguments[0] ? arguments[0] : {};
    Object.keys(data).forEach((key => FCL_REGISTRY[addr].kvs[key] = data[key]));
  },
  fatalError: error => {
    FCL_REGISTRY[addr].error = error;
    for (const to of FCL_REGISTRY[addr].subs) send$2(to, "UPDATED");
  }
});
const pipe$1 = function() {
  for (var _len = arguments.length, funcs = new Array(_len), _key = 0; _key < _len; _key++) funcs[_key] = arguments[_key];
  return v => funcs.reduce(((res, func) => func(res)), v);
}, isObject$1 = value => value && "object" == typeof value && !Array.isArray(value), mergeDeep = function(target) {
  for (var _len3 = arguments.length, sources = new Array(_len3 > 1 ? _len3 - 1 : 0), _key3 = 1; _key3 < _len3; _key3++) sources[_key3 - 1] = arguments[_key3];
  if (!sources.length) return target;
  const source = sources.shift();
  if (isObject$1(target) && isObject$1(source)) for (const key in source) isObject$1(source[key]) ? (target[key] || Object.assign(target, {
    [key]: {}
  }), mergeDeep(target[key], source[key])) : Object.assign(target, {
    [key]: source[key]
  });
  return mergeDeep(target, ...sources);
}, mergeFlowJSONs = value => Array.isArray(value) ? mergeDeep({}, ...value) : value, filterContracts = obj => obj.contracts ? obj.contracts : {}, filterDependencies = obj => obj.dependencies ? obj.dependencies : {}, getContracts = (jsons, network) => pipe$1(mergeFlowJSONs, function() {
  for (var _len2 = arguments.length, funcs = new Array(_len2), _key2 = 0; _key2 < _len2; _key2++) funcs[_key2] = arguments[_key2];
  return v => funcs.reduce(((res, func) => ({
    ...res,
    ...func(v)
  })), {});
}((network => _ref3 => {
  let {deployments: deployments = {}, accounts: accounts = {}} = _ref3;
  const networkDeployment = deployments?.[network];
  return networkDeployment ? Object.entries(networkDeployment).reduce(((c, _ref4) => {
    let [key, value] = _ref4;
    const accountAddress = accounts[key]?.address;
    return accountAddress ? value.reduce(((c, contract) => ({
      ...c,
      [contract]: accountAddress
    })), {}) : c;
  }), {}) : {};
})(network), pipe$1(filterContracts, (network => contracts => Object.entries(contracts).reduce(((c, _ref) => {
  let [key, value] = _ref;
  const networkContractAlias = value?.aliases?.[network];
  return networkContractAlias && (c[key] = networkContractAlias), c;
}), {}))(network)), pipe$1(filterDependencies, (network => dependencies => Object.entries(dependencies).reduce(((c, _ref2) => {
  let [key, value] = _ref2;
  const networkDependencyAlias = value?.aliases?.[network];
  return networkDependencyAlias && (c[key] = networkDependencyAlias), c;
}), {}))(network))))(jsons), hasPrivateKeys = flowJSON => Object.entries(flowJSON?.accounts ?? []).reduce(((hasPrivateKey, _ref5) => {
  let [, value] = _ref5;
  return !!hasPrivateKey || value && Object.prototype.hasOwnProperty.call(value, "key") && (str = value?.key, 
  "string" == typeof str && /^[0-9A-Fa-f]+$/.test(str));
  var str;
}), !1);
config$1 = config;
const NAME = "config", PUT = "PUT_CONFIG", GET = "GET_CONFIG", GET_ALL = "GET_ALL_CONFIG", UPDATE = "UPDATE_CONFIG", DELETE = "DELETE_CONFIG", CLEAR = "CLEAR_CONFIG", WHERE = "WHERE_CONFIG", UPDATED = "CONFIG/UPDATED", identity = v => v, HANDLERS = {
  [PUT]: (ctx, _letter, _ref) => {
    let {key: key, value: value} = _ref;
    if (null == key) throw new Error("Missing 'key' for config/put.");
    ctx.put(key, value), ctx.broadcast(UPDATED, {
      ...ctx.all()
    });
  },
  [GET]: (ctx, letter, _ref2) => {
    let {key: key, fallback: fallback} = _ref2;
    if (null == key) throw new Error("Missing 'key' for config/get");
    letter.reply(ctx.get(key, fallback));
  },
  [GET_ALL]: (ctx, letter) => {
    letter.reply({
      ...ctx.all()
    });
  },
  [UPDATE]: (ctx, letter, _ref3) => {
    let {key: key, fn: fn} = _ref3;
    if (null == key) throw new Error("Missing 'key' for config/update");
    ctx.update(key, fn || identity), ctx.broadcast(UPDATED, {
      ...ctx.all()
    });
  },
  [DELETE]: (ctx, letter, _ref4) => {
    let {key: key} = _ref4;
    if (null == key) throw new Error("Missing 'key' for config/delete");
    ctx.delete(key), ctx.broadcast(UPDATED, {
      ...ctx.all()
    });
  },
  [CLEAR]: ctx => {
    const keys = Object.keys(ctx.all());
    for (const key of keys) ctx.delete(key);
    ctx.broadcast(UPDATED, {
      ...ctx.all()
    });
  },
  [WHERE]: (ctx, letter, _ref5) => {
    let {pattern: pattern} = _ref5;
    if (null == pattern) throw new Error("Missing 'pattern' for config/where");
    letter.reply(ctx.where(pattern));
  },
  [SUBSCRIBE]: (ctx, letter) => {
    ctx.subscribe(letter.from), ctx.send(letter.from, UPDATED, {
      ...ctx.all()
    });
  },
  [UNSUBSCRIBE]: (ctx, letter) => {
    ctx.unsubscribe(letter.from);
  }
};
function put$1(key, value) {
  return send$2(NAME, PUT, {
    key: key,
    value: value
  }), config();
}
function get$1(key, fallback) {
  return send$2(NAME, GET, {
    key: key,
    fallback: fallback
  }, {
    expectReply: !0,
    timeout: 10
  });
}
async function first() {
  let wants = arguments.length > 0 && void 0 !== arguments[0] ? arguments[0] : [], fallback = arguments.length > 1 ? arguments[1] : void 0;
  if (!wants.length) return fallback;
  const [head, ...rest] = wants, ret = await get$1(head);
  return null == ret ? first(rest, fallback) : ret;
}
function all() {
  return send$2(NAME, GET_ALL, null, {
    expectReply: !0,
    timeout: 10
  });
}
function update$1(key) {
  return send$2(NAME, UPDATE, {
    key: key,
    fn: arguments.length > 1 && void 0 !== arguments[1] ? arguments[1] : identity
  }), config();
}
function _delete(key) {
  return send$2(NAME, DELETE, {
    key: key
  }), config();
}
function where(pattern) {
  return send$2(NAME, WHERE, {
    pattern: pattern
  }, {
    expectReply: !0,
    timeout: 10
  });
}
function subscribe$1(callback) {
  return function(address, spawnFn, callback) {
    spawnFn(address);
    const self1 = spawn((async ctx => {
      for (ctx.send(address, "SUBSCRIBE"); ;) {
        const letter = await ctx.receive(), error = FCL_REGISTRY[address].error;
        if ("EXIT" === letter.tag) return void ctx.send(address, "UNSUBSCRIBE");
        if (error) return callback(null, error), void ctx.send(address, "UNSUBSCRIBE");
        callback(letter.data, null);
      }
    }));
    return () => send$2(self1, "EXIT");
  }(NAME, (() => spawn(HANDLERS, NAME)), callback);
}
async function resetConfig(oldConfig) {
  return async function() {
    await send$2(NAME, CLEAR);
  }().then((() => config(oldConfig)));
}
async function load(data) {
  let {ignoreConflicts: ignoreConflicts = !1} = arguments.length > 1 && void 0 !== arguments[1] ? arguments[1] : {};
  const cleanedNetwork = (network => {
    const cleanedNetwork = "local" === network?.toLowerCase() ? "emulator" : network?.toLowerCase();
    if ("emulator" === cleanedNetwork || "testnet" === cleanedNetwork || "mainnet" === cleanedNetwork || "previewnet" === cleanedNetwork) return cleanedNetwork;
    throw new Error(`Invalid network "${network}". Must be one of "emulator", "local", "testnet", or "mainnet"`);
  })(await get$1("flow.network")), {flowJSON: flowJSON} = data;
  if (invariant$1(Boolean(flowJSON), "config.load -- 'flowJSON' must be defined"), 
  invariant$1(!!cleanedNetwork, 'Flow Network Required -- In order for FCL to load your contracts please define "flow.network" to "emulator", "local", "testnet", or "mainnet" in your config. See more here: https://developers.flow.com/tools/fcl-js/reference/configure-fcl'), 
  value = flowJSON, Array.isArray(value) ? value.some(hasPrivateKeys) : hasPrivateKeys(value)) {
    const isEmulator = "emulator" === cleanedNetwork;
    log({
      title: "Private Keys Detected",
      message: "Private keys should be stored in a separate flow.json file for security. See more here: https://developers.flow.com/tools/flow-cli/security",
      level: isEmulator ? LEVELS.warn : LEVELS.error
    }), invariant$1(isEmulator, "Private keys should be stored in a separate flow.json file for security. See more here: https://developers.flow.com/tools/flow-cli/security");
  }
  var value;
  for (const [key, value] of Object.entries(getContracts(flowJSON, cleanedNetwork))) {
    const contractConfigKey = `0x${key}`, existingContractConfigKey = await get$1(contractConfigKey);
    existingContractConfigKey && existingContractConfigKey !== value && !ignoreConflicts ? log({
      title: "Contract Placeholder Conflict Detected",
      message: "A generated contract placeholder from config.load conflicts with a placeholder you've set manually in config have the same name.",
      level: LEVELS.warn
    }) : put$1(contractConfigKey, value);
    const systemContractConfigKey = `system.contracts.${key}`, systemExistingContractConfigKeyValue = await get$1(systemContractConfigKey);
    systemExistingContractConfigKeyValue && systemExistingContractConfigKeyValue !== value && !ignoreConflicts ? log({
      title: "Contract Placeholder Conflict Detected",
      message: "A generated contract placeholder from config.load conflicts with a placeholder you've set manually in config have the same name.",
      level: LEVELS.warn
    }) : put$1(systemContractConfigKey, value);
  }
}
function config(values) {
  return null != values && "object" == typeof values && Object.keys(values).map((d => put$1(d, values[d]))), 
  {
    put: put$1,
    get: get$1,
    all: all,
    first: first,
    update: update$1,
    delete: _delete,
    where: where,
    subscribe: subscribe$1,
    overload: overload,
    load: load
  };
}
async function overload(values, callback) {
  const oldConfig = await all();
  try {
    config(values);
    return await callback(await all());
  } finally {
    await resetConfig(oldConfig);
  }
}
function encode(input) {
  if (Array.isArray(input)) {
    const output = [];
    for (let i = 0; i < input.length; i++) output.push(encode(input[i]));
    const buf = Buffer$1.concat(output);
    return Buffer$1.concat([ encodeLength(buf.length, 192), buf ]);
  }
  {
    const inputBuf = function(v) {
      if (!Buffer$1.isBuffer(v)) {
        if ("string" == typeof v) return isHexPrefixed(v) ? Buffer$1.from((a = "string" != typeof (str = v) ? str : isHexPrefixed(str) ? str.slice(2) : str).length % 2 ? "0" + a : a, "hex") : Buffer$1.from(v);
        if ("number" == typeof v) return v ? function(integer) {
          const hex = intToHex(integer);
          return Buffer$1.from(hex, "hex");
        }(v) : Buffer$1.from([]);
        if (null == v) return Buffer$1.from([]);
        if (v instanceof Uint8Array) return Buffer$1.from(v);
        throw new Error("invalid type");
      }
      var str;
      var a;
      return v;
    }(input);
    return 1 === inputBuf.length && inputBuf[0] < 128 ? inputBuf : Buffer$1.concat([ encodeLength(inputBuf.length, 128), inputBuf ]);
  }
}
function encodeLength(len, offset) {
  if (len < 56) return Buffer$1.from([ len + offset ]);
  {
    const hexLength = intToHex(len), firstByte = intToHex(offset + 55 + hexLength.length / 2);
    return Buffer$1.from(firstByte + hexLength, "hex");
  }
}
function isHexPrefixed(str) {
  return "0x" === str.slice(0, 2);
}
function intToHex(integer) {
  if (integer < 0) throw new Error("Invalid integer as argument, must be unsigned!");
  const hex = integer.toString(16);
  return hex.length % 2 ? "0" + hex : hex;
}
function safeParseJSON(data) {
  try {
    return JSON.parse(data);
  } catch {
    return null;
  }
}
function combineURLs(baseURL, relativeURL) {
  return relativeURL ? baseURL.replace(/\/+$/, "") + "/" + relativeURL.replace(/^\/+/, "") : baseURL;
}
spawn(HANDLERS, NAME), config.put = put$1, config.get = get$1, config.all = all, 
config.first = first, config.update = update$1, config.delete = _delete, config.where = where, 
config.subscribe = subscribe$1, config.overload = overload, config.load = load;
const AbortController = globalThis.AbortController || require("abort-controller");
class HTTPRequestError extends Error {
  constructor(_ref) {
    let {error: error, hostname: hostname, path: path, method: method, requestBody: requestBody, responseBody: responseBody, responseStatusText: responseStatusText, statusCode: statusCode} = _ref;
    super(`\n      HTTP Request Error: An error occurred when interacting with the Access API.\n      ${error ? `error=${error}` : ""}\n      ${hostname ? `hostname=${hostname}` : ""}\n      ${path ? `path=${path}` : ""}\n      ${method ? `method=${method}` : ""}\n      ${requestBody ? `requestBody=${requestBody}` : ""}\n      ${responseBody ? `responseBody=${responseBody}` : ""}\n      ${responseStatusText ? `responseStatusText=${responseStatusText}` : ""}\n      ${statusCode ? `statusCode=${statusCode}` : ""}\n    `), 
    this.name = "HTTP Request Error", this.statusCode = statusCode, this.errorMessage = error;
  }
}
async function httpRequest(_ref2) {
  let {hostname: hostname, path: path, method: method, body: body, headers: headers, retryLimit: retryLimit = 5, retryIntervalMs: retryIntervalMs = 1e3, timeoutLimit: timeoutLimit = 3e4, enableRequestLogging: enableRequestLogging = !0} = _ref2;
  const bodyJSON = body ? JSON.stringify(body) : null;
  return await async function requestLoop() {
    let retryAttempt = arguments.length > 0 && void 0 !== arguments[0] ? arguments[0] : 0;
    try {
      const resp = await function() {
        const controller = new AbortController, fetchTimeout = setTimeout((() => {
          controller.abort();
        }), timeoutLimit);
        return fetchTransport(combineURLs(hostname, path).toString(), {
          method: method,
          body: bodyJSON,
          headers: headers,
          signal: controller.signal
        }).then((async res => {
          if (res.ok) return res.json();
          const responseText = await res.text().catch((() => null)), response = safeParseJSON(responseText);
          throw new HTTPRequestError({
            error: response?.message,
            hostname: hostname,
            path: path,
            method: method,
            requestBody: bodyJSON,
            responseBody: responseText,
            responseStatusText: res.statusText,
            statusCode: res.status
          });
        })).catch((async e => {
          if (e instanceof HTTPRequestError) throw e;
          if ("AbortError" === e.name) throw e;
          throw enableRequestLogging && await log({
            title: "Access Node Error",
            message: `The provided access node ${hostname} does not appear to be a valid REST/HTTP access node.\n  Please verify that you are not unintentionally using a GRPC access node.\n  See more here: https://docs.onflow.org/fcl/reference/sdk-guidelines/#connect`,
            level: LEVELS.error
          }), new HTTPRequestError({
            error: e?.message,
            hostname: hostname,
            path: path,
            method: method,
            requestBody: bodyJSON
          });
        })).finally((() => {
          clearTimeout(fetchTimeout);
        }));
      }();
      return resp;
    } catch (error) {
      const retryStatusCodes = [ 408, 429, 500, 502, 503, 504 ];
      if ("AbortError" === error.name || retryStatusCodes.includes(error.statusCode)) return await new Promise(((resolve, reject) => {
        retryAttempt < retryLimit ? (enableRequestLogging && console.warn(`Access node unavailable, retrying in ${retryIntervalMs} ms...`), 
        setTimeout((() => {
          resolve(requestLoop(retryAttempt + 1));
        }), retryIntervalMs)) : reject(error);
      }));
      throw error;
    }
  }();
}
function constructResponse$5(ix, context, res) {
  let ret = context.response();
  return ret.tag = ix.tag, ret.encodedData = JSON.parse(context.Buffer.from(res, "base64").toString()), 
  ret;
}
async function sendExecuteScript(ix) {
  let context = arguments.length > 1 && void 0 !== arguments[1] ? arguments[1] : {}, opts = arguments.length > 2 && void 0 !== arguments[2] ? arguments[2] : {};
  return invariant$1(opts.node, "SDK Send Execute Script Error: opts.node must be defined."), 
  invariant$1(context.response, "SDK Send Execute Script Error: context.response must be defined."), 
  invariant$1(context.Buffer, "SDK Send Execute Script Error: context.Buffer must be defined."), 
  (ix = await ix).block.id ? await async function(ix, context, opts) {
    const httpRequest$1 = opts.httpRequest || httpRequest, res = await httpRequest$1({
      hostname: opts.node,
      path: `/v1/scripts?block_id=${ix.block.id}`,
      method: "POST",
      body: {
        script: context.Buffer.from(ix.message.cadence).toString("base64"),
        arguments: ix.message.arguments.map((arg => context.Buffer.from(JSON.stringify(ix.arguments[arg].asArgument)).toString("base64")))
      }
    });
    return constructResponse$5(ix, context, res);
  }(ix, context, opts) : ix.block.height ? await async function(ix, context, opts) {
    const httpRequest$1 = opts.httpRequest || httpRequest, res = await httpRequest$1({
      hostname: opts.node,
      path: `/v1/scripts?block_height=${ix.block.height}`,
      method: "POST",
      body: {
        script: context.Buffer.from(ix.message.cadence).toString("base64"),
        arguments: ix.message.arguments.map((arg => context.Buffer.from(JSON.stringify(ix.arguments[arg].asArgument)).toString("base64")))
      }
    });
    return constructResponse$5(ix, context, res);
  }(ix, context, opts) : await async function(ix, context, opts) {
    const httpRequest$1 = opts.httpRequest || httpRequest, res = await httpRequest$1({
      hostname: opts.node,
      path: "/v1/scripts?block_height=" + (ix.block.isSealed ? "sealed" : "final"),
      method: "POST",
      body: {
        script: context.Buffer.from(ix.message.cadence).toString("base64"),
        arguments: ix.message.arguments.map((arg => context.Buffer.from(JSON.stringify(ix.arguments[arg].asArgument)).toString("base64")))
      }
    });
    return constructResponse$5(ix, context, res);
  }(ix, context, opts);
}
const HashAlgorithmIDs = {
  SHA2_256: 1,
  SHA2_384: 2,
  SHA3_256: 3,
  SHA3_384: 4,
  KMAC128_BLS_BLS12_381: 5
}, SignatureAlgorithmIDs = {
  ECDSA_P256: 1,
  ECDSA_secp256k1: 2,
  BLS_BLS12_381: 3
};
function constructResponse$4(ix, context, res) {
  let ret = context.response();
  ret.tag = ix.tag;
  return ret.account = {
    address: res.address,
    balance: Number(res.balance),
    code: "",
    contracts: (contracts => {
      const c = {};
      if (!contracts) return c;
      for (let key of Object.keys(contracts)) c[key] = context.Buffer.from(contracts[key], "base64").toString();
      return c;
    })(res.contracts),
    keys: res.keys?.map((key => ({
      index: Number(key.index),
      publicKey: key.public_key.replace(/^0x/, ""),
      signAlgo: SignatureAlgorithmIDs[key.signing_algorithm],
      signAlgoString: key.signing_algorithm,
      hashAlgo: HashAlgorithmIDs[key.hashing_algorithm],
      hashAlgoString: key.hashing_algorithm,
      sequenceNumber: Number(key.sequence_number),
      weight: Number(key.weight),
      revoked: key.revoked
    }))) ?? []
  }, ret;
}
async function sendGetAccount(ix) {
  let context = arguments.length > 1 && void 0 !== arguments[1] ? arguments[1] : {}, opts = arguments.length > 2 && void 0 !== arguments[2] ? arguments[2] : {};
  return invariant$1(opts.node, "SDK Send Get Account Error: opts.node must be defined."), 
  invariant$1(context.response, "SDK Send Get Account Error: context.response must be defined."), 
  invariant$1(context.Buffer, "SDK Send Get Account Error: context.Buffer must be defined."), 
  null !== (ix = await ix).block.height ? await async function(ix, context, opts) {
    const httpRequest$1 = opts.httpRequest || httpRequest;
    return constructResponse$4(ix, context, await httpRequest$1({
      hostname: opts.node,
      path: `/v1/accounts/${ix.account.addr}?block_height=${ix.block.height}&expand=contracts,keys`,
      method: "GET",
      body: null
    }));
  }(ix, context, opts) : await async function(ix, context, opts) {
    const httpRequest$1 = opts.httpRequest || httpRequest;
    return constructResponse$4(ix, context, await httpRequest$1({
      hostname: opts.node,
      path: `/v1/accounts/${ix.account.addr}?block_height=${ix.block.isSealed ? "sealed" : "final"}&expand=contracts,keys`,
      method: "GET",
      body: null
    }));
  }(ix, context, opts);
}
function constructResponse$3(ix, context, res) {
  const block = res.length ? res[0] : null, ret = context.response();
  return ret.tag = ix.tag, ret.blockHeader = {
    id: block.header.id,
    parentId: block.header.parent_id,
    height: Number(block.header.height),
    timestamp: block.header.timestamp
  }, ret;
}
async function sendGetBlockHeader(ix) {
  let context = arguments.length > 1 && void 0 !== arguments[1] ? arguments[1] : {}, opts = arguments.length > 2 && void 0 !== arguments[2] ? arguments[2] : {};
  invariant$1(opts.node, "SDK Send Get Block Header Error: opts.node must be defined."), 
  invariant$1(context.response, "SDK Send Get Block Header Error: context.response must be defined.");
  const interactionHasBlockID = null !== (ix = await ix).block.id, interactionHasBlockHeight = null !== ix.block.height;
  return interactionHasBlockID ? await async function(ix, context, opts) {
    const httpRequest$1 = opts.httpRequest || httpRequest;
    return constructResponse$3(ix, context, await httpRequest$1({
      hostname: opts.node,
      path: `/v1/blocks/${ix.block.id}`,
      method: "GET",
      body: null
    }));
  }(ix, context, opts) : interactionHasBlockHeight ? await async function(ix, context, opts) {
    const httpRequest$1 = opts.httpRequest || httpRequest;
    return constructResponse$3(ix, context, await httpRequest$1({
      hostname: opts.node,
      path: `/v1/blocks?height=${ix.block.height}`,
      method: "GET",
      body: null
    }));
  }(ix, context, opts) : await async function(ix, context, opts) {
    const httpRequest$1 = opts.httpRequest || httpRequest, height = ix.block?.isSealed ? "sealed" : "final";
    return constructResponse$3(ix, context, await httpRequest$1({
      hostname: opts.node,
      path: `/v1/blocks?height=${height}`,
      method: "GET",
      body: null
    }));
  }(ix, context, opts);
}
function constructResponse$2(ix, context, res) {
  const block = res.length ? res[0] : null, ret = context.response();
  return ret.tag = ix.tag, ret.block = {
    id: block.header.id,
    parentId: block.header.parent_id,
    height: Number(block.header.height),
    timestamp: block.header.timestamp,
    parentVoterSignature: block.header.parent_voter_signature,
    collectionGuarantees: block.payload.collection_guarantees.map((collectionGuarantee => ({
      collectionId: collectionGuarantee.collection_id,
      signerIds: collectionGuarantee.signer_ids
    }))),
    blockSeals: block.payload.block_seals.map((blockSeal => ({
      blockId: blockSeal.block_id,
      executionReceiptId: blockSeal.result_id
    })))
  }, ret;
}
async function sendGetBlock(ix) {
  let context = arguments.length > 1 && void 0 !== arguments[1] ? arguments[1] : {}, opts = arguments.length > 2 && void 0 !== arguments[2] ? arguments[2] : {};
  invariant$1(opts.node, "SDK Send Get Block Error: opts.node must be defined."), 
  invariant$1(context.response, "SDK Send Get Block Error: context.response must be defined.");
  const interactionHasBlockID = null !== (ix = await ix).block.id, interactionHasBlockHeight = null !== ix.block.height;
  return interactionHasBlockID ? await async function(ix, context, opts) {
    const httpRequest$1 = opts.httpRequest || httpRequest;
    return constructResponse$2(ix, context, await httpRequest$1({
      hostname: opts.node,
      path: `/v1/blocks/${ix.block.id}?expand=payload`,
      method: "GET",
      body: null
    }));
  }(ix, context, opts) : interactionHasBlockHeight ? await async function(ix, context, opts) {
    const httpRequest$1 = opts.httpRequest || httpRequest;
    return constructResponse$2(ix, context, await httpRequest$1({
      hostname: opts.node,
      path: `/v1/blocks?height=${ix.block.height}&expand=payload`,
      method: "GET",
      body: null
    }));
  }(ix, context, opts) : await async function(ix, context, opts) {
    const httpRequest$1 = opts.httpRequest || httpRequest, height = ix.block?.isSealed ? "sealed" : "final";
    return constructResponse$2(ix, context, await httpRequest$1({
      hostname: opts.node,
      path: `/v1/blocks?height=${height}&expand=payload`,
      method: "GET",
      body: null
    }));
  }(ix, context, opts);
}
function constructResponse$1(ix, context, res) {
  let ret = context.response();
  return ret.tag = ix.tag, ret.events = [], res.forEach((block => block.events ? block.events.forEach((event => ret.events.push({
    blockId: block.block_id,
    blockHeight: Number(block.block_height),
    blockTimestamp: block.block_timestamp,
    type: event.type,
    transactionId: event.transaction_id,
    transactionIndex: Number(event.transaction_index),
    eventIndex: Number(event.event_index),
    payload: JSON.parse(context.Buffer.from(event.payload, "base64").toString())
  }))) : null)), ret;
}
async function sendGetEvents(ix) {
  let context = arguments.length > 1 && void 0 !== arguments[1] ? arguments[1] : {}, opts = arguments.length > 2 && void 0 !== arguments[2] ? arguments[2] : {};
  invariant$1(opts.node, "SDK Send Get Events Error: opts.node must be defined."), 
  invariant$1(context.response, "SDK Send Get Events Error: context.response must be defined."), 
  invariant$1(context.Buffer, "SDK Send Get Events Error: context.Buffer must be defined.");
  const interactionContainsBlockHeightRange = null !== (ix = await ix).events.start, interactionContainsBlockIDsList = Array.isArray(ix.events.blockIds) && ix.events.blockIds.length > 0;
  return invariant$1(interactionContainsBlockHeightRange || interactionContainsBlockIDsList, "SendGetEventsError: Unable to determine which get events request to send. Either a block height range, or block IDs must be specified."), 
  interactionContainsBlockHeightRange ? await async function(ix, context, opts) {
    const httpRequest$1 = opts.httpRequest || httpRequest;
    return constructResponse$1(ix, context, await httpRequest$1({
      hostname: opts.node,
      path: `/v1/events?type=${ix.events.eventType}&start_height=${ix.events.start}&end_height=${ix.events.end}`,
      method: "GET",
      body: null
    }));
  }(ix, context, opts) : await async function(ix, context, opts) {
    const httpRequest$1 = opts.httpRequest || httpRequest, res = await httpRequest$1({
      hostname: opts.node,
      path: `/v1/events?type=${ix.events.eventType}&block_ids=${ix.events.blockIds.join(",")}`,
      method: "GET",
      body: null
    });
    return constructResponse$1(ix, context, res);
  }(ix, context, opts);
}
const STATUS_MAP$1 = {
  UNKNOWN: 0,
  PENDING: 1,
  FINALIZED: 2,
  EXECUTED: 3,
  SEALED: 4,
  EXPIRED: 5
};
const idof = acct => `${withPrefix(acct.addr)}-${acct.keyId}`;
const WebSocket = _WebSocket;
class WebsocketError extends Error {
  constructor(_ref) {
    let {code: code, reason: reason, message: message, wasClean: wasClean} = _ref;
    super(`\n      connectWs: connection closed with error${message ? `: ${message}` : ""}\n      ${code ? `code: ${code}` : ""}\n      ${reason ? `reason: ${reason}` : ""}\n      ${wasClean ? `wasClean: ${wasClean}` : ""}\n    `), 
    this.name = "WebsocketError", this.code = code, this.reason = reason, this.wasClean = !1;
  }
}
function connectWs(_ref2) {
  let {hostname: hostname, path: path, params: params, getParams: getParams, retryLimit: retryLimit = 5, retryIntervalMs: retryIntervalMs = 1e3} = _ref2;
  if (getParams && params) throw new Error("connectWs: cannot specify both params and getParams");
  let outputEmitter = new EventEmitter, retryCount = 0;
  const resolveParams = getParams || (() => params);
  let close = () => {};
  return function connect() {
    let userClosed = !1, hasOpened = !1;
    const url = function(hostname, path, params) {
      const url = new URL(path || "", hostname);
      "https:" === url.protocol ? url.protocol = "wss:" : "http:" === url.protocol && (url.protocol = "ws:");
      return Object.entries(params || {}).forEach((_ref3 => {
        let [key, value] = _ref3;
        if (value) {
          let formattedValue;
          formattedValue = Array.isArray(value) ? value.join(",") : value.toString(), url.searchParams.append(key, formattedValue);
        }
      })), url.toString();
    }(hostname, path, resolveParams()), ws = new WebSocket(url);
    ws.onmessage = function(e) {
      const data = safeParseJSON(e.data);
      data ? outputEmitter.emit("data", data) : (outputEmitter.emit("error", new WebsocketError({
        message: "invalid JSON data"
      })), this.close());
    }, ws.onclose = function(e) {
      if (userClosed) return outputEmitter.emit("close"), void outputEmitter.removeAllListeners();
      hasOpened ? setTimeout(connect, retryIntervalMs) : retryCount < retryLimit ? (retryCount++, 
      setTimeout(connect, retryIntervalMs)) : (outputEmitter.emit("error", new WebsocketError({
        wasClean: e.wasClean,
        code: e.code,
        reason: e.reason,
        message: "failed to connect"
      })), setTimeout((() => {
        outputEmitter.emit("close"), outputEmitter.removeAllListeners();
      })));
    }, ws.onopen = function() {
      hasOpened = !0, retryCount = 0;
    }, close = () => {
      userClosed = !0, ws.close();
    };
  }(), {
    on(event, listener) {
      return outputEmitter.on(event, listener), this;
    },
    off(event, listener) {
      return outputEmitter.off(event, listener), this;
    },
    close() {
      close();
    }
  };
}
async function connectSubscribeEvents(ix) {
  let context = arguments.length > 1 && void 0 !== arguments[1] ? arguments[1] : {}, opts = arguments.length > 2 && void 0 !== arguments[2] ? arguments[2] : {};
  invariant$1(opts.node, "SDK Send Get Events Error: opts.node must be defined."), 
  invariant$1(context.response, "SDK Send Get Events Error: context.response must be defined."), 
  invariant$1(context.Buffer, "SDK Send Get Events Error: context.Buffer must be defined.");
  const resolvedIx = await ix, connectWs$1 = opts.connectWs || connectWs, outputEmitter = new EventEmitter;
  let lastBlockHeight = null;
  const connection = connectWs$1({
    hostname: opts.node,
    path: "/v1/subscribe_events",
    getParams: () => {
      const params = {
        event_types: resolvedIx.subscribeEvents?.eventTypes,
        addresses: resolvedIx.subscribeEvents?.addresses,
        contracts: resolvedIx.subscribeEvents?.contracts,
        heartbeat_interval: resolvedIx.subscribeEvents?.heartbeatInterval
      };
      return lastBlockHeight ? params.start_height = lastBlockHeight + 1 : (params.start_block_id = resolvedIx.subscribeEvents?.startBlockId, 
      params.start_height = resolvedIx.subscribeEvents?.startHeight), params;
    }
  });
  connection.on("data", (data => {
    const responseData = function(ix, context, data) {
      const response = context.response();
      return response.tag = ix.tag, response.events = data.Events?.length > 0 ? data.Events.map((event => ({
        blockId: data.BlockID,
        blockHeight: Number(data.Height),
        blockTimestamp: data.BlockTimestamp,
        type: event.Type,
        transactionId: event.TransactionID,
        transactionIndex: Number(event.TransactionIndex),
        eventIndex: Number(event.EventIndex),
        payload: JSON.parse(context.Buffer.from(event.Payload, "base64").toString())
      }))) : null, response.heartbeat = {
        blockId: data.BlockID,
        blockHeight: Number(data.Height),
        blockTimestamp: data.BlockTimestamp
      }, response;
    }(resolvedIx, context, data);
    lastBlockHeight = responseData.heartbeat.blockHeight, outputEmitter.emit("data", responseData);
  })), connection.on("error", (error => {
    outputEmitter.emit("error", error);
  })), connection.on("close", (() => {
    outputEmitter.emit("close");
  }));
  return function(ix, context, stream) {
    const response = context.response();
    return response.tag = ix.tag, response.streamConnection = stream, response;
  }(resolvedIx, context, {
    on(event, listener) {
      return outputEmitter.on(event, listener), this;
    },
    off(event, listener) {
      return outputEmitter.off(event, listener), this;
    },
    close() {
      connection.close();
    }
  });
}
const send$1 = async function(ix, context) {
  let opts = arguments.length > 2 && void 0 !== arguments[2] ? arguments[2] : {};
  switch (invariant$1(Boolean(opts?.node), 'SDK Send Error: Either opts.node or "accessNode.api" in config must be defined.'), 
  invariant$1(Boolean(context.ix), "SDK Send Error: context.ix must be defined."), 
  ix = await ix, !0) {
   case context.ix.isTransaction(ix):
    return opts.sendTransaction ? opts.sendTransaction(ix, context, opts) : async function(ix) {
      var t1, t2;
      let context = arguments.length > 1 && void 0 !== arguments[1] ? arguments[1] : {}, opts = arguments.length > 2 && void 0 !== arguments[2] ? arguments[2] : {};
      invariant$1(opts.node, "SDK Send Transaction Error: opts.node must be defined."), 
      invariant$1(context.response, "SDK Send Transaction Error: context.response must be defined."), 
      invariant$1(context.Buffer, "SDK Send Transaction Error: context.Buffer must be defined.");
      const httpRequest$1 = opts.httpRequest || httpRequest;
      ix = await ix;
      let payloadSignatures = [];
      for (let acct of Object.values(ix.accounts)) try {
        if (!acct.role.payer && null != acct.signature) {
          const signature = {
            address: sansPrefix(acct.addr),
            key_index: String(acct.keyId),
            signature: context.Buffer.from(acct.signature, "hex").toString("base64")
          };
          payloadSignatures.find((existingSignature => existingSignature.address === signature.address && existingSignature.key_index === signature.key_index && existingSignature.signature === signature.signature)) || payloadSignatures.push(signature);
        }
      } catch (error) {
        throw console.error("SDK HTTP Send Error: Trouble applying payload signature", {
          acct: acct,
          ix: ix
        }), error;
      }
      let envelopeSignatures = {};
      for (let acct of Object.values(ix.accounts)) try {
        if (acct.role.payer && null != acct.signature) {
          let id = acct.tempId || idof(acct);
          envelopeSignatures[id] = envelopeSignatures[id] || {
            address: sansPrefix(acct.addr),
            key_index: String(acct.keyId),
            signature: context.Buffer.from(acct.signature, "hex").toString("base64")
          };
        }
      } catch (error) {
        throw console.error("SDK HTTP Send Error: Trouble applying envelope signature", {
          acct: acct,
          ix: ix
        }), error;
      }
      envelopeSignatures = Object.values(envelopeSignatures), t1 = Date.now();
      const res = await httpRequest$1({
        hostname: opts.node,
        path: "/v1/transactions",
        method: "POST",
        body: {
          script: context.Buffer.from(ix.message.cadence).toString("base64"),
          arguments: [ ...ix.message.arguments.map((arg => context.Buffer.from(JSON.stringify(ix.arguments[arg].asArgument)).toString("base64"))) ],
          reference_block_id: ix.message.refBlock ? ix.message.refBlock : null,
          gas_limit: String(ix.message.computeLimit),
          payer: sansPrefix(ix.accounts[Array.isArray(ix.payer) ? ix.payer[0] : ix.payer].addr),
          proposal_key: {
            address: sansPrefix(ix.accounts[ix.proposer].addr),
            key_index: String(ix.accounts[ix.proposer].keyId),
            sequence_number: String(ix.accounts[ix.proposer].sequenceNum)
          },
          authorizers: ix.authorizations.map((tempId => ix.accounts[tempId].addr)).reduce(((prev, current) => prev.find((item => item === current)) ? prev : [ ...prev, current ]), []).map(sansPrefix),
          payload_signatures: payloadSignatures,
          envelope_signatures: envelopeSignatures
        }
      });
      t2 = Date.now();
      let ret = context.response();
      return ret.tag = ix.tag, ret.transactionId = res.id, "undefined" != typeof window && "undefined" != typeof CustomEvent && window.dispatchEvent(new CustomEvent("FLOW::TX", {
        detail: {
          txId: ret.transactionId,
          delta: t2 - t1
        }
      })), ret;
    }(ix, context, opts);
   case context.ix.isGetTransactionStatus(ix):
    return opts.sendGetTransactionStatus ? opts.sendGetTransactionStatus(ix, context, opts) : async function(ix) {
      let context = arguments.length > 1 && void 0 !== arguments[1] ? arguments[1] : {}, opts = arguments.length > 2 && void 0 !== arguments[2] ? arguments[2] : {};
      invariant$1(opts.node, "SDK Send Get Transaction Status Error: opts.node must be defined."), 
      invariant$1(context.response, "SDK Send Get Transaction Status Error: context.response must be defined."), 
      invariant$1(context.Buffer, "SDK Send Get Transaction Status Error: context.Buffer must be defined.");
      const httpRequest$1 = opts.httpRequest || httpRequest;
      ix = await ix;
      const res = await httpRequest$1({
        hostname: opts.node,
        path: `/v1/transaction_results/${ix.transaction.id}`,
        method: "GET",
        body: null
      });
      let ret = context.response();
      return ret.tag = ix.tag, ret.transactionStatus = {
        blockId: res.block_id,
        status: STATUS_MAP$1[res.status.toUpperCase()] || "",
        statusString: res.status.toUpperCase(),
        statusCode: res.status_code,
        errorMessage: res.error_message,
        events: res.events.map((event => ({
          type: event.type,
          transactionId: event.transaction_id,
          transactionIndex: Number(event.transaction_index),
          eventIndex: Number(event.event_index),
          payload: JSON.parse(context.Buffer.from(event.payload, "base64").toString())
        })))
      }, ret;
    }(ix, context, opts);
   case context.ix.isGetTransaction(ix):
    return opts.sendGetTransaction ? opts.sendGetTransaction(ix, context, opts) : async function(ix) {
      let context = arguments.length > 1 && void 0 !== arguments[1] ? arguments[1] : {}, opts = arguments.length > 2 && void 0 !== arguments[2] ? arguments[2] : {};
      invariant$1(opts.node, "SDK Send Get Transaction Error: opts.node must be defined."), 
      invariant$1(context.response, "SDK Send Get Transaction Error: context.response must be defined."), 
      invariant$1(context.Buffer, "SDK Send Get Transaction Error: context.Buffer must be defined.");
      const httpRequest$1 = opts.httpRequest || httpRequest;
      ix = await ix;
      const res = await httpRequest$1({
        hostname: opts.node,
        path: `/v1/transactions/${ix.transaction.id}`,
        method: "GET",
        body: null
      }), unwrapSignature = sig => ({
        address: sig.address,
        keyId: Number(sig.key_index),
        signature: sig.signature
      });
      let ret = context.response();
      var key;
      return ret.tag = ix.tag, ret.transaction = {
        script: context.Buffer.from(res.script, "base64").toString(),
        args: [ ...res.arguments.map((arg => JSON.parse(context.Buffer.from(arg, "base64").toString()))) ],
        referenceBlockId: res.reference_block_id,
        gasLimit: Number(res.gas_limit),
        payer: res.payer,
        proposalKey: res.proposal_key ? (key = res.proposal_key, {
          address: key.address,
          keyId: Number(key.key_index),
          sequenceNumber: Number(key.sequence_number)
        }) : res.proposal_key,
        authorizers: res.authorizers,
        payloadSignatures: [ ...res.payload_signatures.map(unwrapSignature) ],
        envelopeSignatures: [ ...res.envelope_signatures.map(unwrapSignature) ]
      }, ret;
    }(ix, context, opts);
   case context.ix.isScript(ix):
    return opts.sendExecuteScript ? opts.sendExecuteScript(ix, context, opts) : sendExecuteScript(ix, context, opts);
   case context.ix.isGetAccount(ix):
    return opts.sendGetAccount ? opts.sendGetAccount(ix, context, opts) : sendGetAccount(ix, context, opts);
   case context.ix.isGetEvents(ix):
    return opts.sendGetEvents ? opts.sendGetEvents(ix, context, opts) : sendGetEvents(ix, context, opts);
   case context.ix.isSubscribeEvents?.(ix):
    return opts.connectSubscribeEvents ? opts.connectSubscribeEvents(ix, context, opts) : connectSubscribeEvents(ix, context, opts);
   case context.ix.isGetBlock(ix):
    return opts.sendGetBlock ? opts.sendGetBlock(ix, context, opts) : sendGetBlock(ix, context, opts);
   case context.ix.isGetBlockHeader(ix):
    return opts.sendGetBlockHeader ? opts.sendGetBlockHeader(ix, context, opts) : sendGetBlockHeader(ix, context, opts);
   case context.ix.isGetCollection(ix):
    return opts.sendGetCollection ? opts.sendGetCollection(ix, context, opts) : async function(ix) {
      let context = arguments.length > 1 && void 0 !== arguments[1] ? arguments[1] : {}, opts = arguments.length > 2 && void 0 !== arguments[2] ? arguments[2] : {};
      invariant$1(opts.node, "SDK Send Get Collection Error: opts.node must be defined."), 
      invariant$1(context.response, "SDK Send Get Collection Error: context.response must be defined.");
      const httpRequest$1 = opts.httpRequest || httpRequest, res = await httpRequest$1({
        hostname: opts.node,
        path: `/v1/collections/${ix.collection.id}?expand=transactions`,
        method: "GET",
        body: null
      }), ret = context.response();
      return ret.tag = ix.tag, ret.collection = {
        id: res.id,
        transactionIds: res.transactions.map((transaction => transaction.id))
      }, ret;
    }(ix, context, opts);
   case context.ix.isPing(ix):
    return opts.sendPing ? opts.sendPing(ix, context, opts) : async function(ix) {
      let context = arguments.length > 1 && void 0 !== arguments[1] ? arguments[1] : {}, opts = arguments.length > 2 && void 0 !== arguments[2] ? arguments[2] : {};
      invariant$1(Boolean(opts.node), "SDK Send Ping Error: opts.node must be defined."), 
      invariant$1(Boolean(context.response), "SDK Send Ping Error: context.response must be defined.");
      const httpRequest$1 = opts.httpRequest || httpRequest;
      await httpRequest$1({
        hostname: opts.node,
        path: "/v1/blocks?height=sealed",
        method: "GET",
        body: null
      });
      let ret = "function" == typeof context?.response ? context.response() : {};
      return ret.tag = ix.tag, ret;
    }(ix, context, opts);
   case context.ix.isGetNetworkParameters(ix):
    return opts.sendGetNetworkParameters ? opts.sendGetNetworkParameters(ix, context, opts) : async function(ix) {
      let context = arguments.length > 1 && void 0 !== arguments[1] ? arguments[1] : {}, opts = arguments.length > 2 && void 0 !== arguments[2] ? arguments[2] : {};
      invariant$1(opts.node, "SDK Send Get Network Parameters Error: opts.node must be defined."), 
      invariant$1(context.response, "SDK Send Get Network Parameters Error: context.response must be defined.");
      const httpRequest$1 = opts.httpRequest || httpRequest;
      ix = await ix;
      const res = await httpRequest$1({
        hostname: opts.node,
        path: "/v1/network/parameters",
        method: "GET",
        body: null,
        enableRequestLogging: opts.enableRequestLogging ?? !0
      });
      let ret = context.response();
      return ret.tag = ix.tag, ret.networkParameters = {
        chainId: res.chain_id
      }, ret;
    }(ix, context, opts);
   case context.ix.isGetNodeVersionInfo?.(ix):
    return opts.sendGetNodeVersionInfo ? opts.sendGetNodeVersionInfo(ix, context, opts) : async function(ix) {
      let context = arguments.length > 1 && void 0 !== arguments[1] ? arguments[1] : {}, opts = arguments.length > 2 && void 0 !== arguments[2] ? arguments[2] : {};
      invariant$1(opts.node, "SDK Send Get Node Version Info Error: opts.node must be defined."), 
      invariant$1(context.response, "SDK Send Get Node Verison Info Error: context.response must be defined.");
      const httpRequest$1 = opts.httpRequest || httpRequest;
      ix = await ix;
      const res = await httpRequest$1({
        hostname: opts.node,
        path: "/v1/node_version_info",
        method: "GET"
      });
      let ret = context.response();
      return ret.tag = ix.tag, ret.nodeVersionInfo = {
        semver: res.semver,
        commit: res.commit,
        sporkId: res.spork_id,
        protocolVersion: parseInt(res.protocol_version),
        sporkRootBlockHeight: parseInt(res.spork_root_block_height),
        nodeRootBlockHeight: parseInt(res.node_root_block_height)
      }, ret;
    }(ix, context, opts);
   default:
    return ix;
  }
};
let Action = function(Action) {
  return Action.LIST_SUBSCRIPTIONS = "list_subscriptions", Action.SUBSCRIBE = "subscribe", 
  Action.UNSUBSCRIBE = "unsubscribe", Action;
}({});
class SocketError extends Error {
  static fromMessage(error) {
    return new SocketError(error.code, error.message);
  }
  constructor(code, message) {
    super(message), this.name = "SocketError", this.code = code;
  }
}
class SubscriptionManager {
  subscribe(opts) {
    const idPromise = this._subscribe(opts);
    return {
      unsubscribe: () => {
        idPromise.then((id => id && this.unsubscribe(id)));
      }
    };
  }
  async _subscribe(opts) {
    const subscriber = this.getHandler(opts.topic).createSubscriber(opts.args, opts.onData, opts.onError);
    let sub = null;
    try {
      await this.connect(), sub = {
        id: String(this.counter++),
        topic: opts.topic,
        subscriber: subscriber
      }, this.subscriptions.push(sub);
      const response = await this.sendSubscribe(sub);
      if (response.error) throw new Error(`Failed to subscribe to topic ${sub.topic}`, {
        cause: SocketError.fromMessage(response.error)
      });
    } catch (e) {
      return subscriber.onError(e instanceof Error ? e : new Error(String(e))), sub && this.unsubscribe(sub.id), 
      null;
    }
    return sub.id;
  }
  unsubscribe(id) {
    const sub = this.subscriptions.find((sub => sub.id === id));
    sub && (this.subscriptions = this.subscriptions.filter((sub => sub.id !== id)), 
    0 !== this.subscriptions.length ? this.sendUnsubscribe(sub).catch((e => {
      console.error(`Error while unsubscribing from topic: ${e}`);
    })) : this.closeConnection?.());
  }
  async connect() {
    return this.connectPromise || (this.connectPromise = new Promise(((resolve, reject) => {
      if (1 === this.socket?.readyState) return void resolve();
      this.socket = new WebSocket(this.config.node);
      const onMessage = event => {
        const message = JSON.parse(event.data);
        if ("action" in message && message.error) {
          const sub = this.subscriptions.find((sub => sub.id === message.subscription_id));
          return void (sub && (sub.subscriber.onError(new Error(`Failed to subscribe to topic ${sub.topic}: ${message.error.message}`)), 
          this.subscriptions = this.subscriptions.filter((sub => sub.id !== message.subscription_id))));
        }
        const sub = this.subscriptions.find((sub => sub.id === message.subscription_id));
        sub && ("action" in message || message.subscription_id !== sub.id || sub.subscriber.onData(message.payload));
      }, onClose = () => {
        this.handleSocketError(new Error("WebSocket closed")).then((() => {
          resolve();
        })).catch((e => {
          reject(e);
        }));
      }, onOpen = () => {
        resolve();
      };
      this.socket.addEventListener("message", onMessage), this.socket.addEventListener("close", onClose), 
      this.socket.addEventListener("open", onOpen), this.closeConnection = () => {
        this.socket?.removeEventListener("message", onMessage), this.socket?.removeEventListener("close", onClose), 
        this.socket?.removeEventListener("open", onOpen), this.socket?.close(), this.socket = null, 
        this.closeConnection = null, this.connectPromise = null;
      };
    }))), this.connectPromise;
  }
  async handleSocketError(error) {
    if (this.closeConnection?.(), ++this.reconnectAttempts >= this.config.reconnectOptions.reconnectAttempts) throw log({
      level: LEVELS.error,
      title: "WebSocket Error",
      message: `Failed to reconnect to the server after ${this.reconnectAttempts + 1} attempts: ${error}`
    }), this.subscriptions.forEach((sub => {
      sub.subscriber.onError(new Error(`Failed to reconnect to the server after ${this.reconnectAttempts + 1} attempts: ${error}`));
    })), this.subscriptions = [], this.reconnectAttempts = 0, error;
    log({
      level: LEVELS.warn,
      title: "WebSocket Error",
      message: `WebSocket error, reconnecting in ${this.backoffInterval}ms: ${error}`
    }), await new Promise((resolve => setTimeout(resolve, this.backoffInterval))), await this.connect(), 
    await Promise.all(this.subscriptions.map((async sub => {
      await this.sendSubscribe(sub).catch((e => {
        sub.subscriber.onError(new Error(`Failed to restore subscription: ${e}`)), this.subscriptions = this.subscriptions.filter((s => s.id !== sub.id));
      }));
    }))), this.reconnectAttempts = 0;
  }
  async sendSubscribe(sub) {
    const request = {
      action: Action.SUBSCRIBE,
      topic: sub.topic,
      arguments: sub.subscriber.getConnectionArgs(),
      subscription_id: String(sub.id)
    }, response = await this.request(request);
    if (response.error) throw new Error(`Failed to subscribe to topic ${sub.topic}`, {
      cause: SocketError.fromMessage(response.error)
    });
    return response;
  }
  async sendUnsubscribe(sub) {
    const request = {
      action: Action.UNSUBSCRIBE,
      subscription_id: sub.id
    };
    this.socket?.send(JSON.stringify(request));
    const response = await this.request(request);
    if (response.error) throw new Error(`Failed to unsubscribe from topic ${sub.topic}`, {
      cause: SocketError.fromMessage(response.error)
    });
    return response;
  }
  async request(request) {
    let cleanup = () => {};
    return await new Promise(((resolve, reject) => {
      function onError(e) {
        reject(new Error(`WebSocket error: ${e}`));
      }
      function onClose() {
        reject(new Error("WebSocket closed"));
      }
      function onMessage(event) {
        const data = JSON.parse(event.data);
        data.subscription_id === request.subscription_id && resolve(data);
      }
      this.socket ? (cleanup = () => {
        this.socket?.removeEventListener("error", onError), this.socket?.removeEventListener("message", onMessage), 
        this.socket?.removeEventListener("close", onClose);
      }, this.socket.addEventListener("error", onError), this.socket.addEventListener("message", onMessage), 
      this.socket.addEventListener("close", onClose), this.socket.send(JSON.stringify(request))) : reject(new Error("WebSocket is not connected"));
    })).finally((() => {
      cleanup();
    }));
  }
  getHandler(topic) {
    const handler = this.handlers.find((handler => handler.topic === topic));
    if (!handler) throw new Error(`No handler found for topic ${topic}`);
    return handler;
  }
  get backoffInterval() {
    return Math.min(this.config.reconnectOptions.maxReconnectDelay, this.config.reconnectOptions.initialReconnectDelay * 2 ** this.reconnectAttempts);
  }
  constructor(handlers, config) {
    this.counter = 0, this.socket = null, this.subscriptions = [], this.reconnectAttempts = 0, 
    this.connectPromise = null, this.closeConnection = null, this.config = {
      ...config,
      reconnectOptions: {
        initialReconnectDelay: 500,
        reconnectAttempts: 5,
        maxReconnectDelay: 5e3,
        ...config.reconnectOptions
      }
    }, this.handlers = handlers;
  }
}
const blocksHandler = {
  topic: SubscriptionTopic.BLOCKS,
  createSubscriber: (initialArgs, onData, onError) => {
    let resumeArgs = {
      ...initialArgs
    };
    return {
      onData(data) {
        const parsedData = {
          block: {
            id: data.header.id,
            parentId: data.header.parent_id,
            height: Number(data.header.height),
            timestamp: data.header.timestamp,
            parentVoterSignature: data.header.parent_voter_signature,
            collectionGuarantees: data.payload.collection_guarantees.map((guarantee => ({
              collectionId: guarantee.collection_id,
              signerIds: guarantee.signer_indices
            }))),
            blockSeals: data.payload.block_seals.map((seal => ({
              blockId: seal.block_id,
              executionReceiptId: seal.result_id
            })))
          }
        };
        resumeArgs = {
          blockStatus: resumeArgs.blockStatus,
          startBlockHeight: Number(BigInt(data.header.height) + BigInt(1))
        }, onData(parsedData);
      },
      onError(error) {
        onError(error);
      },
      getConnectionArgs() {
        let encodedArgs = {
          block_status: resumeArgs.blockStatus
        };
        return "startBlockHeight" in resumeArgs && resumeArgs.startBlockHeight ? {
          ...encodedArgs,
          start_block_height: String(resumeArgs.startBlockHeight)
        } : "startBlockId" in resumeArgs && resumeArgs.startBlockId ? {
          ...encodedArgs,
          start_block_id: resumeArgs.startBlockId
        } : encodedArgs;
      }
    };
  }
}, blockHeadersHandler = {
  topic: SubscriptionTopic.BLOCK_HEADERS,
  createSubscriber: (initialArgs, onData, onError) => {
    let resumeArgs = {
      ...initialArgs
    };
    return {
      onData(data) {
        const parsedData = {
          blockHeader: {
            id: data.id,
            parentId: data.parent_id,
            height: Number(data.height),
            timestamp: data.timestamp,
            parentVoterSignature: data.parent_voter_signature
          }
        };
        resumeArgs = {
          blockStatus: resumeArgs.blockStatus,
          startBlockHeight: Number(BigInt(data.height) + BigInt(1))
        }, onData(parsedData);
      },
      onError(error) {
        onError(error);
      },
      getConnectionArgs() {
        let encodedArgs = {
          block_status: resumeArgs.blockStatus
        };
        return "startBlockHeight" in resumeArgs && resumeArgs.startBlockHeight ? {
          ...encodedArgs,
          start_block_height: resumeArgs.startBlockHeight
        } : "startBlockId" in resumeArgs && resumeArgs.startBlockId ? {
          ...encodedArgs,
          start_block_id: resumeArgs.startBlockId
        } : encodedArgs;
      }
    };
  }
}, blockDigestsHandler = {
  topic: SubscriptionTopic.BLOCK_DIGESTS,
  createSubscriber: (initialArgs, onData, onError) => {
    let resumeArgs = {
      ...initialArgs
    };
    return {
      onData(data) {
        const parsedData = {
          blockDigest: {
            id: data.block_id,
            height: Number(data.height),
            timestamp: data.timestamp
          }
        };
        resumeArgs = {
          blockStatus: resumeArgs.blockStatus,
          startBlockId: String(BigInt(data.height) + BigInt(1))
        }, onData(parsedData);
      },
      onError(error) {
        onError(error);
      },
      getConnectionArgs() {
        let encodedArgs = {
          block_status: resumeArgs.blockStatus
        };
        return "startBlockHeight" in resumeArgs && resumeArgs.startBlockHeight ? {
          ...encodedArgs,
          start_block_height: resumeArgs.startBlockHeight
        } : "startBlockId" in resumeArgs && resumeArgs.startBlockId ? {
          ...encodedArgs,
          start_block_id: resumeArgs.startBlockId
        } : encodedArgs;
      }
    };
  }
}, accountStatusesHandler = {
  topic: SubscriptionTopic.ACCOUNT_STATUSES,
  createSubscriber: (initialArgs, onData, onError) => {
    let resumeArgs = {
      ...initialArgs
    };
    return {
      onData(rawData) {
        const data = [];
        for (const [address, events] of Object.entries(rawData.account_events)) {
          for (const event of events) {
            const parsedData = {
              accountStatusEvent: {
                accountAddress: address,
                blockId: rawData.block_id,
                blockHeight: Number(rawData.height),
                type: event.type,
                transactionId: event.transaction_id,
                transactionIndex: Number(event.transaction_index),
                eventIndex: Number(event.event_index),
                payload: JSON.parse(Buffer.from(event.payload, "base64").toString())
              }
            };
            data.push(parsedData);
          }
          data.sort(((a, b) => {
            const txIndexDiff = a.accountStatusEvent.transactionIndex - b.accountStatusEvent.transactionIndex;
            return 0 !== txIndexDiff ? txIndexDiff : a.accountStatusEvent.eventIndex - b.accountStatusEvent.eventIndex;
          }));
          for (const message of data) onData(message);
          resumeArgs = {
            ...resumeArgs,
            startBlockHeight: Number(BigInt(rawData.height) + BigInt(1)),
            startBlockId: void 0
          };
        }
      },
      onError(error) {
        onError(error);
      },
      getConnectionArgs() {
        let encodedArgs = {
          event_types: resumeArgs.eventTypes,
          addresses: resumeArgs.addresses,
          account_addresses: resumeArgs.accountAddresses
        };
        return "startBlockHeight" in resumeArgs && resumeArgs.startBlockHeight ? {
          ...encodedArgs,
          start_block_height: resumeArgs.startBlockHeight
        } : "startBlockId" in resumeArgs && resumeArgs.startBlockId ? {
          ...encodedArgs,
          start_block_id: resumeArgs.startBlockId
        } : encodedArgs;
      }
    };
  }
}, STATUS_MAP = {
  UNKNOWN: 0,
  PENDING: 1,
  FINALIZED: 2,
  EXECUTED: 3,
  SEALED: 4,
  EXPIRED: 5
}, SUBSCRIPTION_HANDLERS = [ blocksHandler, blockHeadersHandler, blockDigestsHandler, accountStatusesHandler, {
  topic: SubscriptionTopic.TRANSACTION_STATUSES,
  createSubscriber: (initialArgs, onData, onError) => {
    let resumeArgs = {
      ...initialArgs
    };
    return {
      onData(data) {
        const parsedData = {
          transactionStatus: {
            blockId: data.transaction_result.block_id,
            status: STATUS_MAP[data.transaction_result.status.toUpperCase()],
            statusString: data.transaction_result.status.toUpperCase(),
            statusCode: data.transaction_result.status_code,
            errorMessage: data.transaction_result.error_message,
            events: data.transaction_result.events.map((event => ({
              type: event.type,
              transactionId: event.transaction_id,
              transactionIndex: Number(event.transaction_index),
              eventIndex: Number(event.event_index),
              payload: JSON.parse(Buffer$1.from(event.payload, "base64").toString())
            })))
          }
        };
        onData(parsedData);
      },
      onError(error) {
        onError(error);
      },
      getConnectionArgs: () => ({
        tx_id: resumeArgs.transactionId
      })
    };
  }
}, {
  topic: SubscriptionTopic.EVENTS,
  createSubscriber: (initialArgs, onData, onError) => {
    let resumeArgs = {
      ...initialArgs
    };
    return {
      onData(rawData) {
        for (const event of rawData.events) {
          const result = {
            event: {
              blockId: rawData.block_id,
              blockHeight: Number(rawData.block_height),
              blockTimestamp: rawData.block_timestamp,
              type: event.type,
              transactionId: event.transaction_id,
              transactionIndex: Number(event.transaction_index),
              eventIndex: Number(event.event_index),
              payload: JSON.parse(Buffer.from(event.payload, "base64").toString())
            }
          };
          onData(result);
        }
        resumeArgs = {
          ...resumeArgs,
          startHeight: Number(BigInt(rawData.block_height) + BigInt(1)),
          startBlockId: void 0
        };
      },
      onError(error) {
        onError(error);
      },
      getConnectionArgs() {
        let encodedArgs = {
          event_types: resumeArgs.eventTypes,
          addresses: resumeArgs.addresses,
          contracts: resumeArgs.contracts
        };
        return "startBlockHeight" in resumeArgs && resumeArgs.startBlockHeight ? {
          ...encodedArgs,
          start_block_height: resumeArgs.startBlockHeight
        } : "startBlockId" in resumeArgs && resumeArgs.startBlockId ? {
          ...encodedArgs,
          start_block_id: resumeArgs.startBlockId
        } : encodedArgs;
      }
    };
  }
} ];
let subscriptionManagerMap = new Map;
const httpTransport = {
  send: send$1,
  subscribe: function(_ref, opts) {
    let {topic: topic, args: args, onData: onData, onError: onError} = _ref;
    const node = function(node) {
      const url = new URL(combineURLs(node, "/v1/ws"));
      "https:" === url.protocol ? url.protocol = "wss:" : "http:" === url.protocol && (url.protocol = "ws:");
      return url.toString();
    }(opts.node), manager = subscriptionManagerMap.get(node) || new SubscriptionManager(SUBSCRIPTION_HANDLERS, {
      node: node
    });
    return subscriptionManagerMap.set(node, manager), manager.subscribe({
      topic: topic,
      args: args,
      onData: onData,
      onError: onError
    });
  }
}, ACCT = `{\n  "kind":"${InteractionResolverKind.ACCOUNT}",\n  "tempId":null,\n  "addr":null,\n  "keyId":null,\n  "sequenceNum":null,\n  "signature":null,\n  "signingFunction":null,\n  "resolve":null,\n  "role": {\n    "proposer":false,\n    "authorizer":false,\n    "payer":false,\n    "param":false\n  }\n}`, ARG = `{\n  "kind":"${InteractionResolverKind.ARGUMENT}",\n  "tempId":null,\n  "value":null,\n  "asArgument":null,\n  "xform":null,\n  "resolve": null,\n  "resolveArgument": null\n}`, IX = `{\n  "tag":"${InteractionTag.UNKNOWN}",\n  "assigns":{},\n  "status":"${InteractionStatus.OK}",\n  "reason":null,\n  "accounts":{},\n  "params":{},\n  "arguments":{},\n  "message": {\n    "cadence":null,\n    "refBlock":null,\n    "computeLimit":null,\n    "proposer":null,\n    "payer":null,\n    "authorizations":[],\n    "params":[],\n    "arguments":[]\n  },\n  "proposer":null,\n  "authorizations":[],\n  "payer":[],\n  "events": {\n    "eventType":null,\n    "start":null,\n    "end":null,\n    "blockIds":[]\n  },\n  "subscribeEvents": {\n    "startBlockId":null,\n    "startHeight":null,\n    "eventTypes":null,\n    "addresses":null,\n    "contracts":null,\n    "heartbeatInterval":null\n  },\n  "transaction": {\n    "id":null\n  },\n  "block": {\n    "id":null,\n    "height":null,\n    "isSealed":null\n  },\n  "account": {\n    "addr":null\n  },\n  "collection": {\n    "id":null\n  }\n}`, KEYS = new Set(Object.keys(JSON.parse(IX))), initInteraction = () => JSON.parse(IX), isNumber$1 = d => "number" == typeof d, isArray$1 = d => Array.isArray(d), isObj = d => null !== d && "object" == typeof d, isNull = d => null == d, isFn$3 = d => "function" == typeof d, isInteraction = ix => {
  if (!isObj(ix) || isNull(ix) || isNumber$1(ix)) return !1;
  for (let key of KEYS) if (!ix.hasOwnProperty(key)) return !1;
  return !0;
}, Ok = ix => (ix.status = InteractionStatus.OK, ix), Bad = (ix, reason) => (ix.status = InteractionStatus.BAD, 
ix.reason = reason, ix), makeIx = wat => ix => (ix.tag = wat, Ok(ix)), prepAccountKeyId = acct => null == acct.keyId ? acct : (invariant$1(!isNaN(parseInt(acct.keyId.toString())), "account.keyId must be an integer"), 
{
  ...acct,
  keyId: parseInt(acct.keyId.toString())
}), initAccount = () => JSON.parse(ACCT), makeUnknown = makeIx(InteractionTag.UNKNOWN), makeScript = makeIx(InteractionTag.SCRIPT), makeTransaction = makeIx(InteractionTag.TRANSACTION), makeGetTransactionStatus = makeIx(InteractionTag.GET_TRANSACTION_STATUS), makeGetTransaction = makeIx(InteractionTag.GET_TRANSACTION), makeGetAccount = makeIx(InteractionTag.GET_ACCOUNT), makeGetEvents = makeIx(InteractionTag.GET_EVENTS), makePing = makeIx(InteractionTag.PING), makeGetBlock = makeIx(InteractionTag.GET_BLOCK), makeGetBlockHeader = makeIx(InteractionTag.GET_BLOCK_HEADER), makeGetCollection = makeIx(InteractionTag.GET_COLLECTION), makeGetNetworkParameters = makeIx(InteractionTag.GET_NETWORK_PARAMETERS), makeSubscribeEvents = makeIx(InteractionTag.SUBSCRIBE_EVENTS), makeGetNodeVerionInfo = makeIx(InteractionTag.GET_NODE_VERSION_INFO), is = wat => ix => ix.tag === wat, isUnknown = is(InteractionTag.UNKNOWN), isScript = is(InteractionTag.SCRIPT), isTransaction = is(InteractionTag.TRANSACTION), isGetTransactionStatus = is(InteractionTag.GET_TRANSACTION_STATUS), isGetTransaction = is(InteractionTag.GET_TRANSACTION), isGetAccount = is(InteractionTag.GET_ACCOUNT), isGetEvents = is(InteractionTag.GET_EVENTS), isPing = is(InteractionTag.PING), isGetBlock = is(InteractionTag.GET_BLOCK), isGetBlockHeader = is(InteractionTag.GET_BLOCK_HEADER), isGetCollection = is(InteractionTag.GET_COLLECTION), isGetNetworkParameters = is(InteractionTag.GET_NETWORK_PARAMETERS), isGetNodeVersionInfo = is(InteractionTag.GET_NODE_VERSION_INFO), isSubscribeEvents = is(InteractionTag.SUBSCRIBE_EVENTS), isBad = ix => ix.status === InteractionStatus.BAD, recPipe = async function(ix) {
  let fns = arguments.length > 1 && void 0 !== arguments[1] ? arguments[1] : [];
  try {
    if (ix = (ix => {
      for (let key of Object.keys(ix)) if (!KEYS.has(key)) throw new Error(`"${key}" is an invalid root level Interaction property.`);
      return ix;
    })(await ix), isBad(ix)) throw new Error(`Interaction Error: ${ix.reason}`);
    if (!fns.length) return ix;
    const [hd, ...rest] = fns, cur = await hd;
    if (isFn$3(cur)) return recPipe(cur(ix), rest);
    if (isNull(cur) || !cur) return recPipe(ix, rest);
    if (isInteraction(cur)) return recPipe(cur, rest);
    throw new Error("Invalid Interaction Composition");
  } catch (e) {
    throw e;
  }
};
function pipe() {
  for (var _len2 = arguments.length, args = new Array(_len2), _key2 = 0; _key2 < _len2; _key2++) args[_key2] = arguments[_key2];
  const [arg1, arg2] = args;
  if (isArray$1(arg1)) return d => pipe(d, arg1);
  return recPipe(arg1, arg2);
}
const identity$1 = function(v) {
  return v;
}, get = function(ix, key) {
  let fallback = arguments.length > 2 && void 0 !== arguments[2] ? arguments[2] : void 0;
  return null == ix.assigns[key] ? fallback : ix.assigns[key];
};
function build() {
  let fns = arguments.length > 0 && void 0 !== arguments[0] ? arguments[0] : [];
  return pipe(initInteraction(), fns);
}
ixModule = Object.freeze({
  __proto__: null,
  Bad: Bad,
  Ok: Ok,
  destroy: key => ix => (delete ix.assigns[key], Ok(ix)),
  get: get,
  initAccount: initAccount,
  initInteraction: initInteraction,
  interaction: () => (log.deprecate({
    pkg: "FCL/SDK",
    message: "The interaction been deprecated from the Flow JS-SDK/FCL. use initInteraction instead",
    transition: "https://github.com/onflow/flow-js-sdk/blob/master/packages/sdk/TRANSITIONS.md#0010-deprecate-interaction",
    level: LEVELS.warn
  }), initInteraction()),
  isAccount: account => account.kind === InteractionResolverKind.ACCOUNT,
  isArgument: argument => argument.kind === InteractionResolverKind.ARGUMENT,
  isArray: isArray$1,
  isBad: isBad,
  isFn: isFn$3,
  isGetAccount: isGetAccount,
  isGetBlock: isGetBlock,
  isGetBlockHeader: isGetBlockHeader,
  isGetCollection: isGetCollection,
  isGetEvents: isGetEvents,
  isGetNetworkParameters: isGetNetworkParameters,
  isGetNodeVersionInfo: isGetNodeVersionInfo,
  isGetTransaction: isGetTransaction,
  isGetTransactionStatus: isGetTransactionStatus,
  isInteraction: isInteraction,
  isNull: isNull,
  isNumber: isNumber$1,
  isObj: isObj,
  isOk: ix => ix.status === InteractionStatus.OK,
  isPing: isPing,
  isScript: isScript,
  isSubscribeEvents: isSubscribeEvents,
  isTransaction: isTransaction,
  isUnknown: isUnknown,
  makeArgument: arg => ix => {
    let tempId = v4();
    return ix.message.arguments.push(tempId), ix.arguments[tempId] = JSON.parse(ARG), 
    ix.arguments[tempId].tempId = tempId, ix.arguments[tempId].value = arg.value, ix.arguments[tempId].asArgument = arg.asArgument, 
    ix.arguments[tempId].xform = arg.xform, ix.arguments[tempId].resolve = arg.resolve, 
    ix.arguments[tempId].resolveArgument = isFn$3(arg.resolveArgument) ? arg.resolveArgument.bind(arg) : arg.resolveArgument, 
    Ok(ix);
  },
  makeGetAccount: makeGetAccount,
  makeGetBlock: makeGetBlock,
  makeGetBlockHeader: makeGetBlockHeader,
  makeGetCollection: makeGetCollection,
  makeGetEvents: makeGetEvents,
  makeGetNetworkParameters: makeGetNetworkParameters,
  makeGetNodeVerionInfo: makeGetNodeVerionInfo,
  makeGetTransaction: makeGetTransaction,
  makeGetTransactionStatus: makeGetTransactionStatus,
  makePing: makePing,
  makeScript: makeScript,
  makeSubscribeEvents: makeSubscribeEvents,
  makeTransaction: makeTransaction,
  makeUnknown: makeUnknown,
  pipe: pipe,
  prepAccount: function(acct) {
    let opts = arguments.length > 1 && void 0 !== arguments[1] ? arguments[1] : {};
    return ix => {
      invariant$1("function" == typeof acct || "object" == typeof acct, "prepAccount must be passed an authorization function or an account object"), 
      invariant$1(null != opts.role, "Account must have a role");
      const ACCOUNT = initAccount(), role = opts.role, tempId = v4();
      let account = {
        ...acct
      };
      acct.authorization && isFn$3(acct.authorization) && (account = {
        resolve: acct.authorization
      }), !acct.authorization && isFn$3(acct) && (account = {
        resolve: acct
      });
      const resolve = account.resolve;
      return resolve && (account.resolve = function(acct) {
        for (var _len = arguments.length, rest = new Array(_len > 1 ? _len - 1 : 0), _key = 1; _key < _len; _key++) rest[_key - 1] = arguments[_key];
        return [ resolve, prepAccountKeyId ].reduce((async (d, fn) => fn(await d, ...rest)), acct);
      }), account = prepAccountKeyId(account), ix.accounts[tempId] = {
        ...ACCOUNT,
        tempId: tempId,
        ...account,
        role: {
          ...ACCOUNT.role,
          ..."object" == typeof acct.role ? acct.role : {},
          ...role ? {
            [role]: !0
          } : {}
        }
      }, role === TransactionRole.AUTHORIZER ? ix.authorizations.push(tempId) : role === TransactionRole.PAYER ? ix.payer.push(tempId) : role && (ix[role] = tempId), 
      ix;
    };
  },
  put: (key, value) => ix => (ix.assigns[key] = value, Ok(ix)),
  update: function(key) {
    let fn = arguments.length > 1 && void 0 !== arguments[1] ? arguments[1] : identity$1;
    return ix => (ix.assigns[key] = fn(ix.assigns[key], ix), Ok(ix));
  },
  why: ix => ix.reason
});
const DEFAULT_RESPONSE = {
  tag: null,
  transaction: null,
  transactionStatus: null,
  transactionId: null,
  encodedData: null,
  events: null,
  event: null,
  accountStatusEvent: null,
  account: null,
  block: null,
  blockHeader: null,
  blockDigest: null,
  latestBlock: null,
  collection: null,
  networkParameters: null,
  streamConnection: null,
  heartbeat: null,
  nodeVersionInfo: null
}, response = () => ({
  ...DEFAULT_RESPONSE
});
function getBlock() {
  let isSealed = arguments.length > 0 && void 0 !== arguments[0] ? arguments[0] : null;
  return pipe([ makeGetBlock, ix => (ix.block.isSealed = isSealed, Ok(ix)) ]);
}
function getAccount(addr) {
  return pipe([ makeGetAccount, ix => (ix.account.addr = sansPrefix(addr), Ok(ix)) ]);
}
const decodeStream = (stream, decodeResponse, customDecoders) => {
  const newStream = new EventEmitter$1;
  let queue = function() {
    let queue = [], running = !1;
    async function run() {
      if (!running) {
        for (running = !0; queue.length > 0; ) {
          const task = queue.shift();
          await (task?.());
        }
        running = !1;
      }
    }
    return {
      push: task => {
        queue.push(task), run();
      }
    };
  }();
  function relayEvent(event) {
    stream.on(event, (message => {
      queue.push((async () => {
        newStream.emit(event, message);
      }));
    }));
  }
  return stream.on("data", (async data => {
    const topics = Object.keys(data).filter((key => null != data[key] && "tag" !== key));
    let newDataPromise = Promise.all(topics.map((async channel => {
      const partialResponse = {
        [channel]: data[channel]
      };
      return {
        channel: channel,
        message: await decodeResponse(partialResponse, customDecoders)
      };
    })));
    queue.push((async () => {
      (await newDataPromise).forEach((_ref => {
        let {channel: channel, message: message} = _ref;
        newStream.emit(channel, message);
      }));
    }));
  })), relayEvent("close"), relayEvent("error"), {
    on(channel, callback) {
      return newStream.on(channel, callback), this;
    },
    off(channel, callback) {
      return newStream.off(channel, callback), this;
    },
    close: () => {
      stream.close();
    }
  };
};
const decodeImplicit = async i => i, decodeComposite = async (composite, decoders, stack) => {
  const decoded = await composite.fields.reduce((async (acc, v) => ((acc = await acc)[v.name] = await recurseDecode(v.value, decoders, [ ...stack, v.name ]), 
  acc)), Promise.resolve({})), decoder = composite.id && decoderLookup(decoders, composite.id);
  return decoder ? await decoder(decoded) : decoded;
}, defaultDecoders = {
  UInt: decodeImplicit,
  Int: decodeImplicit,
  UInt8: decodeImplicit,
  Int8: decodeImplicit,
  UInt16: decodeImplicit,
  Int16: decodeImplicit,
  UInt32: decodeImplicit,
  Int32: decodeImplicit,
  UInt64: decodeImplicit,
  Int64: decodeImplicit,
  UInt128: decodeImplicit,
  Int128: decodeImplicit,
  UInt256: decodeImplicit,
  Int256: decodeImplicit,
  Word8: decodeImplicit,
  Word16: decodeImplicit,
  Word32: decodeImplicit,
  Word64: decodeImplicit,
  Word128: decodeImplicit,
  Word256: decodeImplicit,
  UFix64: decodeImplicit,
  Fix64: decodeImplicit,
  String: decodeImplicit,
  Character: decodeImplicit,
  Bool: decodeImplicit,
  Address: decodeImplicit,
  Void: async () => null,
  Optional: async (optional, decoders, stack) => optional ? await recurseDecode(optional, decoders, stack) : null,
  Reference: decodeImplicit,
  Array: async (array, decoders, stack) => await Promise.all(array.map((v => new Promise((async res => res(await recurseDecode(v, decoders, [ ...stack, v.type ]))))))),
  Dictionary: async (dictionary, decoders, stack) => await dictionary.reduce((async (acc, v) => ((acc = await acc)[await recurseDecode(v.key, decoders, [ ...stack, v.key ])] = await recurseDecode(v.value, decoders, [ ...stack, v.key ]), 
  acc)), Promise.resolve({})),
  Event: decodeComposite,
  Resource: decodeComposite,
  Struct: decodeComposite,
  Enum: decodeComposite,
  Type: async type => type.staticType,
  Path: decodeImplicit,
  Capability: decodeImplicit,
  InclusiveRange: async (range, decoders, stack) => {
    const keys = [ "start", "end", "step" ];
    return await Object.keys(range).reduce((async (acc, key) => (acc = await acc, keys.includes(key) && (acc[key] = await recurseDecode(range[key], decoders, [ ...stack, key ])), 
    acc)), Promise.resolve({}));
  }
}, decoderLookup = (decoders, lookup) => {
  const found = Object.keys(decoders).find((decoder => {
    if (/^\/.*\/$/.test(decoder)) {
      return new RegExp(decoder.substring(1, decoder.length - 1)).test(lookup);
    }
    return decoder === lookup;
  }));
  return lookup && found && decoders[found];
}, recurseDecode = async (decodeInstructions, decoders, stack) => {
  let decoder = decoderLookup(decoders, decodeInstructions.type);
  if (!decoder) throw new Error(`Undefined Decoder Error: ${decodeInstructions.type}@${stack.join(".")}`);
  return await decoder(decodeInstructions.value, decoders, stack);
}, decode$1 = async function(decodeInstructions) {
  let customDecoders = arguments.length > 1 && void 0 !== arguments[1] ? arguments[1] : {}, stack = arguments.length > 2 && void 0 !== arguments[2] ? arguments[2] : [];
  const filteredDecoders = Object.keys(defaultDecoders).filter((decoder => !Object.keys(customDecoders).find((customDecoder => new RegExp(customDecoder).test(decoder))))).reduce(((decoders, decoderKey) => (decoders[decoderKey] = defaultDecoders[decoderKey], 
  decoders)), customDecoders), decoders = {
    ...filteredDecoders,
    ...customDecoders
  };
  return recurseDecode(decodeInstructions, decoders, stack);
}, decodeResponse = async function(response) {
  let customDecoders = arguments.length > 1 && void 0 !== arguments[1] ? arguments[1] : {};
  if (response.encodedData) return decode$1(response.encodedData, customDecoders);
  if (response.transactionStatus) return {
    ...response.transactionStatus,
    events: await Promise.all(response.transactionStatus.events.map((async function(e) {
      return {
        type: e.type,
        transactionId: e.transactionId,
        transactionIndex: e.transactionIndex,
        eventIndex: e.eventIndex,
        data: await decode$1(e.payload, customDecoders)
      };
    })))
  };
  if (response.transaction) return response.transaction;
  if (response.events) return await Promise.all(response.events.map((async function(e) {
    return {
      blockId: e.blockId,
      blockHeight: e.blockHeight,
      blockTimestamp: e.blockTimestamp,
      type: e.type,
      transactionId: e.transactionId,
      transactionIndex: e.transactionIndex,
      eventIndex: e.eventIndex,
      data: await decode$1(e.payload, customDecoders)
    };
  })));
  if (response.event) {
    const {payload: payload, ...rest} = response.event;
    return {
      ...rest,
      data: await decode$1(payload, customDecoders)
    };
  }
  if (response.accountStatusEvent) {
    const {payload: payload, ...rest} = response.accountStatusEvent;
    return {
      ...rest,
      data: await decode$1(payload, customDecoders)
    };
  }
  if (response.account) return response.account;
  if (response.block) return response.block;
  if (response.blockHeader) return response.blockHeader;
  if (response.blockDigest) return response.blockDigest;
  if (response.latestBlock) return log.deprecate({
    pkg: "@onflow/decode",
    subject: "Operating upon data of the latestBlock field of the response object",
    transition: "https://github.com/onflow/flow-js-sdk/blob/master/packages/decode/WARNINGS.md#0001-Deprecating-latestBlock-field"
  }), response.latestBlock;
  if (response.transactionId) return response.transactionId;
  if (response.collection) return response.collection;
  if (response.networkParameters) {
    const prefixRegex = /^flow-/, rawChainId = response.networkParameters.chainId;
    let formattedChainId;
    return formattedChainId = "flow-emulator" === rawChainId ? "local" : prefixRegex.test(rawChainId) ? rawChainId.replace(prefixRegex, "") : rawChainId, 
    {
      chainId: formattedChainId
    };
  }
  return response.streamConnection ? decodeStream(response.streamConnection, decodeResponse, customDecoders) : response.heartbeat ? response.heartbeat : response.nodeVersionInfo ? response.nodeVersionInfo : null;
}, isFn$2 = v => "function" == typeof v, isString$1 = v => "string" == typeof v;
function isOldIdentifierSyntax(cadence) {
  return /\b(0x\w+)\b/g.test(cadence);
}
function isNewIdentifierSyntax(cadence) {
  return /import\s+"(\w+)"/g.test(cadence);
}
const isFn$1 = v => "function" == typeof v;
function cast(arg) {
  return invariant$1(null != typeof arg.xform, `No type specified for argument: ${arg.value}`), 
  isFn$1(arg.xform) ? arg.xform(arg.value) : isFn$1(arg.xform.asArgument) ? arg.xform.asArgument(arg.value) : void invariant$1(!1, "Invalid Argument", arg);
}
async function handleArgResolution(arg) {
  let depth = arguments.length > 1 && void 0 !== arguments[1] ? arguments[1] : 3;
  if (invariant$1(depth > 0, `Argument Resolve Recursion Limit Exceeded for Arg: ${arg.tempId}`), 
  isFn$1(arg.resolveArgument)) {
    return handleArgResolution(await arg.resolveArgument(), depth - 1);
  }
  return arg;
}
const leftPaddedHexBuffer = (value, pad) => Buffer$1.from(value.padStart(2 * pad, "0"), "hex"), TRANSACTION_DOMAIN_TAG = (value = Buffer$1.from("FLOW-V0.0-transaction").toString("hex"), 
pad = 32, Buffer$1.from(value.padEnd(2 * pad, "0"), "hex")).toString("hex");
var value, pad;
const prependTransactionDomainTag = tx => TRANSACTION_DOMAIN_TAG + tx, addressBuffer = addr => leftPaddedHexBuffer(addr, 8), argumentToString = arg => Buffer$1.from(JSON.stringify(arg), "utf8"), rlpEncode = v => encode(v).toString("hex"), preparePayload = tx => {
  return validatePayload(tx), [ (script = tx.cadence || "", Buffer$1.from(script, "utf8")), tx.arguments.map(argumentToString), (block = tx.refBlock || "", 
  leftPaddedHexBuffer(block, 32)), tx.computeLimit, addressBuffer(sansPrefix(tx.proposalKey.address || "")), tx.proposalKey.keyId, tx.proposalKey.sequenceNum, addressBuffer(sansPrefix(tx.payer)), tx.authorizers.map((authorizer => addressBuffer(sansPrefix(authorizer)))) ];
  var block, script;
}, prepareEnvelope = tx => (validateEnvelope(tx), [ preparePayload(tx), preparePayloadSignatures(tx) ]), preparePayloadSignatures = tx => {
  const signers = collectSigners(tx);
  return tx.payloadSigs?.map((sig => ({
    signerIndex: signers.get(sansPrefix(sig.address)) || "",
    keyId: sig.keyId,
    sig: sig.sig
  }))).sort(((a, b) => a.signerIndex > b.signerIndex ? 1 : a.signerIndex < b.signerIndex ? -1 : a.keyId > b.keyId ? 1 : a.keyId < b.keyId ? -1 : 0)).map((sig => {
    return [ sig.signerIndex, sig.keyId, (signature = sig.sig, Buffer$1.from(signature, "hex")) ];
    var signature;
  }));
}, collectSigners = tx => {
  const signers = new Map;
  let i = 0;
  const addSigner = addr => {
    signers.has(addr) || (signers.set(addr, i), i++);
  };
  return tx.proposalKey.address && addSigner(tx.proposalKey.address), addSigner(tx.payer), 
  tx.authorizers.forEach(addSigner), signers;
}, validatePayload = tx => {
  payloadFields.forEach((field => checkField(tx, field))), proposalKeyFields.forEach((field => checkField(tx.proposalKey, field, "proposalKey")));
}, validateEnvelope = tx => {
  payloadSigsFields.forEach((field => checkField(tx, field))), tx.payloadSigs?.forEach(((sig, index) => {
    payloadSigFields.forEach((field => checkField(sig, field, "payloadSigs", index)));
  }));
}, isNumber = v => "number" == typeof v, isString = v => "string" == typeof v, isObject = v => null !== v && "object" == typeof v, isArray = v => isObject(v) && v instanceof Array, payloadFields = [ {
  name: "cadence",
  check: isString
}, {
  name: "arguments",
  check: isArray
}, {
  name: "refBlock",
  check: isString,
  defaultVal: "0"
}, {
  name: "computeLimit",
  check: isNumber
}, {
  name: "proposalKey",
  check: isObject
}, {
  name: "payer",
  check: isString
}, {
  name: "authorizers",
  check: isArray
} ], proposalKeyFields = [ {
  name: "address",
  check: isString
}, {
  name: "keyId",
  check: isNumber
}, {
  name: "sequenceNum",
  check: isNumber
} ], payloadSigsFields = [ {
  name: "payloadSigs",
  check: isArray
} ], payloadSigFields = [ {
  name: "address",
  check: isString
}, {
  name: "keyId",
  check: isNumber
}, {
  name: "sig",
  check: isString
} ], checkField = (obj, field, base, index) => {
  const {name: name, check: check, defaultVal: defaultVal} = field;
  if (null == obj[name] && null != defaultVal && (obj[name] = defaultVal), null == obj[name]) throw missingFieldError(name, base, index);
  if (!check(obj[name])) throw invalidFieldError(name, base, index);
}, printFieldName = (field, base, index) => base ? null == index ? `${base}.${field}` : `${base}.${index}.${field}` : field, missingFieldError = (field, base, index) => new Error(`Missing field ${printFieldName(field, base, index)}`), invalidFieldError = (field, base, index) => new Error(`Invalid field ${printFieldName(field, base, index)}`);
function findInsideSigners(ix) {
  let inside = new Set(ix.authorizations);
  return ix.proposer && inside.add(ix.proposer), Array.isArray(ix.payer) ? ix.payer.forEach((p => inside.delete(p))) : inside.delete(ix.payer), 
  Array.from(inside);
}
function findOutsideSigners(ix) {
  let outside = new Set(Array.isArray(ix.payer) ? ix.payer : [ ix.payer ]);
  return Array.from(outside);
}
const createSignableVoucher = ix => {
  const proposalKey = ix.proposer ? {
    address: withPrefix$1(ix.accounts[ix.proposer].addr),
    keyId: ix.accounts[ix.proposer].keyId,
    sequenceNum: ix.accounts[ix.proposer].sequenceNum
  } : {};
  return {
    cadence: ix.message.cadence,
    refBlock: ix.message.refBlock || null,
    computeLimit: ix.message.computeLimit,
    arguments: ix.message.arguments.map((id => ix.arguments[id].asArgument)),
    proposalKey: proposalKey,
    payer: withPrefix$1(ix.accounts[Array.isArray(ix.payer) ? ix.payer[0] : ix.payer].addr),
    authorizers: ix.authorizations.map((cid => withPrefix$1(ix.accounts[cid].addr))).reduce(((prev, current) => prev.find((item => item === current)) ? prev : [ ...prev, current ]), []),
    payloadSigs: findInsideSigners(ix).map((id => ({
      address: withPrefix$1(ix.accounts[id].addr),
      keyId: ix.accounts[id].keyId,
      sig: ix.accounts[id].signature
    }))),
    envelopeSigs: findOutsideSigners(ix).map((id => ({
      address: withPrefix$1(ix.accounts[id].addr),
      keyId: ix.accounts[id].keyId,
      sig: ix.accounts[id].signature
    })))
  };
};
function recurseFlatMap(el) {
  let depthLimit = arguments.length > 1 && void 0 !== arguments[1] ? arguments[1] : 3;
  return depthLimit <= 0 ? el : Array.isArray(el) ? recurseFlatMap(el.flatMap((e => e)), depthLimit - 1) : el;
}
function addAccountToIx(ix, newAccount) {
  var acct;
  "string" != typeof newAccount.addr || "number" != typeof newAccount.keyId && "string" != typeof newAccount.keyId ? newAccount.tempId = newAccount.tempId || v4() : newAccount.tempId = `${withPrefix$1((acct = newAccount).addr)}-${acct.keyId}`;
  const existingAccount = ix.accounts[newAccount.tempId] || newAccount;
  return ix.accounts[newAccount.tempId] || (ix.accounts[newAccount.tempId] = newAccount), 
  ix.accounts[newAccount.tempId].role.proposer = existingAccount.role.proposer || newAccount.role.proposer, 
  ix.accounts[newAccount.tempId].role.payer = existingAccount.role.payer || newAccount.role.payer, 
  ix.accounts[newAccount.tempId].role.authorizer = existingAccount.role.authorizer || newAccount.role.authorizer, 
  ix.accounts[newAccount.tempId];
}
function uniqueAccountsFlatMap(accounts) {
  const flatMapped = recurseFlatMap(accounts), seen = new Set, uniqueAccountsFlatMapped = flatMapped.map((account => {
    const accountId = function() {
      for (var _len = arguments.length, ids = new Array(_len), _key = 0; _key < _len; _key++) ids[_key] = arguments[_key];
      return ids.join("-");
    }(account.tempId, account.role.payer, account.role.proposer, account.role.authorizer, account.role.param);
    return seen.has(accountId) ? null : (seen.add(accountId), account);
  })).filter((e => null !== e));
  return uniqueAccountsFlatMapped;
}
async function resolveSingleAccount(ix, currentAccountTempId) {
  let depthLimit = arguments.length > 2 && void 0 !== arguments[2] ? arguments[2] : 5, {debugLogger: debugLogger} = arguments.length > 3 ? arguments[3] : void 0;
  if (depthLimit <= 0) throw new Error("recurseResolveAccount Error: Depth limit (5) reached. Ensure your authorization functions resolve to an account after 5 resolves.");
  let account = ix.accounts[currentAccountTempId];
  if (!account) return [ [], !1 ];
  if (debugLogger(`account: ${account.tempId}`, Math.max(5 - depthLimit, 0)), account?.resolve) {
    if (v = account?.resolve, v && ("[object Function]" === Object.prototype.toString.call(v) || "function" == typeof v || v instanceof Function)) {
      debugLogger(`account: ${account.tempId} -- cache MISS`, Math.max(5 - depthLimit, 0));
      const {resolve: resolve, ...accountWithoutResolve} = account;
      let resolvedAccounts = await resolve(accountWithoutResolve, function(acct, ix) {
        try {
          return {
            f_type: "PreSignable",
            f_vsn: "1.0.1",
            roles: acct.role,
            cadence: ix.message.cadence,
            args: ix.message.arguments.map((d => ix.arguments[d].asArgument)),
            data: {},
            interaction: ix,
            voucher: createSignableVoucher(ix)
          };
        } catch (error) {
          throw console.error("buildPreSignable", error), error;
        }
      }(accountWithoutResolve, ix));
      resolvedAccounts = Array.isArray(resolvedAccounts) ? resolvedAccounts : [ resolvedAccounts ];
      let flatResolvedAccounts = recurseFlatMap(resolvedAccounts);
      return flatResolvedAccounts = flatResolvedAccounts.map((flatResolvedAccount => addAccountToIx(ix, flatResolvedAccount))), 
      account.resolve = flatResolvedAccounts.map((flatResolvedAccount => flatResolvedAccount.tempId)), 
      account = addAccountToIx(ix, account), [ flatResolvedAccounts.map((flatResolvedAccount => flatResolvedAccount.tempId)), !0 ];
    }
    return debugLogger(`account: ${account.tempId} -- cache HIT`, Math.max(5 - depthLimit, 0)), 
    [ account.resolve, !1 ];
  }
  var v;
  return [ account.tempId ? [ account.tempId ] : [], !1 ];
}
ROLES = function(ROLES) {
  return ROLES.PAYER = "payer", ROLES.PROPOSER = "proposer", ROLES.AUTHORIZATIONS = "authorizations", 
  ROLES;
}(ROLES || {});
const getAccountTempIDs = rawTempIds => null === rawTempIds ? [] : Array.isArray(rawTempIds) ? rawTempIds : [ rawTempIds ];
async function replaceRoles(ix, oldAccountTempId, newAccounts) {
  for (let role of Object.values(ROLES)) if (role === ROLES.AUTHORIZATIONS || role === ROLES.PAYER) ix[role] = getAccountTempIDs(ix[role]).reduce(((acc, acctTempId) => acctTempId === oldAccountTempId ? acc.concat(...newAccounts.filter((x => role === ROLES.PAYER && x.role.payer || role === ROLES.AUTHORIZATIONS && x.role.authorizer)).map((acct => acct.tempId))) : acc.concat(acctTempId)), []); else if (role === ROLES.PROPOSER) {
    const proposerAccts = newAccounts.filter((x => x.role.proposer));
    if (proposerAccts.length > 1) throw new Error("replaceRoles Error: Multiple proposer keys were resolved, but only one is allowed");
    ix[role] = proposerAccts[0]?.tempId ?? ix[role];
  }
}
async function resolveAccountsByIds(ix, accountTempIds) {
  let depthLimit = arguments.length > 2 && void 0 !== arguments[2] ? arguments[2] : 5, {debugLogger: debugLogger} = arguments.length > 3 ? arguments[3] : void 0;
  invariant$1(ix && "object" == typeof ix, "resolveAccountType Error: ix not defined");
  let payerAddress, newTempIds = new Set;
  for (let accountId of accountTempIds) {
    let account = ix.accounts[accountId];
    invariant$1(Boolean(account), "resolveAccountType Error: account not found");
    const [resolvedAccountTempIds, foundNewAccounts] = await resolveSingleAccount(ix, accountId, depthLimit, {
      debugLogger: debugLogger
    });
    if (foundNewAccounts) {
      const flatResolvedAccounts = uniqueAccountsFlatMap(resolvedAccountTempIds.map((resolvedAccountTempId => ix.accounts[resolvedAccountTempId])));
      flatResolvedAccounts.forEach((x => newTempIds.add(x.tempId))), replaceRoles(ix, accountId, flatResolvedAccounts);
    }
  }
  for (const payerTempID of ix[ROLES.PAYER]) {
    let pAcct = ix.accounts[payerTempID];
    if (payerAddress) {
      if (payerAddress !== pAcct.addr) throw new Error("resolveAccountType Error: payers from different accounts detected");
    } else payerAddress = pAcct.addr;
  }
  return newTempIds;
}
function fetchSignature(ix, payload) {
  return async function(id) {
    const acct = ix.accounts[id];
    if (null != acct.signature && void 0 !== acct.signature) return;
    const {signature: signature} = await acct.signingFunction(function(acct, message, ix) {
      try {
        return {
          f_type: "Signable",
          f_vsn: "1.0.1",
          message: message,
          addr: sansPrefix(acct.addr),
          keyId: acct.keyId,
          roles: acct.role,
          cadence: ix.message.cadence,
          args: ix.message.arguments.map((d => ix.arguments[d].asArgument)),
          data: {},
          interaction: ix,
          voucher: createSignableVoucher(ix)
        };
      } catch (error) {
        throw console.error("buildSignable", error), error;
      }
    }(acct, payload, ix));
    ix.accounts[id].signature = signature;
  };
}
function prepForEncoding(ix) {
  const payerAddress = sansPrefix((Array.isArray(ix.payer) ? ix.accounts[ix.payer[0]] : ix.accounts[ix.payer]).addr || ""), proposalKey = ix.proposer ? {
    address: sansPrefix(ix.accounts[ix.proposer].addr) || "",
    keyId: ix.accounts[ix.proposer].keyId || 0,
    sequenceNum: ix.accounts[ix.proposer].sequenceNum || 0
  } : {};
  return {
    cadence: ix.message.cadence,
    refBlock: ix.message.refBlock,
    computeLimit: ix.message.computeLimit,
    arguments: ix.message.arguments.map((id => ix.arguments[id].asArgument)),
    proposalKey: proposalKey,
    payer: payerAddress,
    authorizers: ix.authorizations.map((cid => sansPrefix(ix.accounts[cid].addr) || "")).reduce(((prev, current) => prev.find((item => item === current)) ? prev : [ ...prev, current ]), [])
  };
}
const noop = v => v, debug = function(key) {
  let fn = arguments.length > 1 && void 0 !== arguments[1] ? arguments[1] : noop;
  return async ix => (await config.get(`debug.${key}`) && await fn(ix, (function() {
    for (var _len = arguments.length, msg = new Array(_len), _key = 0; _key < _len; _key++) msg[_key] = arguments[_key];
    console.log(`debug[${key}] ---\n`, ...msg, "\n\n\n---");
  }), (ix => [ "\nAccounts:", {
    proposer: ix.proposer,
    authorizations: ix.authorizations,
    payer: ix.payer
  }, "\n\nDetails:", ix.accounts ].filter(Boolean))), ix);
}, resolve = pipe([ async function(ix) {
  if (!isTransaction(ix) && !isScript(ix)) return ix;
  var cadence = get(ix, "ix.cadence");
  if (invariant$1(isFn$2(cadence) || isString$1(cadence), "Cadence needs to be a function or a string."), 
  isFn$2(cadence) && (cadence = await cadence({})), invariant$1(isString$1(cadence), "Cadence needs to be a string at this point."), 
  invariant$1(!isOldIdentifierSyntax(cadence) || !isNewIdentifierSyntax(cadence), "Both account identifier and contract identifier syntax not simultaneously supported."), 
  isOldIdentifierSyntax(cadence) && (cadence = await config().where(/^0x/).then((d => Object.entries(d).reduce(((cadence, _ref) => {
    let [key, value] = _ref;
    const regex = new RegExp("(\\b" + key + "\\b)", "g");
    return cadence.replace(regex, value);
  }), cadence)))), isNewIdentifierSyntax(cadence)) for (const [fullMatch, contractName] of function(cadence) {
    return cadence.matchAll(/import\s+"(\w+)"/g);
  }(cadence)) {
    const address = await config().get(`system.contracts.${contractName}`);
    address ? cadence = cadence.replace(fullMatch, `import ${contractName} from ${withPrefix$1(address)}`) : log({
      title: "Contract Placeholder not found",
      message: `Cannot find a value for contract placeholder ${contractName}. Please add to your flow.json or explicitly add it to the config 'contracts.*' namespace.`,
      level: LEVELS.warn
    });
  }
  return ix.message.cadence = cadence, ix;
}, debug("cadence", ((ix, log) => log(ix.message.cadence))), async function(ix) {
  return isTransaction(ix) && (ix.message.computeLimit = ix.message.computeLimit || await config.get("fcl.limit"), 
  ix.message.computeLimit || (log.deprecate({
    pkg: "FCL/SDK",
    subject: "The built-in default compute limit (DEFAULT_COMPUTE_LIMIT=10)",
    transition: "https://github.com/onflow/flow-js-sdk/blob/master/packages/sdk/TRANSITIONS.md#0009-deprecate-default-compute-limit"
  }), ix.message.computeLimit = 100)), ix;
}, debug("compute limit", ((ix, log) => log(ix.message.computeLimit))), async function(ix) {
  if (isTransaction(ix) || isScript(ix)) for (let [id, arg] of Object.entries(ix.arguments)) {
    const res = await handleArgResolution(arg);
    ix.arguments[id].asArgument = cast(res);
  }
  return ix;
}, debug("arguments", ((ix, log) => log(ix.message.arguments, ix.message))), async function(ix) {
  let opts = arguments.length > 1 && void 0 !== arguments[1] ? arguments[1] : {};
  if (isTransaction(ix)) {
    Array.isArray(ix.payer) || log.deprecate({
      pkg: "FCL",
      subject: '"ix.payer" must be an array. Support for ix.payer as a singular',
      message: "See changelog for more info."
    });
    let [debugLogger, getDebugMessage] = function() {
      const DEBUG_MESSAGE = [];
      return [ function() {
        let msg = arguments.length > 0 && void 0 !== arguments[0] ? arguments[0] : "", indent = arguments.length > 1 && void 0 !== arguments[1] ? arguments[1] : 0;
        DEBUG_MESSAGE.push(Array(4 * indent).fill(" ").join("-") + msg);
      }, function() {
        return DEBUG_MESSAGE.reduce(((prev, curr) => prev + "\n" + curr));
      } ];
    }();
    try {
      let depthLimit = 5, frontier = new Set([ ...getAccountTempIDs(ix[ROLES.PAYER]), ...getAccountTempIDs(ix[ROLES.PROPOSER]), ...getAccountTempIDs(ix[ROLES.AUTHORIZATIONS]) ]);
      for (;frontier.size > 0; ) {
        if (depthLimit <= 0) throw new Error("resolveAccounts Error: Depth limit (5) reached. Ensure your authorization functions resolve to an account after 5 resolves.");
        frontier = await resolveAccountsByIds(ix, frontier, depthLimit, {
          debugLogger: debugLogger
        }), depthLimit--;
      }
      await async function(ix) {
        const payerTempIds = Array.isArray(ix.payer) ? ix.payer : [ ix.payer ], authorizersTempIds = Array.isArray(ix.authorizations) ? ix.authorizations : [ ix.authorizations ], proposerTempIds = null === ix.proposer ? [] : Array.isArray(ix.proposer) ? ix.proposer : [ ix.proposer ], ixAccountKeys = Object.keys(ix.accounts), uniqueTempIds = [ ...new Set(payerTempIds.concat(authorizersTempIds, proposerTempIds)) ];
        for (const ixAccountKey of ixAccountKeys) uniqueTempIds.find((id => id === ixAccountKey)) || delete ix.accounts[ixAccountKey];
      }(ix);
      for (const role of Object.values(ROLES)) invariant$1(getAccountTempIDs(ix[role]).length > 0 || role === ROLES.AUTHORIZATIONS, `resolveAccountType Error: no accounts for role "${role}" found`);
      opts.enableDebug && console.debug(getDebugMessage());
    } catch (error) {
      throw console.error("=== SAD PANDA ===\n\n", error, "\n\n=== SAD PANDA ==="), error;
    }
  }
  return ix;
}, debug("accounts", ((ix, log, accts) => log(...accts(ix)))), async function(ix) {
  if (isTransaction(ix) && null == ix.message.refBlock) {
    const node = await config().get("accessNode.api"), sendFn = await config.first([ "sdk.transport", "sdk.send" ], send$1);
    invariant$1(sendFn, "Required value for sdk.transport is not defined in config. See: https://github.com/onflow/fcl-js/blob/master/packages/sdk/CHANGELOG.md#0057-alpha1----2022-01-21"), 
    ix.message.refBlock = (await sendFn(build([ getBlock() ]), {
      config: config,
      response: response,
      Buffer: Buffer$1,
      ix: ixModule
    }, {
      node: node
    }).then(decodeResponse)).id;
  }
  return ix;
}, async function(ix) {
  if (isTransaction(ix)) {
    var acct = Object.values(ix.accounts).find((a => a.role.proposer));
    if (invariant$1(void 0 !== acct, "Transactions require a proposer"), acct && null == acct.sequenceNum) {
      const node = await config().get("accessNode.api"), sendFn = await config.first([ "sdk.transport", "sdk.send" ], send$1);
      invariant$1(sendFn, "Required value for sdk.transport is not defined in config. See: https://github.com/onflow/fcl-js/blob/master/packages/sdk/CHANGELOG.md#0057-alpha1----2022-01-21"), 
      ix.accounts[acct.tempId].sequenceNum = await sendFn(await build([ getAccount(acct.addr) ]), {
        config: config,
        response: response,
        Buffer: Buffer$1,
        ix: ixModule
      }, {
        node: node
      }).then(decodeResponse).then((acctResponse => acctResponse.keys)).then((keys => keys.find((key => key.index === acct.keyId)))).then((key => key.sequenceNumber));
    }
  }
  return ix;
}, async function(ix) {
  if (isTransaction(ix)) try {
    let insideSigners = findInsideSigners(ix);
    const insidePayload = (tx = prepForEncoding(ix), prependTransactionDomainTag(rlpEncode(preparePayload(tx))));
    await Promise.all(insideSigners.map(fetchSignature(ix, insidePayload)));
    let outsideSigners = findOutsideSigners(ix);
    const outsidePayload = (tx => prependTransactionDomainTag(rlpEncode(prepareEnvelope(tx))))({
      ...prepForEncoding(ix),
      payloadSigs: insideSigners.map((id => ({
        address: ix.accounts[id].addr || "",
        keyId: ix.accounts[id].keyId || 0,
        sig: ix.accounts[id].signature || ""
      })))
    });
    await Promise.all(outsideSigners.map(fetchSignature(ix, outsidePayload)));
  } catch (error) {
    throw console.error("Signatures", error, {
      ix: ix
    }), error;
  }
  var tx;
  return ix;
}, debug("signatures", ((ix, log, accts) => log(...accts(ix)))), async function(ix) {
  for (let key of Object.keys(ix.accounts)) ix.accounts[key].addr = sansPrefix(ix.accounts[key].addr);
  return ix;
}, async function(ix) {
  return pipe(ix, get(ix, "ix.validators", []).map((cb => ix => cb(ix, {
    Ok: Ok,
    Bad: Bad
  }))));
}, async function(ix) {
  const fn = get(ix, "ix.voucher-intercept");
  return isFn$3(fn) && await fn(createSignableVoucher(ix)), ix;
}, debug("resolved", ((ix, log) => log(ix))) ]);
function invariant() {
  for (var _len = arguments.length, args = new Array(_len), _key = 0; _key < _len; _key++) args[_key] = arguments[_key];
  if (args.length > 1) {
    const [predicate, message] = args;
    return invariant(((ix, _ref) => {
      let {Ok: Ok, Bad: Bad} = _ref;
      return predicate ? Ok(ix) : Bad(ix, message);
    }));
  }
  const [fn] = args;
  return ix => fn(ix, {
    Ok: Ok,
    Bad: Bad
  });
}
class SubscriptionsNotSupportedError extends Error {
  constructor() {
    super("The current transport does not support subscriptions.  If you have provided a custom transport (e.g. via `sdk.transport` configuration), ensure that it implements the subscribe method."), 
    this.name = "SubscriptionsNotSupportedError";
  }
}
async function getTransport() {
  let override = arguments.length > 0 && void 0 !== arguments[0] ? arguments[0] : {};
  invariant$1(null == override.send || null == override.transport, 'SDK Transport Error: Cannot provide both "transport" and legacy "send" options.');
  const transportOrSend = override.transport || override.send || await config().first([ "sdk.transport", "sdk.send" ], httpTransport);
  return void 0 === (transport = transportOrSend).send || void 0 === transport.subscribe || "function" != typeof transport.send || "function" != typeof transport.subscribe ? {
    send: transportOrSend,
    subscribe: () => {
      throw new SubscriptionsNotSupportedError;
    }
  } : transportOrSend;
  var transport;
}
const send = async function() {
  let args = arguments.length > 0 && void 0 !== arguments[0] ? arguments[0] : [], opts = arguments.length > 1 && void 0 !== arguments[1] ? arguments[1] : {};
  const transport = await getTransport(opts), sendFn = transport.send.bind(transport);
  invariant(sendFn, "Required value for sdk.transport is not defined in config. See: https://github.com/onflow/fcl-js/blob/master/packages/sdk/CHANGELOG.md#0057-alpha1----2022-01-21");
  const resolveFn = await config.first([ "sdk.resolve" ], opts.resolve || resolve);
  return opts.node = opts.node || await config().get("accessNode.api"), Array.isArray(args) && (args = pipe(initInteraction(), args)), 
  sendFn(await resolveFn(args), {
    config: config,
    response: response,
    ix: ixModule,
    Buffer: Buffer$1
  }, opts);
};
async function decode(response) {
  const decodersFromConfig = await config().where(/^decoder\./), decoders = Object.entries(decodersFromConfig).map((_ref => {
    let [pattern, xform] = _ref;
    return pattern = `/${pattern.replace(/^decoder\./, "")}$/`, [ pattern, xform ];
  }));
  return decodeResponse(response, Object.fromEntries(decoders));
}
function getTransactionStatus(transactionId) {
  return pipe([ makeGetTransactionStatus, ix => (ix.transaction.id = transactionId, 
  Ok(ix)) ]);
}
function limit(limit) {
  return ix => (ix.message.computeLimit = limit, ix);
}
function getDefaultExportFromNamespaceIfPresent(n) {
  return n && Object.prototype.hasOwnProperty.call(n, "default") ? n.default : n;
}
function requireEvent_pb() {
  return hasRequiredEvent_pb || (hasRequiredEvent_pb = 1, function(exports) {
    var jspb = require$$0, goog = jspb, global = "undefined" != typeof globalThis && globalThis || "undefined" != typeof window && window || void 0 !== global && global || "undefined" != typeof self && self || function() {
      return this;
    }.call(null) || Function("return this")();
    goog.exportSymbol("proto.flow.entities.Event", null, global), goog.exportSymbol("proto.flow.entities.EventEncodingVersion", null, global), 
    proto.flow.entities.Event = function(opt_data) {
      jspb.Message.initialize(this, opt_data, 0, -1, null, null);
    }, goog.inherits(proto.flow.entities.Event, jspb.Message), goog.DEBUG && !COMPILED && (proto.flow.entities.Event.displayName = "proto.flow.entities.Event"), 
    jspb.Message.GENERATE_TO_OBJECT && (proto.flow.entities.Event.prototype.toObject = function(opt_includeInstance) {
      return proto.flow.entities.Event.toObject(opt_includeInstance, this);
    }, proto.flow.entities.Event.toObject = function(includeInstance, msg) {
      var obj = {
        type: jspb.Message.getFieldWithDefault(msg, 1, ""),
        transactionId: msg.getTransactionId_asB64(),
        transactionIndex: jspb.Message.getFieldWithDefault(msg, 3, 0),
        eventIndex: jspb.Message.getFieldWithDefault(msg, 4, 0),
        payload: msg.getPayload_asB64()
      };
      return includeInstance && (obj.$jspbMessageInstance = msg), obj;
    }), proto.flow.entities.Event.deserializeBinary = function(bytes) {
      var reader = new jspb.BinaryReader(bytes), msg = new proto.flow.entities.Event;
      return proto.flow.entities.Event.deserializeBinaryFromReader(msg, reader);
    }, proto.flow.entities.Event.deserializeBinaryFromReader = function(msg, reader) {
      for (var value; reader.nextField() && !reader.isEndGroup(); ) switch (reader.getFieldNumber()) {
       case 1:
        value = reader.readString(), msg.setType(value);
        break;
       case 2:
        value = reader.readBytes(), msg.setTransactionId(value);
        break;
       case 3:
        value = reader.readUint32(), msg.setTransactionIndex(value);
        break;
       case 4:
        value = reader.readUint32(), msg.setEventIndex(value);
        break;
       case 5:
        value = reader.readBytes(), msg.setPayload(value);
        break;
       default:
        reader.skipField();
      }
      return msg;
    }, proto.flow.entities.Event.prototype.serializeBinary = function() {
      var writer = new jspb.BinaryWriter;
      return proto.flow.entities.Event.serializeBinaryToWriter(this, writer), writer.getResultBuffer();
    }, proto.flow.entities.Event.serializeBinaryToWriter = function(message, writer) {
      var f = void 0;
      (f = message.getType()).length > 0 && writer.writeString(1, f), (f = message.getTransactionId_asU8()).length > 0 && writer.writeBytes(2, f), 
      0 !== (f = message.getTransactionIndex()) && writer.writeUint32(3, f), 0 !== (f = message.getEventIndex()) && writer.writeUint32(4, f), 
      (f = message.getPayload_asU8()).length > 0 && writer.writeBytes(5, f);
    }, proto.flow.entities.Event.prototype.getType = function() {
      return jspb.Message.getFieldWithDefault(this, 1, "");
    }, proto.flow.entities.Event.prototype.setType = function(value) {
      return jspb.Message.setProto3StringField(this, 1, value);
    }, proto.flow.entities.Event.prototype.getTransactionId = function() {
      return jspb.Message.getFieldWithDefault(this, 2, "");
    }, proto.flow.entities.Event.prototype.getTransactionId_asB64 = function() {
      return jspb.Message.bytesAsB64(this.getTransactionId());
    }, proto.flow.entities.Event.prototype.getTransactionId_asU8 = function() {
      return jspb.Message.bytesAsU8(this.getTransactionId());
    }, proto.flow.entities.Event.prototype.setTransactionId = function(value) {
      return jspb.Message.setProto3BytesField(this, 2, value);
    }, proto.flow.entities.Event.prototype.getTransactionIndex = function() {
      return jspb.Message.getFieldWithDefault(this, 3, 0);
    }, proto.flow.entities.Event.prototype.setTransactionIndex = function(value) {
      return jspb.Message.setProto3IntField(this, 3, value);
    }, proto.flow.entities.Event.prototype.getEventIndex = function() {
      return jspb.Message.getFieldWithDefault(this, 4, 0);
    }, proto.flow.entities.Event.prototype.setEventIndex = function(value) {
      return jspb.Message.setProto3IntField(this, 4, value);
    }, proto.flow.entities.Event.prototype.getPayload = function() {
      return jspb.Message.getFieldWithDefault(this, 5, "");
    }, proto.flow.entities.Event.prototype.getPayload_asB64 = function() {
      return jspb.Message.bytesAsB64(this.getPayload());
    }, proto.flow.entities.Event.prototype.getPayload_asU8 = function() {
      return jspb.Message.bytesAsU8(this.getPayload());
    }, proto.flow.entities.Event.prototype.setPayload = function(value) {
      return jspb.Message.setProto3BytesField(this, 5, value);
    }, proto.flow.entities.EventEncodingVersion = {
      JSON_CDC_V0: 0,
      CCF_V0: 1
    }, goog.object.extend(exports, proto.flow.entities);
  }(event_pb)), event_pb;
}
function requireTransaction_pb() {
  return hasRequiredTransaction_pb || (hasRequiredTransaction_pb = 1, function(exports) {
    var jspb = require$$0, goog = jspb, global = "undefined" != typeof globalThis && globalThis || "undefined" != typeof window && window || void 0 !== global && global || "undefined" != typeof self && self || function() {
      return this;
    }.call(null) || Function("return this")(), flow_entities_event_pb = requireEvent_pb();
    goog.object.extend(proto, flow_entities_event_pb), goog.exportSymbol("proto.flow.entities.Transaction", null, global), 
    goog.exportSymbol("proto.flow.entities.Transaction.ProposalKey", null, global), 
    goog.exportSymbol("proto.flow.entities.Transaction.Signature", null, global), goog.exportSymbol("proto.flow.entities.TransactionStatus", null, global), 
    proto.flow.entities.Transaction = function(opt_data) {
      jspb.Message.initialize(this, opt_data, 0, -1, proto.flow.entities.Transaction.repeatedFields_, null);
    }, goog.inherits(proto.flow.entities.Transaction, jspb.Message), goog.DEBUG && !COMPILED && (proto.flow.entities.Transaction.displayName = "proto.flow.entities.Transaction"), 
    proto.flow.entities.Transaction.ProposalKey = function(opt_data) {
      jspb.Message.initialize(this, opt_data, 0, -1, null, null);
    }, goog.inherits(proto.flow.entities.Transaction.ProposalKey, jspb.Message), goog.DEBUG && !COMPILED && (proto.flow.entities.Transaction.ProposalKey.displayName = "proto.flow.entities.Transaction.ProposalKey"), 
    proto.flow.entities.Transaction.Signature = function(opt_data) {
      jspb.Message.initialize(this, opt_data, 0, -1, null, null);
    }, goog.inherits(proto.flow.entities.Transaction.Signature, jspb.Message), goog.DEBUG && !COMPILED && (proto.flow.entities.Transaction.Signature.displayName = "proto.flow.entities.Transaction.Signature"), 
    proto.flow.entities.Transaction.repeatedFields_ = [ 2, 7, 8, 9 ], jspb.Message.GENERATE_TO_OBJECT && (proto.flow.entities.Transaction.prototype.toObject = function(opt_includeInstance) {
      return proto.flow.entities.Transaction.toObject(opt_includeInstance, this);
    }, proto.flow.entities.Transaction.toObject = function(includeInstance, msg) {
      var f, obj = {
        script: msg.getScript_asB64(),
        argumentsList: msg.getArgumentsList_asB64(),
        referenceBlockId: msg.getReferenceBlockId_asB64(),
        gasLimit: jspb.Message.getFieldWithDefault(msg, 4, 0),
        proposalKey: (f = msg.getProposalKey()) && proto.flow.entities.Transaction.ProposalKey.toObject(includeInstance, f),
        payer: msg.getPayer_asB64(),
        authorizersList: msg.getAuthorizersList_asB64(),
        payloadSignaturesList: jspb.Message.toObjectList(msg.getPayloadSignaturesList(), proto.flow.entities.Transaction.Signature.toObject, includeInstance),
        envelopeSignaturesList: jspb.Message.toObjectList(msg.getEnvelopeSignaturesList(), proto.flow.entities.Transaction.Signature.toObject, includeInstance)
      };
      return includeInstance && (obj.$jspbMessageInstance = msg), obj;
    }), proto.flow.entities.Transaction.deserializeBinary = function(bytes) {
      var reader = new jspb.BinaryReader(bytes), msg = new proto.flow.entities.Transaction;
      return proto.flow.entities.Transaction.deserializeBinaryFromReader(msg, reader);
    }, proto.flow.entities.Transaction.deserializeBinaryFromReader = function(msg, reader) {
      for (var value; reader.nextField() && !reader.isEndGroup(); ) switch (reader.getFieldNumber()) {
       case 1:
        value = reader.readBytes(), msg.setScript(value);
        break;
       case 2:
        value = reader.readBytes(), msg.addArguments(value);
        break;
       case 3:
        value = reader.readBytes(), msg.setReferenceBlockId(value);
        break;
       case 4:
        value = reader.readUint64(), msg.setGasLimit(value);
        break;
       case 5:
        value = new proto.flow.entities.Transaction.ProposalKey, reader.readMessage(value, proto.flow.entities.Transaction.ProposalKey.deserializeBinaryFromReader), 
        msg.setProposalKey(value);
        break;
       case 6:
        value = reader.readBytes(), msg.setPayer(value);
        break;
       case 7:
        value = reader.readBytes(), msg.addAuthorizers(value);
        break;
       case 8:
        value = new proto.flow.entities.Transaction.Signature, reader.readMessage(value, proto.flow.entities.Transaction.Signature.deserializeBinaryFromReader), 
        msg.addPayloadSignatures(value);
        break;
       case 9:
        value = new proto.flow.entities.Transaction.Signature, reader.readMessage(value, proto.flow.entities.Transaction.Signature.deserializeBinaryFromReader), 
        msg.addEnvelopeSignatures(value);
        break;
       default:
        reader.skipField();
      }
      return msg;
    }, proto.flow.entities.Transaction.prototype.serializeBinary = function() {
      var writer = new jspb.BinaryWriter;
      return proto.flow.entities.Transaction.serializeBinaryToWriter(this, writer), writer.getResultBuffer();
    }, proto.flow.entities.Transaction.serializeBinaryToWriter = function(message, writer) {
      var f = void 0;
      (f = message.getScript_asU8()).length > 0 && writer.writeBytes(1, f), (f = message.getArgumentsList_asU8()).length > 0 && writer.writeRepeatedBytes(2, f), 
      (f = message.getReferenceBlockId_asU8()).length > 0 && writer.writeBytes(3, f), 
      0 !== (f = message.getGasLimit()) && writer.writeUint64(4, f), null != (f = message.getProposalKey()) && writer.writeMessage(5, f, proto.flow.entities.Transaction.ProposalKey.serializeBinaryToWriter), 
      (f = message.getPayer_asU8()).length > 0 && writer.writeBytes(6, f), (f = message.getAuthorizersList_asU8()).length > 0 && writer.writeRepeatedBytes(7, f), 
      (f = message.getPayloadSignaturesList()).length > 0 && writer.writeRepeatedMessage(8, f, proto.flow.entities.Transaction.Signature.serializeBinaryToWriter), 
      (f = message.getEnvelopeSignaturesList()).length > 0 && writer.writeRepeatedMessage(9, f, proto.flow.entities.Transaction.Signature.serializeBinaryToWriter);
    }, jspb.Message.GENERATE_TO_OBJECT && (proto.flow.entities.Transaction.ProposalKey.prototype.toObject = function(opt_includeInstance) {
      return proto.flow.entities.Transaction.ProposalKey.toObject(opt_includeInstance, this);
    }, proto.flow.entities.Transaction.ProposalKey.toObject = function(includeInstance, msg) {
      var obj = {
        address: msg.getAddress_asB64(),
        keyId: jspb.Message.getFieldWithDefault(msg, 2, 0),
        sequenceNumber: jspb.Message.getFieldWithDefault(msg, 3, 0)
      };
      return includeInstance && (obj.$jspbMessageInstance = msg), obj;
    }), proto.flow.entities.Transaction.ProposalKey.deserializeBinary = function(bytes) {
      var reader = new jspb.BinaryReader(bytes), msg = new proto.flow.entities.Transaction.ProposalKey;
      return proto.flow.entities.Transaction.ProposalKey.deserializeBinaryFromReader(msg, reader);
    }, proto.flow.entities.Transaction.ProposalKey.deserializeBinaryFromReader = function(msg, reader) {
      for (var value; reader.nextField() && !reader.isEndGroup(); ) switch (reader.getFieldNumber()) {
       case 1:
        value = reader.readBytes(), msg.setAddress(value);
        break;
       case 2:
        value = reader.readUint32(), msg.setKeyId(value);
        break;
       case 3:
        value = reader.readUint64(), msg.setSequenceNumber(value);
        break;
       default:
        reader.skipField();
      }
      return msg;
    }, proto.flow.entities.Transaction.ProposalKey.prototype.serializeBinary = function() {
      var writer = new jspb.BinaryWriter;
      return proto.flow.entities.Transaction.ProposalKey.serializeBinaryToWriter(this, writer), 
      writer.getResultBuffer();
    }, proto.flow.entities.Transaction.ProposalKey.serializeBinaryToWriter = function(message, writer) {
      var f = void 0;
      (f = message.getAddress_asU8()).length > 0 && writer.writeBytes(1, f), 0 !== (f = message.getKeyId()) && writer.writeUint32(2, f), 
      0 !== (f = message.getSequenceNumber()) && writer.writeUint64(3, f);
    }, proto.flow.entities.Transaction.ProposalKey.prototype.getAddress = function() {
      return jspb.Message.getFieldWithDefault(this, 1, "");
    }, proto.flow.entities.Transaction.ProposalKey.prototype.getAddress_asB64 = function() {
      return jspb.Message.bytesAsB64(this.getAddress());
    }, proto.flow.entities.Transaction.ProposalKey.prototype.getAddress_asU8 = function() {
      return jspb.Message.bytesAsU8(this.getAddress());
    }, proto.flow.entities.Transaction.ProposalKey.prototype.setAddress = function(value) {
      return jspb.Message.setProto3BytesField(this, 1, value);
    }, proto.flow.entities.Transaction.ProposalKey.prototype.getKeyId = function() {
      return jspb.Message.getFieldWithDefault(this, 2, 0);
    }, proto.flow.entities.Transaction.ProposalKey.prototype.setKeyId = function(value) {
      return jspb.Message.setProto3IntField(this, 2, value);
    }, proto.flow.entities.Transaction.ProposalKey.prototype.getSequenceNumber = function() {
      return jspb.Message.getFieldWithDefault(this, 3, 0);
    }, proto.flow.entities.Transaction.ProposalKey.prototype.setSequenceNumber = function(value) {
      return jspb.Message.setProto3IntField(this, 3, value);
    }, jspb.Message.GENERATE_TO_OBJECT && (proto.flow.entities.Transaction.Signature.prototype.toObject = function(opt_includeInstance) {
      return proto.flow.entities.Transaction.Signature.toObject(opt_includeInstance, this);
    }, proto.flow.entities.Transaction.Signature.toObject = function(includeInstance, msg) {
      var obj = {
        address: msg.getAddress_asB64(),
        keyId: jspb.Message.getFieldWithDefault(msg, 2, 0),
        signature: msg.getSignature_asB64()
      };
      return includeInstance && (obj.$jspbMessageInstance = msg), obj;
    }), proto.flow.entities.Transaction.Signature.deserializeBinary = function(bytes) {
      var reader = new jspb.BinaryReader(bytes), msg = new proto.flow.entities.Transaction.Signature;
      return proto.flow.entities.Transaction.Signature.deserializeBinaryFromReader(msg, reader);
    }, proto.flow.entities.Transaction.Signature.deserializeBinaryFromReader = function(msg, reader) {
      for (var value; reader.nextField() && !reader.isEndGroup(); ) switch (reader.getFieldNumber()) {
       case 1:
        value = reader.readBytes(), msg.setAddress(value);
        break;
       case 2:
        value = reader.readUint32(), msg.setKeyId(value);
        break;
       case 3:
        value = reader.readBytes(), msg.setSignature(value);
        break;
       default:
        reader.skipField();
      }
      return msg;
    }, proto.flow.entities.Transaction.Signature.prototype.serializeBinary = function() {
      var writer = new jspb.BinaryWriter;
      return proto.flow.entities.Transaction.Signature.serializeBinaryToWriter(this, writer), 
      writer.getResultBuffer();
    }, proto.flow.entities.Transaction.Signature.serializeBinaryToWriter = function(message, writer) {
      var f = void 0;
      (f = message.getAddress_asU8()).length > 0 && writer.writeBytes(1, f), 0 !== (f = message.getKeyId()) && writer.writeUint32(2, f), 
      (f = message.getSignature_asU8()).length > 0 && writer.writeBytes(3, f);
    }, proto.flow.entities.Transaction.Signature.prototype.getAddress = function() {
      return jspb.Message.getFieldWithDefault(this, 1, "");
    }, proto.flow.entities.Transaction.Signature.prototype.getAddress_asB64 = function() {
      return jspb.Message.bytesAsB64(this.getAddress());
    }, proto.flow.entities.Transaction.Signature.prototype.getAddress_asU8 = function() {
      return jspb.Message.bytesAsU8(this.getAddress());
    }, proto.flow.entities.Transaction.Signature.prototype.setAddress = function(value) {
      return jspb.Message.setProto3BytesField(this, 1, value);
    }, proto.flow.entities.Transaction.Signature.prototype.getKeyId = function() {
      return jspb.Message.getFieldWithDefault(this, 2, 0);
    }, proto.flow.entities.Transaction.Signature.prototype.setKeyId = function(value) {
      return jspb.Message.setProto3IntField(this, 2, value);
    }, proto.flow.entities.Transaction.Signature.prototype.getSignature = function() {
      return jspb.Message.getFieldWithDefault(this, 3, "");
    }, proto.flow.entities.Transaction.Signature.prototype.getSignature_asB64 = function() {
      return jspb.Message.bytesAsB64(this.getSignature());
    }, proto.flow.entities.Transaction.Signature.prototype.getSignature_asU8 = function() {
      return jspb.Message.bytesAsU8(this.getSignature());
    }, proto.flow.entities.Transaction.Signature.prototype.setSignature = function(value) {
      return jspb.Message.setProto3BytesField(this, 3, value);
    }, proto.flow.entities.Transaction.prototype.getScript = function() {
      return jspb.Message.getFieldWithDefault(this, 1, "");
    }, proto.flow.entities.Transaction.prototype.getScript_asB64 = function() {
      return jspb.Message.bytesAsB64(this.getScript());
    }, proto.flow.entities.Transaction.prototype.getScript_asU8 = function() {
      return jspb.Message.bytesAsU8(this.getScript());
    }, proto.flow.entities.Transaction.prototype.setScript = function(value) {
      return jspb.Message.setProto3BytesField(this, 1, value);
    }, proto.flow.entities.Transaction.prototype.getArgumentsList = function() {
      return jspb.Message.getRepeatedField(this, 2);
    }, proto.flow.entities.Transaction.prototype.getArgumentsList_asB64 = function() {
      return jspb.Message.bytesListAsB64(this.getArgumentsList());
    }, proto.flow.entities.Transaction.prototype.getArgumentsList_asU8 = function() {
      return jspb.Message.bytesListAsU8(this.getArgumentsList());
    }, proto.flow.entities.Transaction.prototype.setArgumentsList = function(value) {
      return jspb.Message.setField(this, 2, value || []);
    }, proto.flow.entities.Transaction.prototype.addArguments = function(value, opt_index) {
      return jspb.Message.addToRepeatedField(this, 2, value, opt_index);
    }, proto.flow.entities.Transaction.prototype.clearArgumentsList = function() {
      return this.setArgumentsList([]);
    }, proto.flow.entities.Transaction.prototype.getReferenceBlockId = function() {
      return jspb.Message.getFieldWithDefault(this, 3, "");
    }, proto.flow.entities.Transaction.prototype.getReferenceBlockId_asB64 = function() {
      return jspb.Message.bytesAsB64(this.getReferenceBlockId());
    }, proto.flow.entities.Transaction.prototype.getReferenceBlockId_asU8 = function() {
      return jspb.Message.bytesAsU8(this.getReferenceBlockId());
    }, proto.flow.entities.Transaction.prototype.setReferenceBlockId = function(value) {
      return jspb.Message.setProto3BytesField(this, 3, value);
    }, proto.flow.entities.Transaction.prototype.getGasLimit = function() {
      return jspb.Message.getFieldWithDefault(this, 4, 0);
    }, proto.flow.entities.Transaction.prototype.setGasLimit = function(value) {
      return jspb.Message.setProto3IntField(this, 4, value);
    }, proto.flow.entities.Transaction.prototype.getProposalKey = function() {
      return jspb.Message.getWrapperField(this, proto.flow.entities.Transaction.ProposalKey, 5);
    }, proto.flow.entities.Transaction.prototype.setProposalKey = function(value) {
      return jspb.Message.setWrapperField(this, 5, value);
    }, proto.flow.entities.Transaction.prototype.clearProposalKey = function() {
      return this.setProposalKey(void 0);
    }, proto.flow.entities.Transaction.prototype.hasProposalKey = function() {
      return null != jspb.Message.getField(this, 5);
    }, proto.flow.entities.Transaction.prototype.getPayer = function() {
      return jspb.Message.getFieldWithDefault(this, 6, "");
    }, proto.flow.entities.Transaction.prototype.getPayer_asB64 = function() {
      return jspb.Message.bytesAsB64(this.getPayer());
    }, proto.flow.entities.Transaction.prototype.getPayer_asU8 = function() {
      return jspb.Message.bytesAsU8(this.getPayer());
    }, proto.flow.entities.Transaction.prototype.setPayer = function(value) {
      return jspb.Message.setProto3BytesField(this, 6, value);
    }, proto.flow.entities.Transaction.prototype.getAuthorizersList = function() {
      return jspb.Message.getRepeatedField(this, 7);
    }, proto.flow.entities.Transaction.prototype.getAuthorizersList_asB64 = function() {
      return jspb.Message.bytesListAsB64(this.getAuthorizersList());
    }, proto.flow.entities.Transaction.prototype.getAuthorizersList_asU8 = function() {
      return jspb.Message.bytesListAsU8(this.getAuthorizersList());
    }, proto.flow.entities.Transaction.prototype.setAuthorizersList = function(value) {
      return jspb.Message.setField(this, 7, value || []);
    }, proto.flow.entities.Transaction.prototype.addAuthorizers = function(value, opt_index) {
      return jspb.Message.addToRepeatedField(this, 7, value, opt_index);
    }, proto.flow.entities.Transaction.prototype.clearAuthorizersList = function() {
      return this.setAuthorizersList([]);
    }, proto.flow.entities.Transaction.prototype.getPayloadSignaturesList = function() {
      return jspb.Message.getRepeatedWrapperField(this, proto.flow.entities.Transaction.Signature, 8);
    }, proto.flow.entities.Transaction.prototype.setPayloadSignaturesList = function(value) {
      return jspb.Message.setRepeatedWrapperField(this, 8, value);
    }, proto.flow.entities.Transaction.prototype.addPayloadSignatures = function(opt_value, opt_index) {
      return jspb.Message.addToRepeatedWrapperField(this, 8, opt_value, proto.flow.entities.Transaction.Signature, opt_index);
    }, proto.flow.entities.Transaction.prototype.clearPayloadSignaturesList = function() {
      return this.setPayloadSignaturesList([]);
    }, proto.flow.entities.Transaction.prototype.getEnvelopeSignaturesList = function() {
      return jspb.Message.getRepeatedWrapperField(this, proto.flow.entities.Transaction.Signature, 9);
    }, proto.flow.entities.Transaction.prototype.setEnvelopeSignaturesList = function(value) {
      return jspb.Message.setRepeatedWrapperField(this, 9, value);
    }, proto.flow.entities.Transaction.prototype.addEnvelopeSignatures = function(opt_value, opt_index) {
      return jspb.Message.addToRepeatedWrapperField(this, 9, opt_value, proto.flow.entities.Transaction.Signature, opt_index);
    }, proto.flow.entities.Transaction.prototype.clearEnvelopeSignaturesList = function() {
      return this.setEnvelopeSignaturesList([]);
    }, proto.flow.entities.TransactionStatus = {
      UNKNOWN: 0,
      PENDING: 1,
      FINALIZED: 2,
      EXECUTED: 3,
      SEALED: 4,
      EXPIRED: 5
    }, goog.object.extend(exports, proto.flow.entities);
  }(transaction_pb)), transaction_pb;
}
function requireAccount_pb() {
  return hasRequiredAccount_pb || (hasRequiredAccount_pb = 1, function(exports) {
    var jspb = require$$0, goog = jspb, global = "undefined" != typeof globalThis && globalThis || "undefined" != typeof window && window || void 0 !== global && global || "undefined" != typeof self && self || function() {
      return this;
    }.call(null) || Function("return this")();
    goog.exportSymbol("proto.flow.entities.Account", null, global), goog.exportSymbol("proto.flow.entities.AccountKey", null, global), 
    proto.flow.entities.Account = function(opt_data) {
      jspb.Message.initialize(this, opt_data, 0, -1, proto.flow.entities.Account.repeatedFields_, null);
    }, goog.inherits(proto.flow.entities.Account, jspb.Message), goog.DEBUG && !COMPILED && (proto.flow.entities.Account.displayName = "proto.flow.entities.Account"), 
    proto.flow.entities.AccountKey = function(opt_data) {
      jspb.Message.initialize(this, opt_data, 0, -1, null, null);
    }, goog.inherits(proto.flow.entities.AccountKey, jspb.Message), goog.DEBUG && !COMPILED && (proto.flow.entities.AccountKey.displayName = "proto.flow.entities.AccountKey"), 
    proto.flow.entities.Account.repeatedFields_ = [ 4 ], jspb.Message.GENERATE_TO_OBJECT && (proto.flow.entities.Account.prototype.toObject = function(opt_includeInstance) {
      return proto.flow.entities.Account.toObject(opt_includeInstance, this);
    }, proto.flow.entities.Account.toObject = function(includeInstance, msg) {
      var f, obj = {
        address: msg.getAddress_asB64(),
        balance: jspb.Message.getFieldWithDefault(msg, 2, 0),
        code: msg.getCode_asB64(),
        keysList: jspb.Message.toObjectList(msg.getKeysList(), proto.flow.entities.AccountKey.toObject, includeInstance),
        contractsMap: (f = msg.getContractsMap()) ? f.toObject(includeInstance, void 0) : []
      };
      return includeInstance && (obj.$jspbMessageInstance = msg), obj;
    }), proto.flow.entities.Account.deserializeBinary = function(bytes) {
      var reader = new jspb.BinaryReader(bytes), msg = new proto.flow.entities.Account;
      return proto.flow.entities.Account.deserializeBinaryFromReader(msg, reader);
    }, proto.flow.entities.Account.deserializeBinaryFromReader = function(msg, reader) {
      for (var value; reader.nextField() && !reader.isEndGroup(); ) switch (reader.getFieldNumber()) {
       case 1:
        value = reader.readBytes(), msg.setAddress(value);
        break;
       case 2:
        value = reader.readUint64(), msg.setBalance(value);
        break;
       case 3:
        value = reader.readBytes(), msg.setCode(value);
        break;
       case 4:
        value = new proto.flow.entities.AccountKey, reader.readMessage(value, proto.flow.entities.AccountKey.deserializeBinaryFromReader), 
        msg.addKeys(value);
        break;
       case 5:
        value = msg.getContractsMap(), reader.readMessage(value, (function(message, reader) {
          jspb.Map.deserializeBinary(message, reader, jspb.BinaryReader.prototype.readString, jspb.BinaryReader.prototype.readBytes, null, "", "");
        }));
        break;
       default:
        reader.skipField();
      }
      return msg;
    }, proto.flow.entities.Account.prototype.serializeBinary = function() {
      var writer = new jspb.BinaryWriter;
      return proto.flow.entities.Account.serializeBinaryToWriter(this, writer), writer.getResultBuffer();
    }, proto.flow.entities.Account.serializeBinaryToWriter = function(message, writer) {
      var f = void 0;
      (f = message.getAddress_asU8()).length > 0 && writer.writeBytes(1, f), 0 !== (f = message.getBalance()) && writer.writeUint64(2, f), 
      (f = message.getCode_asU8()).length > 0 && writer.writeBytes(3, f), (f = message.getKeysList()).length > 0 && writer.writeRepeatedMessage(4, f, proto.flow.entities.AccountKey.serializeBinaryToWriter), 
      (f = message.getContractsMap(!0)) && f.getLength() > 0 && f.serializeBinary(5, writer, jspb.BinaryWriter.prototype.writeString, jspb.BinaryWriter.prototype.writeBytes);
    }, proto.flow.entities.Account.prototype.getAddress = function() {
      return jspb.Message.getFieldWithDefault(this, 1, "");
    }, proto.flow.entities.Account.prototype.getAddress_asB64 = function() {
      return jspb.Message.bytesAsB64(this.getAddress());
    }, proto.flow.entities.Account.prototype.getAddress_asU8 = function() {
      return jspb.Message.bytesAsU8(this.getAddress());
    }, proto.flow.entities.Account.prototype.setAddress = function(value) {
      return jspb.Message.setProto3BytesField(this, 1, value);
    }, proto.flow.entities.Account.prototype.getBalance = function() {
      return jspb.Message.getFieldWithDefault(this, 2, 0);
    }, proto.flow.entities.Account.prototype.setBalance = function(value) {
      return jspb.Message.setProto3IntField(this, 2, value);
    }, proto.flow.entities.Account.prototype.getCode = function() {
      return jspb.Message.getFieldWithDefault(this, 3, "");
    }, proto.flow.entities.Account.prototype.getCode_asB64 = function() {
      return jspb.Message.bytesAsB64(this.getCode());
    }, proto.flow.entities.Account.prototype.getCode_asU8 = function() {
      return jspb.Message.bytesAsU8(this.getCode());
    }, proto.flow.entities.Account.prototype.setCode = function(value) {
      return jspb.Message.setProto3BytesField(this, 3, value);
    }, proto.flow.entities.Account.prototype.getKeysList = function() {
      return jspb.Message.getRepeatedWrapperField(this, proto.flow.entities.AccountKey, 4);
    }, proto.flow.entities.Account.prototype.setKeysList = function(value) {
      return jspb.Message.setRepeatedWrapperField(this, 4, value);
    }, proto.flow.entities.Account.prototype.addKeys = function(opt_value, opt_index) {
      return jspb.Message.addToRepeatedWrapperField(this, 4, opt_value, proto.flow.entities.AccountKey, opt_index);
    }, proto.flow.entities.Account.prototype.clearKeysList = function() {
      return this.setKeysList([]);
    }, proto.flow.entities.Account.prototype.getContractsMap = function(opt_noLazyCreate) {
      return jspb.Message.getMapField(this, 5, opt_noLazyCreate, null);
    }, proto.flow.entities.Account.prototype.clearContractsMap = function() {
      return this.getContractsMap().clear(), this;
    }, jspb.Message.GENERATE_TO_OBJECT && (proto.flow.entities.AccountKey.prototype.toObject = function(opt_includeInstance) {
      return proto.flow.entities.AccountKey.toObject(opt_includeInstance, this);
    }, proto.flow.entities.AccountKey.toObject = function(includeInstance, msg) {
      var obj = {
        index: jspb.Message.getFieldWithDefault(msg, 1, 0),
        publicKey: msg.getPublicKey_asB64(),
        signAlgo: jspb.Message.getFieldWithDefault(msg, 3, 0),
        hashAlgo: jspb.Message.getFieldWithDefault(msg, 4, 0),
        weight: jspb.Message.getFieldWithDefault(msg, 5, 0),
        sequenceNumber: jspb.Message.getFieldWithDefault(msg, 6, 0),
        revoked: jspb.Message.getBooleanFieldWithDefault(msg, 7, !1)
      };
      return includeInstance && (obj.$jspbMessageInstance = msg), obj;
    }), proto.flow.entities.AccountKey.deserializeBinary = function(bytes) {
      var reader = new jspb.BinaryReader(bytes), msg = new proto.flow.entities.AccountKey;
      return proto.flow.entities.AccountKey.deserializeBinaryFromReader(msg, reader);
    }, proto.flow.entities.AccountKey.deserializeBinaryFromReader = function(msg, reader) {
      for (var value; reader.nextField() && !reader.isEndGroup(); ) switch (reader.getFieldNumber()) {
       case 1:
        value = reader.readUint32(), msg.setIndex(value);
        break;
       case 2:
        value = reader.readBytes(), msg.setPublicKey(value);
        break;
       case 3:
        value = reader.readUint32(), msg.setSignAlgo(value);
        break;
       case 4:
        value = reader.readUint32(), msg.setHashAlgo(value);
        break;
       case 5:
        value = reader.readUint32(), msg.setWeight(value);
        break;
       case 6:
        value = reader.readUint32(), msg.setSequenceNumber(value);
        break;
       case 7:
        value = reader.readBool(), msg.setRevoked(value);
        break;
       default:
        reader.skipField();
      }
      return msg;
    }, proto.flow.entities.AccountKey.prototype.serializeBinary = function() {
      var writer = new jspb.BinaryWriter;
      return proto.flow.entities.AccountKey.serializeBinaryToWriter(this, writer), writer.getResultBuffer();
    }, proto.flow.entities.AccountKey.serializeBinaryToWriter = function(message, writer) {
      var f = void 0;
      0 !== (f = message.getIndex()) && writer.writeUint32(1, f), (f = message.getPublicKey_asU8()).length > 0 && writer.writeBytes(2, f), 
      0 !== (f = message.getSignAlgo()) && writer.writeUint32(3, f), 0 !== (f = message.getHashAlgo()) && writer.writeUint32(4, f), 
      0 !== (f = message.getWeight()) && writer.writeUint32(5, f), 0 !== (f = message.getSequenceNumber()) && writer.writeUint32(6, f), 
      (f = message.getRevoked()) && writer.writeBool(7, f);
    }, proto.flow.entities.AccountKey.prototype.getIndex = function() {
      return jspb.Message.getFieldWithDefault(this, 1, 0);
    }, proto.flow.entities.AccountKey.prototype.setIndex = function(value) {
      return jspb.Message.setProto3IntField(this, 1, value);
    }, proto.flow.entities.AccountKey.prototype.getPublicKey = function() {
      return jspb.Message.getFieldWithDefault(this, 2, "");
    }, proto.flow.entities.AccountKey.prototype.getPublicKey_asB64 = function() {
      return jspb.Message.bytesAsB64(this.getPublicKey());
    }, proto.flow.entities.AccountKey.prototype.getPublicKey_asU8 = function() {
      return jspb.Message.bytesAsU8(this.getPublicKey());
    }, proto.flow.entities.AccountKey.prototype.setPublicKey = function(value) {
      return jspb.Message.setProto3BytesField(this, 2, value);
    }, proto.flow.entities.AccountKey.prototype.getSignAlgo = function() {
      return jspb.Message.getFieldWithDefault(this, 3, 0);
    }, proto.flow.entities.AccountKey.prototype.setSignAlgo = function(value) {
      return jspb.Message.setProto3IntField(this, 3, value);
    }, proto.flow.entities.AccountKey.prototype.getHashAlgo = function() {
      return jspb.Message.getFieldWithDefault(this, 4, 0);
    }, proto.flow.entities.AccountKey.prototype.setHashAlgo = function(value) {
      return jspb.Message.setProto3IntField(this, 4, value);
    }, proto.flow.entities.AccountKey.prototype.getWeight = function() {
      return jspb.Message.getFieldWithDefault(this, 5, 0);
    }, proto.flow.entities.AccountKey.prototype.setWeight = function(value) {
      return jspb.Message.setProto3IntField(this, 5, value);
    }, proto.flow.entities.AccountKey.prototype.getSequenceNumber = function() {
      return jspb.Message.getFieldWithDefault(this, 6, 0);
    }, proto.flow.entities.AccountKey.prototype.setSequenceNumber = function(value) {
      return jspb.Message.setProto3IntField(this, 6, value);
    }, proto.flow.entities.AccountKey.prototype.getRevoked = function() {
      return jspb.Message.getBooleanFieldWithDefault(this, 7, !1);
    }, proto.flow.entities.AccountKey.prototype.setRevoked = function(value) {
      return jspb.Message.setProto3BooleanField(this, 7, value);
    }, goog.object.extend(exports, proto.flow.entities);
  }(account_pb)), account_pb;
}
function requireBlock_header_pb() {
  return hasRequiredBlock_header_pb || (hasRequiredBlock_header_pb = 1, function(exports) {
    var jspb = require$$0, goog = jspb, global = "undefined" != typeof globalThis && globalThis || "undefined" != typeof window && window || void 0 !== global && global || "undefined" != typeof self && self || function() {
      return this;
    }.call(null) || Function("return this")(), google_protobuf_timestamp_pb = require$$10;
    goog.object.extend(proto, google_protobuf_timestamp_pb), goog.exportSymbol("proto.flow.entities.BlockHeader", null, global), 
    goog.exportSymbol("proto.flow.entities.QuorumCertificate", null, global), goog.exportSymbol("proto.flow.entities.TimeoutCertificate", null, global), 
    proto.flow.entities.BlockHeader = function(opt_data) {
      jspb.Message.initialize(this, opt_data, 0, -1, proto.flow.entities.BlockHeader.repeatedFields_, null);
    }, goog.inherits(proto.flow.entities.BlockHeader, jspb.Message), goog.DEBUG && !COMPILED && (proto.flow.entities.BlockHeader.displayName = "proto.flow.entities.BlockHeader"), 
    proto.flow.entities.TimeoutCertificate = function(opt_data) {
      jspb.Message.initialize(this, opt_data, 0, -1, proto.flow.entities.TimeoutCertificate.repeatedFields_, null);
    }, goog.inherits(proto.flow.entities.TimeoutCertificate, jspb.Message), goog.DEBUG && !COMPILED && (proto.flow.entities.TimeoutCertificate.displayName = "proto.flow.entities.TimeoutCertificate"), 
    proto.flow.entities.QuorumCertificate = function(opt_data) {
      jspb.Message.initialize(this, opt_data, 0, -1, null, null);
    }, goog.inherits(proto.flow.entities.QuorumCertificate, jspb.Message), goog.DEBUG && !COMPILED && (proto.flow.entities.QuorumCertificate.displayName = "proto.flow.entities.QuorumCertificate"), 
    proto.flow.entities.BlockHeader.repeatedFields_ = [ 7 ], jspb.Message.GENERATE_TO_OBJECT && (proto.flow.entities.BlockHeader.prototype.toObject = function(opt_includeInstance) {
      return proto.flow.entities.BlockHeader.toObject(opt_includeInstance, this);
    }, proto.flow.entities.BlockHeader.toObject = function(includeInstance, msg) {
      var f, obj = {
        id: msg.getId_asB64(),
        parentId: msg.getParentId_asB64(),
        height: jspb.Message.getFieldWithDefault(msg, 3, 0),
        timestamp: (f = msg.getTimestamp()) && google_protobuf_timestamp_pb.Timestamp.toObject(includeInstance, f),
        payloadHash: msg.getPayloadHash_asB64(),
        view: jspb.Message.getFieldWithDefault(msg, 6, 0),
        parentVoterIdsList: msg.getParentVoterIdsList_asB64(),
        parentVoterSigData: msg.getParentVoterSigData_asB64(),
        proposerId: msg.getProposerId_asB64(),
        proposerSigData: msg.getProposerSigData_asB64(),
        chainId: jspb.Message.getFieldWithDefault(msg, 11, ""),
        parentVoterIndices: msg.getParentVoterIndices_asB64(),
        lastViewTc: (f = msg.getLastViewTc()) && proto.flow.entities.TimeoutCertificate.toObject(includeInstance, f),
        parentView: jspb.Message.getFieldWithDefault(msg, 14, 0)
      };
      return includeInstance && (obj.$jspbMessageInstance = msg), obj;
    }), proto.flow.entities.BlockHeader.deserializeBinary = function(bytes) {
      var reader = new jspb.BinaryReader(bytes), msg = new proto.flow.entities.BlockHeader;
      return proto.flow.entities.BlockHeader.deserializeBinaryFromReader(msg, reader);
    }, proto.flow.entities.BlockHeader.deserializeBinaryFromReader = function(msg, reader) {
      for (var value; reader.nextField() && !reader.isEndGroup(); ) switch (reader.getFieldNumber()) {
       case 1:
        value = reader.readBytes(), msg.setId(value);
        break;
       case 2:
        value = reader.readBytes(), msg.setParentId(value);
        break;
       case 3:
        value = reader.readUint64(), msg.setHeight(value);
        break;
       case 4:
        value = new google_protobuf_timestamp_pb.Timestamp, reader.readMessage(value, google_protobuf_timestamp_pb.Timestamp.deserializeBinaryFromReader), 
        msg.setTimestamp(value);
        break;
       case 5:
        value = reader.readBytes(), msg.setPayloadHash(value);
        break;
       case 6:
        value = reader.readUint64(), msg.setView(value);
        break;
       case 7:
        value = reader.readBytes(), msg.addParentVoterIds(value);
        break;
       case 8:
        value = reader.readBytes(), msg.setParentVoterSigData(value);
        break;
       case 9:
        value = reader.readBytes(), msg.setProposerId(value);
        break;
       case 10:
        value = reader.readBytes(), msg.setProposerSigData(value);
        break;
       case 11:
        value = reader.readString(), msg.setChainId(value);
        break;
       case 12:
        value = reader.readBytes(), msg.setParentVoterIndices(value);
        break;
       case 13:
        value = new proto.flow.entities.TimeoutCertificate, reader.readMessage(value, proto.flow.entities.TimeoutCertificate.deserializeBinaryFromReader), 
        msg.setLastViewTc(value);
        break;
       case 14:
        value = reader.readUint64(), msg.setParentView(value);
        break;
       default:
        reader.skipField();
      }
      return msg;
    }, proto.flow.entities.BlockHeader.prototype.serializeBinary = function() {
      var writer = new jspb.BinaryWriter;
      return proto.flow.entities.BlockHeader.serializeBinaryToWriter(this, writer), writer.getResultBuffer();
    }, proto.flow.entities.BlockHeader.serializeBinaryToWriter = function(message, writer) {
      var f = void 0;
      (f = message.getId_asU8()).length > 0 && writer.writeBytes(1, f), (f = message.getParentId_asU8()).length > 0 && writer.writeBytes(2, f), 
      0 !== (f = message.getHeight()) && writer.writeUint64(3, f), null != (f = message.getTimestamp()) && writer.writeMessage(4, f, google_protobuf_timestamp_pb.Timestamp.serializeBinaryToWriter), 
      (f = message.getPayloadHash_asU8()).length > 0 && writer.writeBytes(5, f), 0 !== (f = message.getView()) && writer.writeUint64(6, f), 
      (f = message.getParentVoterIdsList_asU8()).length > 0 && writer.writeRepeatedBytes(7, f), 
      (f = message.getParentVoterSigData_asU8()).length > 0 && writer.writeBytes(8, f), 
      (f = message.getProposerId_asU8()).length > 0 && writer.writeBytes(9, f), (f = message.getProposerSigData_asU8()).length > 0 && writer.writeBytes(10, f), 
      (f = message.getChainId()).length > 0 && writer.writeString(11, f), (f = message.getParentVoterIndices_asU8()).length > 0 && writer.writeBytes(12, f), 
      null != (f = message.getLastViewTc()) && writer.writeMessage(13, f, proto.flow.entities.TimeoutCertificate.serializeBinaryToWriter), 
      0 !== (f = message.getParentView()) && writer.writeUint64(14, f);
    }, proto.flow.entities.BlockHeader.prototype.getId = function() {
      return jspb.Message.getFieldWithDefault(this, 1, "");
    }, proto.flow.entities.BlockHeader.prototype.getId_asB64 = function() {
      return jspb.Message.bytesAsB64(this.getId());
    }, proto.flow.entities.BlockHeader.prototype.getId_asU8 = function() {
      return jspb.Message.bytesAsU8(this.getId());
    }, proto.flow.entities.BlockHeader.prototype.setId = function(value) {
      return jspb.Message.setProto3BytesField(this, 1, value);
    }, proto.flow.entities.BlockHeader.prototype.getParentId = function() {
      return jspb.Message.getFieldWithDefault(this, 2, "");
    }, proto.flow.entities.BlockHeader.prototype.getParentId_asB64 = function() {
      return jspb.Message.bytesAsB64(this.getParentId());
    }, proto.flow.entities.BlockHeader.prototype.getParentId_asU8 = function() {
      return jspb.Message.bytesAsU8(this.getParentId());
    }, proto.flow.entities.BlockHeader.prototype.setParentId = function(value) {
      return jspb.Message.setProto3BytesField(this, 2, value);
    }, proto.flow.entities.BlockHeader.prototype.getHeight = function() {
      return jspb.Message.getFieldWithDefault(this, 3, 0);
    }, proto.flow.entities.BlockHeader.prototype.setHeight = function(value) {
      return jspb.Message.setProto3IntField(this, 3, value);
    }, proto.flow.entities.BlockHeader.prototype.getTimestamp = function() {
      return jspb.Message.getWrapperField(this, google_protobuf_timestamp_pb.Timestamp, 4);
    }, proto.flow.entities.BlockHeader.prototype.setTimestamp = function(value) {
      return jspb.Message.setWrapperField(this, 4, value);
    }, proto.flow.entities.BlockHeader.prototype.clearTimestamp = function() {
      return this.setTimestamp(void 0);
    }, proto.flow.entities.BlockHeader.prototype.hasTimestamp = function() {
      return null != jspb.Message.getField(this, 4);
    }, proto.flow.entities.BlockHeader.prototype.getPayloadHash = function() {
      return jspb.Message.getFieldWithDefault(this, 5, "");
    }, proto.flow.entities.BlockHeader.prototype.getPayloadHash_asB64 = function() {
      return jspb.Message.bytesAsB64(this.getPayloadHash());
    }, proto.flow.entities.BlockHeader.prototype.getPayloadHash_asU8 = function() {
      return jspb.Message.bytesAsU8(this.getPayloadHash());
    }, proto.flow.entities.BlockHeader.prototype.setPayloadHash = function(value) {
      return jspb.Message.setProto3BytesField(this, 5, value);
    }, proto.flow.entities.BlockHeader.prototype.getView = function() {
      return jspb.Message.getFieldWithDefault(this, 6, 0);
    }, proto.flow.entities.BlockHeader.prototype.setView = function(value) {
      return jspb.Message.setProto3IntField(this, 6, value);
    }, proto.flow.entities.BlockHeader.prototype.getParentVoterIdsList = function() {
      return jspb.Message.getRepeatedField(this, 7);
    }, proto.flow.entities.BlockHeader.prototype.getParentVoterIdsList_asB64 = function() {
      return jspb.Message.bytesListAsB64(this.getParentVoterIdsList());
    }, proto.flow.entities.BlockHeader.prototype.getParentVoterIdsList_asU8 = function() {
      return jspb.Message.bytesListAsU8(this.getParentVoterIdsList());
    }, proto.flow.entities.BlockHeader.prototype.setParentVoterIdsList = function(value) {
      return jspb.Message.setField(this, 7, value || []);
    }, proto.flow.entities.BlockHeader.prototype.addParentVoterIds = function(value, opt_index) {
      return jspb.Message.addToRepeatedField(this, 7, value, opt_index);
    }, proto.flow.entities.BlockHeader.prototype.clearParentVoterIdsList = function() {
      return this.setParentVoterIdsList([]);
    }, proto.flow.entities.BlockHeader.prototype.getParentVoterSigData = function() {
      return jspb.Message.getFieldWithDefault(this, 8, "");
    }, proto.flow.entities.BlockHeader.prototype.getParentVoterSigData_asB64 = function() {
      return jspb.Message.bytesAsB64(this.getParentVoterSigData());
    }, proto.flow.entities.BlockHeader.prototype.getParentVoterSigData_asU8 = function() {
      return jspb.Message.bytesAsU8(this.getParentVoterSigData());
    }, proto.flow.entities.BlockHeader.prototype.setParentVoterSigData = function(value) {
      return jspb.Message.setProto3BytesField(this, 8, value);
    }, proto.flow.entities.BlockHeader.prototype.getProposerId = function() {
      return jspb.Message.getFieldWithDefault(this, 9, "");
    }, proto.flow.entities.BlockHeader.prototype.getProposerId_asB64 = function() {
      return jspb.Message.bytesAsB64(this.getProposerId());
    }, proto.flow.entities.BlockHeader.prototype.getProposerId_asU8 = function() {
      return jspb.Message.bytesAsU8(this.getProposerId());
    }, proto.flow.entities.BlockHeader.prototype.setProposerId = function(value) {
      return jspb.Message.setProto3BytesField(this, 9, value);
    }, proto.flow.entities.BlockHeader.prototype.getProposerSigData = function() {
      return jspb.Message.getFieldWithDefault(this, 10, "");
    }, proto.flow.entities.BlockHeader.prototype.getProposerSigData_asB64 = function() {
      return jspb.Message.bytesAsB64(this.getProposerSigData());
    }, proto.flow.entities.BlockHeader.prototype.getProposerSigData_asU8 = function() {
      return jspb.Message.bytesAsU8(this.getProposerSigData());
    }, proto.flow.entities.BlockHeader.prototype.setProposerSigData = function(value) {
      return jspb.Message.setProto3BytesField(this, 10, value);
    }, proto.flow.entities.BlockHeader.prototype.getChainId = function() {
      return jspb.Message.getFieldWithDefault(this, 11, "");
    }, proto.flow.entities.BlockHeader.prototype.setChainId = function(value) {
      return jspb.Message.setProto3StringField(this, 11, value);
    }, proto.flow.entities.BlockHeader.prototype.getParentVoterIndices = function() {
      return jspb.Message.getFieldWithDefault(this, 12, "");
    }, proto.flow.entities.BlockHeader.prototype.getParentVoterIndices_asB64 = function() {
      return jspb.Message.bytesAsB64(this.getParentVoterIndices());
    }, proto.flow.entities.BlockHeader.prototype.getParentVoterIndices_asU8 = function() {
      return jspb.Message.bytesAsU8(this.getParentVoterIndices());
    }, proto.flow.entities.BlockHeader.prototype.setParentVoterIndices = function(value) {
      return jspb.Message.setProto3BytesField(this, 12, value);
    }, proto.flow.entities.BlockHeader.prototype.getLastViewTc = function() {
      return jspb.Message.getWrapperField(this, proto.flow.entities.TimeoutCertificate, 13);
    }, proto.flow.entities.BlockHeader.prototype.setLastViewTc = function(value) {
      return jspb.Message.setWrapperField(this, 13, value);
    }, proto.flow.entities.BlockHeader.prototype.clearLastViewTc = function() {
      return this.setLastViewTc(void 0);
    }, proto.flow.entities.BlockHeader.prototype.hasLastViewTc = function() {
      return null != jspb.Message.getField(this, 13);
    }, proto.flow.entities.BlockHeader.prototype.getParentView = function() {
      return jspb.Message.getFieldWithDefault(this, 14, 0);
    }, proto.flow.entities.BlockHeader.prototype.setParentView = function(value) {
      return jspb.Message.setProto3IntField(this, 14, value);
    }, proto.flow.entities.TimeoutCertificate.repeatedFields_ = [ 2 ], jspb.Message.GENERATE_TO_OBJECT && (proto.flow.entities.TimeoutCertificate.prototype.toObject = function(opt_includeInstance) {
      return proto.flow.entities.TimeoutCertificate.toObject(opt_includeInstance, this);
    }, proto.flow.entities.TimeoutCertificate.toObject = function(includeInstance, msg) {
      var f, obj = {
        view: jspb.Message.getFieldWithDefault(msg, 1, 0),
        highQcViewsList: null == (f = jspb.Message.getRepeatedField(msg, 2)) ? void 0 : f,
        highestQc: (f = msg.getHighestQc()) && proto.flow.entities.QuorumCertificate.toObject(includeInstance, f),
        signerIndices: msg.getSignerIndices_asB64(),
        sigData: msg.getSigData_asB64()
      };
      return includeInstance && (obj.$jspbMessageInstance = msg), obj;
    }), proto.flow.entities.TimeoutCertificate.deserializeBinary = function(bytes) {
      var reader = new jspb.BinaryReader(bytes), msg = new proto.flow.entities.TimeoutCertificate;
      return proto.flow.entities.TimeoutCertificate.deserializeBinaryFromReader(msg, reader);
    }, proto.flow.entities.TimeoutCertificate.deserializeBinaryFromReader = function(msg, reader) {
      for (var value, values, i; reader.nextField() && !reader.isEndGroup(); ) switch (reader.getFieldNumber()) {
       case 1:
        value = reader.readUint64(), msg.setView(value);
        break;
       case 2:
        for (values = reader.isDelimited() ? reader.readPackedUint64() : [ reader.readUint64() ], 
        i = 0; i < values.length; i++) msg.addHighQcViews(values[i]);
        break;
       case 3:
        value = new proto.flow.entities.QuorumCertificate, reader.readMessage(value, proto.flow.entities.QuorumCertificate.deserializeBinaryFromReader), 
        msg.setHighestQc(value);
        break;
       case 4:
        value = reader.readBytes(), msg.setSignerIndices(value);
        break;
       case 5:
        value = reader.readBytes(), msg.setSigData(value);
        break;
       default:
        reader.skipField();
      }
      return msg;
    }, proto.flow.entities.TimeoutCertificate.prototype.serializeBinary = function() {
      var writer = new jspb.BinaryWriter;
      return proto.flow.entities.TimeoutCertificate.serializeBinaryToWriter(this, writer), 
      writer.getResultBuffer();
    }, proto.flow.entities.TimeoutCertificate.serializeBinaryToWriter = function(message, writer) {
      var f = void 0;
      0 !== (f = message.getView()) && writer.writeUint64(1, f), (f = message.getHighQcViewsList()).length > 0 && writer.writePackedUint64(2, f), 
      null != (f = message.getHighestQc()) && writer.writeMessage(3, f, proto.flow.entities.QuorumCertificate.serializeBinaryToWriter), 
      (f = message.getSignerIndices_asU8()).length > 0 && writer.writeBytes(4, f), (f = message.getSigData_asU8()).length > 0 && writer.writeBytes(5, f);
    }, proto.flow.entities.TimeoutCertificate.prototype.getView = function() {
      return jspb.Message.getFieldWithDefault(this, 1, 0);
    }, proto.flow.entities.TimeoutCertificate.prototype.setView = function(value) {
      return jspb.Message.setProto3IntField(this, 1, value);
    }, proto.flow.entities.TimeoutCertificate.prototype.getHighQcViewsList = function() {
      return jspb.Message.getRepeatedField(this, 2);
    }, proto.flow.entities.TimeoutCertificate.prototype.setHighQcViewsList = function(value) {
      return jspb.Message.setField(this, 2, value || []);
    }, proto.flow.entities.TimeoutCertificate.prototype.addHighQcViews = function(value, opt_index) {
      return jspb.Message.addToRepeatedField(this, 2, value, opt_index);
    }, proto.flow.entities.TimeoutCertificate.prototype.clearHighQcViewsList = function() {
      return this.setHighQcViewsList([]);
    }, proto.flow.entities.TimeoutCertificate.prototype.getHighestQc = function() {
      return jspb.Message.getWrapperField(this, proto.flow.entities.QuorumCertificate, 3);
    }, proto.flow.entities.TimeoutCertificate.prototype.setHighestQc = function(value) {
      return jspb.Message.setWrapperField(this, 3, value);
    }, proto.flow.entities.TimeoutCertificate.prototype.clearHighestQc = function() {
      return this.setHighestQc(void 0);
    }, proto.flow.entities.TimeoutCertificate.prototype.hasHighestQc = function() {
      return null != jspb.Message.getField(this, 3);
    }, proto.flow.entities.TimeoutCertificate.prototype.getSignerIndices = function() {
      return jspb.Message.getFieldWithDefault(this, 4, "");
    }, proto.flow.entities.TimeoutCertificate.prototype.getSignerIndices_asB64 = function() {
      return jspb.Message.bytesAsB64(this.getSignerIndices());
    }, proto.flow.entities.TimeoutCertificate.prototype.getSignerIndices_asU8 = function() {
      return jspb.Message.bytesAsU8(this.getSignerIndices());
    }, proto.flow.entities.TimeoutCertificate.prototype.setSignerIndices = function(value) {
      return jspb.Message.setProto3BytesField(this, 4, value);
    }, proto.flow.entities.TimeoutCertificate.prototype.getSigData = function() {
      return jspb.Message.getFieldWithDefault(this, 5, "");
    }, proto.flow.entities.TimeoutCertificate.prototype.getSigData_asB64 = function() {
      return jspb.Message.bytesAsB64(this.getSigData());
    }, proto.flow.entities.TimeoutCertificate.prototype.getSigData_asU8 = function() {
      return jspb.Message.bytesAsU8(this.getSigData());
    }, proto.flow.entities.TimeoutCertificate.prototype.setSigData = function(value) {
      return jspb.Message.setProto3BytesField(this, 5, value);
    }, jspb.Message.GENERATE_TO_OBJECT && (proto.flow.entities.QuorumCertificate.prototype.toObject = function(opt_includeInstance) {
      return proto.flow.entities.QuorumCertificate.toObject(opt_includeInstance, this);
    }, proto.flow.entities.QuorumCertificate.toObject = function(includeInstance, msg) {
      var obj = {
        view: jspb.Message.getFieldWithDefault(msg, 1, 0),
        blockId: msg.getBlockId_asB64(),
        signerIndices: msg.getSignerIndices_asB64(),
        sigData: msg.getSigData_asB64()
      };
      return includeInstance && (obj.$jspbMessageInstance = msg), obj;
    }), proto.flow.entities.QuorumCertificate.deserializeBinary = function(bytes) {
      var reader = new jspb.BinaryReader(bytes), msg = new proto.flow.entities.QuorumCertificate;
      return proto.flow.entities.QuorumCertificate.deserializeBinaryFromReader(msg, reader);
    }, proto.flow.entities.QuorumCertificate.deserializeBinaryFromReader = function(msg, reader) {
      for (var value; reader.nextField() && !reader.isEndGroup(); ) switch (reader.getFieldNumber()) {
       case 1:
        value = reader.readUint64(), msg.setView(value);
        break;
       case 2:
        value = reader.readBytes(), msg.setBlockId(value);
        break;
       case 3:
        value = reader.readBytes(), msg.setSignerIndices(value);
        break;
       case 4:
        value = reader.readBytes(), msg.setSigData(value);
        break;
       default:
        reader.skipField();
      }
      return msg;
    }, proto.flow.entities.QuorumCertificate.prototype.serializeBinary = function() {
      var writer = new jspb.BinaryWriter;
      return proto.flow.entities.QuorumCertificate.serializeBinaryToWriter(this, writer), 
      writer.getResultBuffer();
    }, proto.flow.entities.QuorumCertificate.serializeBinaryToWriter = function(message, writer) {
      var f = void 0;
      0 !== (f = message.getView()) && writer.writeUint64(1, f), (f = message.getBlockId_asU8()).length > 0 && writer.writeBytes(2, f), 
      (f = message.getSignerIndices_asU8()).length > 0 && writer.writeBytes(3, f), (f = message.getSigData_asU8()).length > 0 && writer.writeBytes(4, f);
    }, proto.flow.entities.QuorumCertificate.prototype.getView = function() {
      return jspb.Message.getFieldWithDefault(this, 1, 0);
    }, proto.flow.entities.QuorumCertificate.prototype.setView = function(value) {
      return jspb.Message.setProto3IntField(this, 1, value);
    }, proto.flow.entities.QuorumCertificate.prototype.getBlockId = function() {
      return jspb.Message.getFieldWithDefault(this, 2, "");
    }, proto.flow.entities.QuorumCertificate.prototype.getBlockId_asB64 = function() {
      return jspb.Message.bytesAsB64(this.getBlockId());
    }, proto.flow.entities.QuorumCertificate.prototype.getBlockId_asU8 = function() {
      return jspb.Message.bytesAsU8(this.getBlockId());
    }, proto.flow.entities.QuorumCertificate.prototype.setBlockId = function(value) {
      return jspb.Message.setProto3BytesField(this, 2, value);
    }, proto.flow.entities.QuorumCertificate.prototype.getSignerIndices = function() {
      return jspb.Message.getFieldWithDefault(this, 3, "");
    }, proto.flow.entities.QuorumCertificate.prototype.getSignerIndices_asB64 = function() {
      return jspb.Message.bytesAsB64(this.getSignerIndices());
    }, proto.flow.entities.QuorumCertificate.prototype.getSignerIndices_asU8 = function() {
      return jspb.Message.bytesAsU8(this.getSignerIndices());
    }, proto.flow.entities.QuorumCertificate.prototype.setSignerIndices = function(value) {
      return jspb.Message.setProto3BytesField(this, 3, value);
    }, proto.flow.entities.QuorumCertificate.prototype.getSigData = function() {
      return jspb.Message.getFieldWithDefault(this, 4, "");
    }, proto.flow.entities.QuorumCertificate.prototype.getSigData_asB64 = function() {
      return jspb.Message.bytesAsB64(this.getSigData());
    }, proto.flow.entities.QuorumCertificate.prototype.getSigData_asU8 = function() {
      return jspb.Message.bytesAsU8(this.getSigData());
    }, proto.flow.entities.QuorumCertificate.prototype.setSigData = function(value) {
      return jspb.Message.setProto3BytesField(this, 4, value);
    }, goog.object.extend(exports, proto.flow.entities);
  }(block_header_pb)), block_header_pb;
}
function requireCollection_pb() {
  return hasRequiredCollection_pb || (hasRequiredCollection_pb = 1, function(exports) {
    var jspb = require$$0, goog = jspb, global = "undefined" != typeof globalThis && globalThis || "undefined" != typeof window && window || void 0 !== global && global || "undefined" != typeof self && self || function() {
      return this;
    }.call(null) || Function("return this")();
    goog.exportSymbol("proto.flow.entities.Collection", null, global), goog.exportSymbol("proto.flow.entities.CollectionGuarantee", null, global), 
    proto.flow.entities.Collection = function(opt_data) {
      jspb.Message.initialize(this, opt_data, 0, -1, proto.flow.entities.Collection.repeatedFields_, null);
    }, goog.inherits(proto.flow.entities.Collection, jspb.Message), goog.DEBUG && !COMPILED && (proto.flow.entities.Collection.displayName = "proto.flow.entities.Collection"), 
    proto.flow.entities.CollectionGuarantee = function(opt_data) {
      jspb.Message.initialize(this, opt_data, 0, -1, proto.flow.entities.CollectionGuarantee.repeatedFields_, null);
    }, goog.inherits(proto.flow.entities.CollectionGuarantee, jspb.Message), goog.DEBUG && !COMPILED && (proto.flow.entities.CollectionGuarantee.displayName = "proto.flow.entities.CollectionGuarantee"), 
    proto.flow.entities.Collection.repeatedFields_ = [ 2 ], jspb.Message.GENERATE_TO_OBJECT && (proto.flow.entities.Collection.prototype.toObject = function(opt_includeInstance) {
      return proto.flow.entities.Collection.toObject(opt_includeInstance, this);
    }, proto.flow.entities.Collection.toObject = function(includeInstance, msg) {
      var obj = {
        id: msg.getId_asB64(),
        transactionIdsList: msg.getTransactionIdsList_asB64()
      };
      return includeInstance && (obj.$jspbMessageInstance = msg), obj;
    }), proto.flow.entities.Collection.deserializeBinary = function(bytes) {
      var reader = new jspb.BinaryReader(bytes), msg = new proto.flow.entities.Collection;
      return proto.flow.entities.Collection.deserializeBinaryFromReader(msg, reader);
    }, proto.flow.entities.Collection.deserializeBinaryFromReader = function(msg, reader) {
      for (var value; reader.nextField() && !reader.isEndGroup(); ) switch (reader.getFieldNumber()) {
       case 1:
        value = reader.readBytes(), msg.setId(value);
        break;
       case 2:
        value = reader.readBytes(), msg.addTransactionIds(value);
        break;
       default:
        reader.skipField();
      }
      return msg;
    }, proto.flow.entities.Collection.prototype.serializeBinary = function() {
      var writer = new jspb.BinaryWriter;
      return proto.flow.entities.Collection.serializeBinaryToWriter(this, writer), writer.getResultBuffer();
    }, proto.flow.entities.Collection.serializeBinaryToWriter = function(message, writer) {
      var f = void 0;
      (f = message.getId_asU8()).length > 0 && writer.writeBytes(1, f), (f = message.getTransactionIdsList_asU8()).length > 0 && writer.writeRepeatedBytes(2, f);
    }, proto.flow.entities.Collection.prototype.getId = function() {
      return jspb.Message.getFieldWithDefault(this, 1, "");
    }, proto.flow.entities.Collection.prototype.getId_asB64 = function() {
      return jspb.Message.bytesAsB64(this.getId());
    }, proto.flow.entities.Collection.prototype.getId_asU8 = function() {
      return jspb.Message.bytesAsU8(this.getId());
    }, proto.flow.entities.Collection.prototype.setId = function(value) {
      return jspb.Message.setProto3BytesField(this, 1, value);
    }, proto.flow.entities.Collection.prototype.getTransactionIdsList = function() {
      return jspb.Message.getRepeatedField(this, 2);
    }, proto.flow.entities.Collection.prototype.getTransactionIdsList_asB64 = function() {
      return jspb.Message.bytesListAsB64(this.getTransactionIdsList());
    }, proto.flow.entities.Collection.prototype.getTransactionIdsList_asU8 = function() {
      return jspb.Message.bytesListAsU8(this.getTransactionIdsList());
    }, proto.flow.entities.Collection.prototype.setTransactionIdsList = function(value) {
      return jspb.Message.setField(this, 2, value || []);
    }, proto.flow.entities.Collection.prototype.addTransactionIds = function(value, opt_index) {
      return jspb.Message.addToRepeatedField(this, 2, value, opt_index);
    }, proto.flow.entities.Collection.prototype.clearTransactionIdsList = function() {
      return this.setTransactionIdsList([]);
    }, proto.flow.entities.CollectionGuarantee.repeatedFields_ = [ 2, 5 ], jspb.Message.GENERATE_TO_OBJECT && (proto.flow.entities.CollectionGuarantee.prototype.toObject = function(opt_includeInstance) {
      return proto.flow.entities.CollectionGuarantee.toObject(opt_includeInstance, this);
    }, proto.flow.entities.CollectionGuarantee.toObject = function(includeInstance, msg) {
      var obj = {
        collectionId: msg.getCollectionId_asB64(),
        signaturesList: msg.getSignaturesList_asB64(),
        referenceBlockId: msg.getReferenceBlockId_asB64(),
        signature: msg.getSignature_asB64(),
        signerIdsList: msg.getSignerIdsList_asB64(),
        signerIndices: msg.getSignerIndices_asB64()
      };
      return includeInstance && (obj.$jspbMessageInstance = msg), obj;
    }), proto.flow.entities.CollectionGuarantee.deserializeBinary = function(bytes) {
      var reader = new jspb.BinaryReader(bytes), msg = new proto.flow.entities.CollectionGuarantee;
      return proto.flow.entities.CollectionGuarantee.deserializeBinaryFromReader(msg, reader);
    }, proto.flow.entities.CollectionGuarantee.deserializeBinaryFromReader = function(msg, reader) {
      for (var value; reader.nextField() && !reader.isEndGroup(); ) switch (reader.getFieldNumber()) {
       case 1:
        value = reader.readBytes(), msg.setCollectionId(value);
        break;
       case 2:
        value = reader.readBytes(), msg.addSignatures(value);
        break;
       case 3:
        value = reader.readBytes(), msg.setReferenceBlockId(value);
        break;
       case 4:
        value = reader.readBytes(), msg.setSignature(value);
        break;
       case 5:
        value = reader.readBytes(), msg.addSignerIds(value);
        break;
       case 6:
        value = reader.readBytes(), msg.setSignerIndices(value);
        break;
       default:
        reader.skipField();
      }
      return msg;
    }, proto.flow.entities.CollectionGuarantee.prototype.serializeBinary = function() {
      var writer = new jspb.BinaryWriter;
      return proto.flow.entities.CollectionGuarantee.serializeBinaryToWriter(this, writer), 
      writer.getResultBuffer();
    }, proto.flow.entities.CollectionGuarantee.serializeBinaryToWriter = function(message, writer) {
      var f = void 0;
      (f = message.getCollectionId_asU8()).length > 0 && writer.writeBytes(1, f), (f = message.getSignaturesList_asU8()).length > 0 && writer.writeRepeatedBytes(2, f), 
      (f = message.getReferenceBlockId_asU8()).length > 0 && writer.writeBytes(3, f), 
      (f = message.getSignature_asU8()).length > 0 && writer.writeBytes(4, f), (f = message.getSignerIdsList_asU8()).length > 0 && writer.writeRepeatedBytes(5, f), 
      (f = message.getSignerIndices_asU8()).length > 0 && writer.writeBytes(6, f);
    }, proto.flow.entities.CollectionGuarantee.prototype.getCollectionId = function() {
      return jspb.Message.getFieldWithDefault(this, 1, "");
    }, proto.flow.entities.CollectionGuarantee.prototype.getCollectionId_asB64 = function() {
      return jspb.Message.bytesAsB64(this.getCollectionId());
    }, proto.flow.entities.CollectionGuarantee.prototype.getCollectionId_asU8 = function() {
      return jspb.Message.bytesAsU8(this.getCollectionId());
    }, proto.flow.entities.CollectionGuarantee.prototype.setCollectionId = function(value) {
      return jspb.Message.setProto3BytesField(this, 1, value);
    }, proto.flow.entities.CollectionGuarantee.prototype.getSignaturesList = function() {
      return jspb.Message.getRepeatedField(this, 2);
    }, proto.flow.entities.CollectionGuarantee.prototype.getSignaturesList_asB64 = function() {
      return jspb.Message.bytesListAsB64(this.getSignaturesList());
    }, proto.flow.entities.CollectionGuarantee.prototype.getSignaturesList_asU8 = function() {
      return jspb.Message.bytesListAsU8(this.getSignaturesList());
    }, proto.flow.entities.CollectionGuarantee.prototype.setSignaturesList = function(value) {
      return jspb.Message.setField(this, 2, value || []);
    }, proto.flow.entities.CollectionGuarantee.prototype.addSignatures = function(value, opt_index) {
      return jspb.Message.addToRepeatedField(this, 2, value, opt_index);
    }, proto.flow.entities.CollectionGuarantee.prototype.clearSignaturesList = function() {
      return this.setSignaturesList([]);
    }, proto.flow.entities.CollectionGuarantee.prototype.getReferenceBlockId = function() {
      return jspb.Message.getFieldWithDefault(this, 3, "");
    }, proto.flow.entities.CollectionGuarantee.prototype.getReferenceBlockId_asB64 = function() {
      return jspb.Message.bytesAsB64(this.getReferenceBlockId());
    }, proto.flow.entities.CollectionGuarantee.prototype.getReferenceBlockId_asU8 = function() {
      return jspb.Message.bytesAsU8(this.getReferenceBlockId());
    }, proto.flow.entities.CollectionGuarantee.prototype.setReferenceBlockId = function(value) {
      return jspb.Message.setProto3BytesField(this, 3, value);
    }, proto.flow.entities.CollectionGuarantee.prototype.getSignature = function() {
      return jspb.Message.getFieldWithDefault(this, 4, "");
    }, proto.flow.entities.CollectionGuarantee.prototype.getSignature_asB64 = function() {
      return jspb.Message.bytesAsB64(this.getSignature());
    }, proto.flow.entities.CollectionGuarantee.prototype.getSignature_asU8 = function() {
      return jspb.Message.bytesAsU8(this.getSignature());
    }, proto.flow.entities.CollectionGuarantee.prototype.setSignature = function(value) {
      return jspb.Message.setProto3BytesField(this, 4, value);
    }, proto.flow.entities.CollectionGuarantee.prototype.getSignerIdsList = function() {
      return jspb.Message.getRepeatedField(this, 5);
    }, proto.flow.entities.CollectionGuarantee.prototype.getSignerIdsList_asB64 = function() {
      return jspb.Message.bytesListAsB64(this.getSignerIdsList());
    }, proto.flow.entities.CollectionGuarantee.prototype.getSignerIdsList_asU8 = function() {
      return jspb.Message.bytesListAsU8(this.getSignerIdsList());
    }, proto.flow.entities.CollectionGuarantee.prototype.setSignerIdsList = function(value) {
      return jspb.Message.setField(this, 5, value || []);
    }, proto.flow.entities.CollectionGuarantee.prototype.addSignerIds = function(value, opt_index) {
      return jspb.Message.addToRepeatedField(this, 5, value, opt_index);
    }, proto.flow.entities.CollectionGuarantee.prototype.clearSignerIdsList = function() {
      return this.setSignerIdsList([]);
    }, proto.flow.entities.CollectionGuarantee.prototype.getSignerIndices = function() {
      return jspb.Message.getFieldWithDefault(this, 6, "");
    }, proto.flow.entities.CollectionGuarantee.prototype.getSignerIndices_asB64 = function() {
      return jspb.Message.bytesAsB64(this.getSignerIndices());
    }, proto.flow.entities.CollectionGuarantee.prototype.getSignerIndices_asU8 = function() {
      return jspb.Message.bytesAsU8(this.getSignerIndices());
    }, proto.flow.entities.CollectionGuarantee.prototype.setSignerIndices = function(value) {
      return jspb.Message.setProto3BytesField(this, 6, value);
    }, goog.object.extend(exports, proto.flow.entities);
  }(collection_pb)), collection_pb;
}
function requireBlock_seal_pb() {
  return hasRequiredBlock_seal_pb || (hasRequiredBlock_seal_pb = 1, function(exports) {
    var jspb = require$$0, goog = jspb, global = "undefined" != typeof globalThis && globalThis || "undefined" != typeof window && window || void 0 !== global && global || "undefined" != typeof self && self || function() {
      return this;
    }.call(null) || Function("return this")();
    goog.exportSymbol("proto.flow.entities.AggregatedSignature", null, global), goog.exportSymbol("proto.flow.entities.BlockSeal", null, global), 
    proto.flow.entities.BlockSeal = function(opt_data) {
      jspb.Message.initialize(this, opt_data, 0, -1, proto.flow.entities.BlockSeal.repeatedFields_, null);
    }, goog.inherits(proto.flow.entities.BlockSeal, jspb.Message), goog.DEBUG && !COMPILED && (proto.flow.entities.BlockSeal.displayName = "proto.flow.entities.BlockSeal"), 
    proto.flow.entities.AggregatedSignature = function(opt_data) {
      jspb.Message.initialize(this, opt_data, 0, -1, proto.flow.entities.AggregatedSignature.repeatedFields_, null);
    }, goog.inherits(proto.flow.entities.AggregatedSignature, jspb.Message), goog.DEBUG && !COMPILED && (proto.flow.entities.AggregatedSignature.displayName = "proto.flow.entities.AggregatedSignature"), 
    proto.flow.entities.BlockSeal.repeatedFields_ = [ 3, 4, 7 ], jspb.Message.GENERATE_TO_OBJECT && (proto.flow.entities.BlockSeal.prototype.toObject = function(opt_includeInstance) {
      return proto.flow.entities.BlockSeal.toObject(opt_includeInstance, this);
    }, proto.flow.entities.BlockSeal.toObject = function(includeInstance, msg) {
      var obj = {
        blockId: msg.getBlockId_asB64(),
        executionReceiptId: msg.getExecutionReceiptId_asB64(),
        executionReceiptSignaturesList: msg.getExecutionReceiptSignaturesList_asB64(),
        resultApprovalSignaturesList: msg.getResultApprovalSignaturesList_asB64(),
        finalState: msg.getFinalState_asB64(),
        resultId: msg.getResultId_asB64(),
        aggregatedApprovalSigsList: jspb.Message.toObjectList(msg.getAggregatedApprovalSigsList(), proto.flow.entities.AggregatedSignature.toObject, includeInstance)
      };
      return includeInstance && (obj.$jspbMessageInstance = msg), obj;
    }), proto.flow.entities.BlockSeal.deserializeBinary = function(bytes) {
      var reader = new jspb.BinaryReader(bytes), msg = new proto.flow.entities.BlockSeal;
      return proto.flow.entities.BlockSeal.deserializeBinaryFromReader(msg, reader);
    }, proto.flow.entities.BlockSeal.deserializeBinaryFromReader = function(msg, reader) {
      for (var value; reader.nextField() && !reader.isEndGroup(); ) switch (reader.getFieldNumber()) {
       case 1:
        value = reader.readBytes(), msg.setBlockId(value);
        break;
       case 2:
        value = reader.readBytes(), msg.setExecutionReceiptId(value);
        break;
       case 3:
        value = reader.readBytes(), msg.addExecutionReceiptSignatures(value);
        break;
       case 4:
        value = reader.readBytes(), msg.addResultApprovalSignatures(value);
        break;
       case 5:
        value = reader.readBytes(), msg.setFinalState(value);
        break;
       case 6:
        value = reader.readBytes(), msg.setResultId(value);
        break;
       case 7:
        value = new proto.flow.entities.AggregatedSignature, reader.readMessage(value, proto.flow.entities.AggregatedSignature.deserializeBinaryFromReader), 
        msg.addAggregatedApprovalSigs(value);
        break;
       default:
        reader.skipField();
      }
      return msg;
    }, proto.flow.entities.BlockSeal.prototype.serializeBinary = function() {
      var writer = new jspb.BinaryWriter;
      return proto.flow.entities.BlockSeal.serializeBinaryToWriter(this, writer), writer.getResultBuffer();
    }, proto.flow.entities.BlockSeal.serializeBinaryToWriter = function(message, writer) {
      var f = void 0;
      (f = message.getBlockId_asU8()).length > 0 && writer.writeBytes(1, f), (f = message.getExecutionReceiptId_asU8()).length > 0 && writer.writeBytes(2, f), 
      (f = message.getExecutionReceiptSignaturesList_asU8()).length > 0 && writer.writeRepeatedBytes(3, f), 
      (f = message.getResultApprovalSignaturesList_asU8()).length > 0 && writer.writeRepeatedBytes(4, f), 
      (f = message.getFinalState_asU8()).length > 0 && writer.writeBytes(5, f), (f = message.getResultId_asU8()).length > 0 && writer.writeBytes(6, f), 
      (f = message.getAggregatedApprovalSigsList()).length > 0 && writer.writeRepeatedMessage(7, f, proto.flow.entities.AggregatedSignature.serializeBinaryToWriter);
    }, proto.flow.entities.BlockSeal.prototype.getBlockId = function() {
      return jspb.Message.getFieldWithDefault(this, 1, "");
    }, proto.flow.entities.BlockSeal.prototype.getBlockId_asB64 = function() {
      return jspb.Message.bytesAsB64(this.getBlockId());
    }, proto.flow.entities.BlockSeal.prototype.getBlockId_asU8 = function() {
      return jspb.Message.bytesAsU8(this.getBlockId());
    }, proto.flow.entities.BlockSeal.prototype.setBlockId = function(value) {
      return jspb.Message.setProto3BytesField(this, 1, value);
    }, proto.flow.entities.BlockSeal.prototype.getExecutionReceiptId = function() {
      return jspb.Message.getFieldWithDefault(this, 2, "");
    }, proto.flow.entities.BlockSeal.prototype.getExecutionReceiptId_asB64 = function() {
      return jspb.Message.bytesAsB64(this.getExecutionReceiptId());
    }, proto.flow.entities.BlockSeal.prototype.getExecutionReceiptId_asU8 = function() {
      return jspb.Message.bytesAsU8(this.getExecutionReceiptId());
    }, proto.flow.entities.BlockSeal.prototype.setExecutionReceiptId = function(value) {
      return jspb.Message.setProto3BytesField(this, 2, value);
    }, proto.flow.entities.BlockSeal.prototype.getExecutionReceiptSignaturesList = function() {
      return jspb.Message.getRepeatedField(this, 3);
    }, proto.flow.entities.BlockSeal.prototype.getExecutionReceiptSignaturesList_asB64 = function() {
      return jspb.Message.bytesListAsB64(this.getExecutionReceiptSignaturesList());
    }, proto.flow.entities.BlockSeal.prototype.getExecutionReceiptSignaturesList_asU8 = function() {
      return jspb.Message.bytesListAsU8(this.getExecutionReceiptSignaturesList());
    }, proto.flow.entities.BlockSeal.prototype.setExecutionReceiptSignaturesList = function(value) {
      return jspb.Message.setField(this, 3, value || []);
    }, proto.flow.entities.BlockSeal.prototype.addExecutionReceiptSignatures = function(value, opt_index) {
      return jspb.Message.addToRepeatedField(this, 3, value, opt_index);
    }, proto.flow.entities.BlockSeal.prototype.clearExecutionReceiptSignaturesList = function() {
      return this.setExecutionReceiptSignaturesList([]);
    }, proto.flow.entities.BlockSeal.prototype.getResultApprovalSignaturesList = function() {
      return jspb.Message.getRepeatedField(this, 4);
    }, proto.flow.entities.BlockSeal.prototype.getResultApprovalSignaturesList_asB64 = function() {
      return jspb.Message.bytesListAsB64(this.getResultApprovalSignaturesList());
    }, proto.flow.entities.BlockSeal.prototype.getResultApprovalSignaturesList_asU8 = function() {
      return jspb.Message.bytesListAsU8(this.getResultApprovalSignaturesList());
    }, proto.flow.entities.BlockSeal.prototype.setResultApprovalSignaturesList = function(value) {
      return jspb.Message.setField(this, 4, value || []);
    }, proto.flow.entities.BlockSeal.prototype.addResultApprovalSignatures = function(value, opt_index) {
      return jspb.Message.addToRepeatedField(this, 4, value, opt_index);
    }, proto.flow.entities.BlockSeal.prototype.clearResultApprovalSignaturesList = function() {
      return this.setResultApprovalSignaturesList([]);
    }, proto.flow.entities.BlockSeal.prototype.getFinalState = function() {
      return jspb.Message.getFieldWithDefault(this, 5, "");
    }, proto.flow.entities.BlockSeal.prototype.getFinalState_asB64 = function() {
      return jspb.Message.bytesAsB64(this.getFinalState());
    }, proto.flow.entities.BlockSeal.prototype.getFinalState_asU8 = function() {
      return jspb.Message.bytesAsU8(this.getFinalState());
    }, proto.flow.entities.BlockSeal.prototype.setFinalState = function(value) {
      return jspb.Message.setProto3BytesField(this, 5, value);
    }, proto.flow.entities.BlockSeal.prototype.getResultId = function() {
      return jspb.Message.getFieldWithDefault(this, 6, "");
    }, proto.flow.entities.BlockSeal.prototype.getResultId_asB64 = function() {
      return jspb.Message.bytesAsB64(this.getResultId());
    }, proto.flow.entities.BlockSeal.prototype.getResultId_asU8 = function() {
      return jspb.Message.bytesAsU8(this.getResultId());
    }, proto.flow.entities.BlockSeal.prototype.setResultId = function(value) {
      return jspb.Message.setProto3BytesField(this, 6, value);
    }, proto.flow.entities.BlockSeal.prototype.getAggregatedApprovalSigsList = function() {
      return jspb.Message.getRepeatedWrapperField(this, proto.flow.entities.AggregatedSignature, 7);
    }, proto.flow.entities.BlockSeal.prototype.setAggregatedApprovalSigsList = function(value) {
      return jspb.Message.setRepeatedWrapperField(this, 7, value);
    }, proto.flow.entities.BlockSeal.prototype.addAggregatedApprovalSigs = function(opt_value, opt_index) {
      return jspb.Message.addToRepeatedWrapperField(this, 7, opt_value, proto.flow.entities.AggregatedSignature, opt_index);
    }, proto.flow.entities.BlockSeal.prototype.clearAggregatedApprovalSigsList = function() {
      return this.setAggregatedApprovalSigsList([]);
    }, proto.flow.entities.AggregatedSignature.repeatedFields_ = [ 1, 2 ], jspb.Message.GENERATE_TO_OBJECT && (proto.flow.entities.AggregatedSignature.prototype.toObject = function(opt_includeInstance) {
      return proto.flow.entities.AggregatedSignature.toObject(opt_includeInstance, this);
    }, proto.flow.entities.AggregatedSignature.toObject = function(includeInstance, msg) {
      var obj = {
        verifierSignaturesList: msg.getVerifierSignaturesList_asB64(),
        signerIdsList: msg.getSignerIdsList_asB64()
      };
      return includeInstance && (obj.$jspbMessageInstance = msg), obj;
    }), proto.flow.entities.AggregatedSignature.deserializeBinary = function(bytes) {
      var reader = new jspb.BinaryReader(bytes), msg = new proto.flow.entities.AggregatedSignature;
      return proto.flow.entities.AggregatedSignature.deserializeBinaryFromReader(msg, reader);
    }, proto.flow.entities.AggregatedSignature.deserializeBinaryFromReader = function(msg, reader) {
      for (var value; reader.nextField() && !reader.isEndGroup(); ) switch (reader.getFieldNumber()) {
       case 1:
        value = reader.readBytes(), msg.addVerifierSignatures(value);
        break;
       case 2:
        value = reader.readBytes(), msg.addSignerIds(value);
        break;
       default:
        reader.skipField();
      }
      return msg;
    }, proto.flow.entities.AggregatedSignature.prototype.serializeBinary = function() {
      var writer = new jspb.BinaryWriter;
      return proto.flow.entities.AggregatedSignature.serializeBinaryToWriter(this, writer), 
      writer.getResultBuffer();
    }, proto.flow.entities.AggregatedSignature.serializeBinaryToWriter = function(message, writer) {
      var f = void 0;
      (f = message.getVerifierSignaturesList_asU8()).length > 0 && writer.writeRepeatedBytes(1, f), 
      (f = message.getSignerIdsList_asU8()).length > 0 && writer.writeRepeatedBytes(2, f);
    }, proto.flow.entities.AggregatedSignature.prototype.getVerifierSignaturesList = function() {
      return jspb.Message.getRepeatedField(this, 1);
    }, proto.flow.entities.AggregatedSignature.prototype.getVerifierSignaturesList_asB64 = function() {
      return jspb.Message.bytesListAsB64(this.getVerifierSignaturesList());
    }, proto.flow.entities.AggregatedSignature.prototype.getVerifierSignaturesList_asU8 = function() {
      return jspb.Message.bytesListAsU8(this.getVerifierSignaturesList());
    }, proto.flow.entities.AggregatedSignature.prototype.setVerifierSignaturesList = function(value) {
      return jspb.Message.setField(this, 1, value || []);
    }, proto.flow.entities.AggregatedSignature.prototype.addVerifierSignatures = function(value, opt_index) {
      return jspb.Message.addToRepeatedField(this, 1, value, opt_index);
    }, proto.flow.entities.AggregatedSignature.prototype.clearVerifierSignaturesList = function() {
      return this.setVerifierSignaturesList([]);
    }, proto.flow.entities.AggregatedSignature.prototype.getSignerIdsList = function() {
      return jspb.Message.getRepeatedField(this, 2);
    }, proto.flow.entities.AggregatedSignature.prototype.getSignerIdsList_asB64 = function() {
      return jspb.Message.bytesListAsB64(this.getSignerIdsList());
    }, proto.flow.entities.AggregatedSignature.prototype.getSignerIdsList_asU8 = function() {
      return jspb.Message.bytesListAsU8(this.getSignerIdsList());
    }, proto.flow.entities.AggregatedSignature.prototype.setSignerIdsList = function(value) {
      return jspb.Message.setField(this, 2, value || []);
    }, proto.flow.entities.AggregatedSignature.prototype.addSignerIds = function(value, opt_index) {
      return jspb.Message.addToRepeatedField(this, 2, value, opt_index);
    }, proto.flow.entities.AggregatedSignature.prototype.clearSignerIdsList = function() {
      return this.setSignerIdsList([]);
    }, goog.object.extend(exports, proto.flow.entities);
  }(block_seal_pb)), block_seal_pb;
}
function requireExecution_result_pb() {
  return hasRequiredExecution_result_pb || (hasRequiredExecution_result_pb = 1, function(exports) {
    var jspb = require$$0, goog = jspb, global = "undefined" != typeof globalThis && globalThis || "undefined" != typeof window && window || void 0 !== global && global || "undefined" != typeof self && self || function() {
      return this;
    }.call(null) || Function("return this")();
    goog.exportSymbol("proto.flow.entities.Chunk", null, global), goog.exportSymbol("proto.flow.entities.ExecutionReceiptMeta", null, global), 
    goog.exportSymbol("proto.flow.entities.ExecutionResult", null, global), goog.exportSymbol("proto.flow.entities.ServiceEvent", null, global), 
    proto.flow.entities.ExecutionResult = function(opt_data) {
      jspb.Message.initialize(this, opt_data, 0, -1, proto.flow.entities.ExecutionResult.repeatedFields_, null);
    }, goog.inherits(proto.flow.entities.ExecutionResult, jspb.Message), goog.DEBUG && !COMPILED && (proto.flow.entities.ExecutionResult.displayName = "proto.flow.entities.ExecutionResult"), 
    proto.flow.entities.Chunk = function(opt_data) {
      jspb.Message.initialize(this, opt_data, 0, -1, null, null);
    }, goog.inherits(proto.flow.entities.Chunk, jspb.Message), goog.DEBUG && !COMPILED && (proto.flow.entities.Chunk.displayName = "proto.flow.entities.Chunk"), 
    proto.flow.entities.ServiceEvent = function(opt_data) {
      jspb.Message.initialize(this, opt_data, 0, -1, null, null);
    }, goog.inherits(proto.flow.entities.ServiceEvent, jspb.Message), goog.DEBUG && !COMPILED && (proto.flow.entities.ServiceEvent.displayName = "proto.flow.entities.ServiceEvent"), 
    proto.flow.entities.ExecutionReceiptMeta = function(opt_data) {
      jspb.Message.initialize(this, opt_data, 0, -1, proto.flow.entities.ExecutionReceiptMeta.repeatedFields_, null);
    }, goog.inherits(proto.flow.entities.ExecutionReceiptMeta, jspb.Message), goog.DEBUG && !COMPILED && (proto.flow.entities.ExecutionReceiptMeta.displayName = "proto.flow.entities.ExecutionReceiptMeta"), 
    proto.flow.entities.ExecutionResult.repeatedFields_ = [ 3, 4 ], jspb.Message.GENERATE_TO_OBJECT && (proto.flow.entities.ExecutionResult.prototype.toObject = function(opt_includeInstance) {
      return proto.flow.entities.ExecutionResult.toObject(opt_includeInstance, this);
    }, proto.flow.entities.ExecutionResult.toObject = function(includeInstance, msg) {
      var obj = {
        previousResultId: msg.getPreviousResultId_asB64(),
        blockId: msg.getBlockId_asB64(),
        chunksList: jspb.Message.toObjectList(msg.getChunksList(), proto.flow.entities.Chunk.toObject, includeInstance),
        serviceEventsList: jspb.Message.toObjectList(msg.getServiceEventsList(), proto.flow.entities.ServiceEvent.toObject, includeInstance),
        executionDataId: msg.getExecutionDataId_asB64()
      };
      return includeInstance && (obj.$jspbMessageInstance = msg), obj;
    }), proto.flow.entities.ExecutionResult.deserializeBinary = function(bytes) {
      var reader = new jspb.BinaryReader(bytes), msg = new proto.flow.entities.ExecutionResult;
      return proto.flow.entities.ExecutionResult.deserializeBinaryFromReader(msg, reader);
    }, proto.flow.entities.ExecutionResult.deserializeBinaryFromReader = function(msg, reader) {
      for (var value; reader.nextField() && !reader.isEndGroup(); ) switch (reader.getFieldNumber()) {
       case 1:
        value = reader.readBytes(), msg.setPreviousResultId(value);
        break;
       case 2:
        value = reader.readBytes(), msg.setBlockId(value);
        break;
       case 3:
        value = new proto.flow.entities.Chunk, reader.readMessage(value, proto.flow.entities.Chunk.deserializeBinaryFromReader), 
        msg.addChunks(value);
        break;
       case 4:
        value = new proto.flow.entities.ServiceEvent, reader.readMessage(value, proto.flow.entities.ServiceEvent.deserializeBinaryFromReader), 
        msg.addServiceEvents(value);
        break;
       case 5:
        value = reader.readBytes(), msg.setExecutionDataId(value);
        break;
       default:
        reader.skipField();
      }
      return msg;
    }, proto.flow.entities.ExecutionResult.prototype.serializeBinary = function() {
      var writer = new jspb.BinaryWriter;
      return proto.flow.entities.ExecutionResult.serializeBinaryToWriter(this, writer), 
      writer.getResultBuffer();
    }, proto.flow.entities.ExecutionResult.serializeBinaryToWriter = function(message, writer) {
      var f = void 0;
      (f = message.getPreviousResultId_asU8()).length > 0 && writer.writeBytes(1, f), 
      (f = message.getBlockId_asU8()).length > 0 && writer.writeBytes(2, f), (f = message.getChunksList()).length > 0 && writer.writeRepeatedMessage(3, f, proto.flow.entities.Chunk.serializeBinaryToWriter), 
      (f = message.getServiceEventsList()).length > 0 && writer.writeRepeatedMessage(4, f, proto.flow.entities.ServiceEvent.serializeBinaryToWriter), 
      (f = message.getExecutionDataId_asU8()).length > 0 && writer.writeBytes(5, f);
    }, proto.flow.entities.ExecutionResult.prototype.getPreviousResultId = function() {
      return jspb.Message.getFieldWithDefault(this, 1, "");
    }, proto.flow.entities.ExecutionResult.prototype.getPreviousResultId_asB64 = function() {
      return jspb.Message.bytesAsB64(this.getPreviousResultId());
    }, proto.flow.entities.ExecutionResult.prototype.getPreviousResultId_asU8 = function() {
      return jspb.Message.bytesAsU8(this.getPreviousResultId());
    }, proto.flow.entities.ExecutionResult.prototype.setPreviousResultId = function(value) {
      return jspb.Message.setProto3BytesField(this, 1, value);
    }, proto.flow.entities.ExecutionResult.prototype.getBlockId = function() {
      return jspb.Message.getFieldWithDefault(this, 2, "");
    }, proto.flow.entities.ExecutionResult.prototype.getBlockId_asB64 = function() {
      return jspb.Message.bytesAsB64(this.getBlockId());
    }, proto.flow.entities.ExecutionResult.prototype.getBlockId_asU8 = function() {
      return jspb.Message.bytesAsU8(this.getBlockId());
    }, proto.flow.entities.ExecutionResult.prototype.setBlockId = function(value) {
      return jspb.Message.setProto3BytesField(this, 2, value);
    }, proto.flow.entities.ExecutionResult.prototype.getChunksList = function() {
      return jspb.Message.getRepeatedWrapperField(this, proto.flow.entities.Chunk, 3);
    }, proto.flow.entities.ExecutionResult.prototype.setChunksList = function(value) {
      return jspb.Message.setRepeatedWrapperField(this, 3, value);
    }, proto.flow.entities.ExecutionResult.prototype.addChunks = function(opt_value, opt_index) {
      return jspb.Message.addToRepeatedWrapperField(this, 3, opt_value, proto.flow.entities.Chunk, opt_index);
    }, proto.flow.entities.ExecutionResult.prototype.clearChunksList = function() {
      return this.setChunksList([]);
    }, proto.flow.entities.ExecutionResult.prototype.getServiceEventsList = function() {
      return jspb.Message.getRepeatedWrapperField(this, proto.flow.entities.ServiceEvent, 4);
    }, proto.flow.entities.ExecutionResult.prototype.setServiceEventsList = function(value) {
      return jspb.Message.setRepeatedWrapperField(this, 4, value);
    }, proto.flow.entities.ExecutionResult.prototype.addServiceEvents = function(opt_value, opt_index) {
      return jspb.Message.addToRepeatedWrapperField(this, 4, opt_value, proto.flow.entities.ServiceEvent, opt_index);
    }, proto.flow.entities.ExecutionResult.prototype.clearServiceEventsList = function() {
      return this.setServiceEventsList([]);
    }, proto.flow.entities.ExecutionResult.prototype.getExecutionDataId = function() {
      return jspb.Message.getFieldWithDefault(this, 5, "");
    }, proto.flow.entities.ExecutionResult.prototype.getExecutionDataId_asB64 = function() {
      return jspb.Message.bytesAsB64(this.getExecutionDataId());
    }, proto.flow.entities.ExecutionResult.prototype.getExecutionDataId_asU8 = function() {
      return jspb.Message.bytesAsU8(this.getExecutionDataId());
    }, proto.flow.entities.ExecutionResult.prototype.setExecutionDataId = function(value) {
      return jspb.Message.setProto3BytesField(this, 5, value);
    }, jspb.Message.GENERATE_TO_OBJECT && (proto.flow.entities.Chunk.prototype.toObject = function(opt_includeInstance) {
      return proto.flow.entities.Chunk.toObject(opt_includeInstance, this);
    }, proto.flow.entities.Chunk.toObject = function(includeInstance, msg) {
      var obj = {
        collectionindex: jspb.Message.getFieldWithDefault(msg, 1, 0),
        startState: msg.getStartState_asB64(),
        eventCollection: msg.getEventCollection_asB64(),
        blockId: msg.getBlockId_asB64(),
        totalComputationUsed: jspb.Message.getFieldWithDefault(msg, 5, 0),
        numberOfTransactions: jspb.Message.getFieldWithDefault(msg, 6, 0),
        index: jspb.Message.getFieldWithDefault(msg, 7, 0),
        endState: msg.getEndState_asB64(),
        executionDataId: msg.getExecutionDataId_asB64(),
        stateDeltaCommitment: msg.getStateDeltaCommitment_asB64()
      };
      return includeInstance && (obj.$jspbMessageInstance = msg), obj;
    }), proto.flow.entities.Chunk.deserializeBinary = function(bytes) {
      var reader = new jspb.BinaryReader(bytes), msg = new proto.flow.entities.Chunk;
      return proto.flow.entities.Chunk.deserializeBinaryFromReader(msg, reader);
    }, proto.flow.entities.Chunk.deserializeBinaryFromReader = function(msg, reader) {
      for (var value; reader.nextField() && !reader.isEndGroup(); ) switch (reader.getFieldNumber()) {
       case 1:
        value = reader.readUint32(), msg.setCollectionindex(value);
        break;
       case 2:
        value = reader.readBytes(), msg.setStartState(value);
        break;
       case 3:
        value = reader.readBytes(), msg.setEventCollection(value);
        break;
       case 4:
        value = reader.readBytes(), msg.setBlockId(value);
        break;
       case 5:
        value = reader.readUint64(), msg.setTotalComputationUsed(value);
        break;
       case 6:
        value = reader.readUint32(), msg.setNumberOfTransactions(value);
        break;
       case 7:
        value = reader.readUint64(), msg.setIndex(value);
        break;
       case 8:
        value = reader.readBytes(), msg.setEndState(value);
        break;
       case 9:
        value = reader.readBytes(), msg.setExecutionDataId(value);
        break;
       case 10:
        value = reader.readBytes(), msg.setStateDeltaCommitment(value);
        break;
       default:
        reader.skipField();
      }
      return msg;
    }, proto.flow.entities.Chunk.prototype.serializeBinary = function() {
      var writer = new jspb.BinaryWriter;
      return proto.flow.entities.Chunk.serializeBinaryToWriter(this, writer), writer.getResultBuffer();
    }, proto.flow.entities.Chunk.serializeBinaryToWriter = function(message, writer) {
      var f = void 0;
      0 !== (f = message.getCollectionindex()) && writer.writeUint32(1, f), (f = message.getStartState_asU8()).length > 0 && writer.writeBytes(2, f), 
      (f = message.getEventCollection_asU8()).length > 0 && writer.writeBytes(3, f), (f = message.getBlockId_asU8()).length > 0 && writer.writeBytes(4, f), 
      0 !== (f = message.getTotalComputationUsed()) && writer.writeUint64(5, f), 0 !== (f = message.getNumberOfTransactions()) && writer.writeUint32(6, f), 
      0 !== (f = message.getIndex()) && writer.writeUint64(7, f), (f = message.getEndState_asU8()).length > 0 && writer.writeBytes(8, f), 
      (f = message.getExecutionDataId_asU8()).length > 0 && writer.writeBytes(9, f), (f = message.getStateDeltaCommitment_asU8()).length > 0 && writer.writeBytes(10, f);
    }, proto.flow.entities.Chunk.prototype.getCollectionindex = function() {
      return jspb.Message.getFieldWithDefault(this, 1, 0);
    }, proto.flow.entities.Chunk.prototype.setCollectionindex = function(value) {
      return jspb.Message.setProto3IntField(this, 1, value);
    }, proto.flow.entities.Chunk.prototype.getStartState = function() {
      return jspb.Message.getFieldWithDefault(this, 2, "");
    }, proto.flow.entities.Chunk.prototype.getStartState_asB64 = function() {
      return jspb.Message.bytesAsB64(this.getStartState());
    }, proto.flow.entities.Chunk.prototype.getStartState_asU8 = function() {
      return jspb.Message.bytesAsU8(this.getStartState());
    }, proto.flow.entities.Chunk.prototype.setStartState = function(value) {
      return jspb.Message.setProto3BytesField(this, 2, value);
    }, proto.flow.entities.Chunk.prototype.getEventCollection = function() {
      return jspb.Message.getFieldWithDefault(this, 3, "");
    }, proto.flow.entities.Chunk.prototype.getEventCollection_asB64 = function() {
      return jspb.Message.bytesAsB64(this.getEventCollection());
    }, proto.flow.entities.Chunk.prototype.getEventCollection_asU8 = function() {
      return jspb.Message.bytesAsU8(this.getEventCollection());
    }, proto.flow.entities.Chunk.prototype.setEventCollection = function(value) {
      return jspb.Message.setProto3BytesField(this, 3, value);
    }, proto.flow.entities.Chunk.prototype.getBlockId = function() {
      return jspb.Message.getFieldWithDefault(this, 4, "");
    }, proto.flow.entities.Chunk.prototype.getBlockId_asB64 = function() {
      return jspb.Message.bytesAsB64(this.getBlockId());
    }, proto.flow.entities.Chunk.prototype.getBlockId_asU8 = function() {
      return jspb.Message.bytesAsU8(this.getBlockId());
    }, proto.flow.entities.Chunk.prototype.setBlockId = function(value) {
      return jspb.Message.setProto3BytesField(this, 4, value);
    }, proto.flow.entities.Chunk.prototype.getTotalComputationUsed = function() {
      return jspb.Message.getFieldWithDefault(this, 5, 0);
    }, proto.flow.entities.Chunk.prototype.setTotalComputationUsed = function(value) {
      return jspb.Message.setProto3IntField(this, 5, value);
    }, proto.flow.entities.Chunk.prototype.getNumberOfTransactions = function() {
      return jspb.Message.getFieldWithDefault(this, 6, 0);
    }, proto.flow.entities.Chunk.prototype.setNumberOfTransactions = function(value) {
      return jspb.Message.setProto3IntField(this, 6, value);
    }, proto.flow.entities.Chunk.prototype.getIndex = function() {
      return jspb.Message.getFieldWithDefault(this, 7, 0);
    }, proto.flow.entities.Chunk.prototype.setIndex = function(value) {
      return jspb.Message.setProto3IntField(this, 7, value);
    }, proto.flow.entities.Chunk.prototype.getEndState = function() {
      return jspb.Message.getFieldWithDefault(this, 8, "");
    }, proto.flow.entities.Chunk.prototype.getEndState_asB64 = function() {
      return jspb.Message.bytesAsB64(this.getEndState());
    }, proto.flow.entities.Chunk.prototype.getEndState_asU8 = function() {
      return jspb.Message.bytesAsU8(this.getEndState());
    }, proto.flow.entities.Chunk.prototype.setEndState = function(value) {
      return jspb.Message.setProto3BytesField(this, 8, value);
    }, proto.flow.entities.Chunk.prototype.getExecutionDataId = function() {
      return jspb.Message.getFieldWithDefault(this, 9, "");
    }, proto.flow.entities.Chunk.prototype.getExecutionDataId_asB64 = function() {
      return jspb.Message.bytesAsB64(this.getExecutionDataId());
    }, proto.flow.entities.Chunk.prototype.getExecutionDataId_asU8 = function() {
      return jspb.Message.bytesAsU8(this.getExecutionDataId());
    }, proto.flow.entities.Chunk.prototype.setExecutionDataId = function(value) {
      return jspb.Message.setProto3BytesField(this, 9, value);
    }, proto.flow.entities.Chunk.prototype.getStateDeltaCommitment = function() {
      return jspb.Message.getFieldWithDefault(this, 10, "");
    }, proto.flow.entities.Chunk.prototype.getStateDeltaCommitment_asB64 = function() {
      return jspb.Message.bytesAsB64(this.getStateDeltaCommitment());
    }, proto.flow.entities.Chunk.prototype.getStateDeltaCommitment_asU8 = function() {
      return jspb.Message.bytesAsU8(this.getStateDeltaCommitment());
    }, proto.flow.entities.Chunk.prototype.setStateDeltaCommitment = function(value) {
      return jspb.Message.setProto3BytesField(this, 10, value);
    }, jspb.Message.GENERATE_TO_OBJECT && (proto.flow.entities.ServiceEvent.prototype.toObject = function(opt_includeInstance) {
      return proto.flow.entities.ServiceEvent.toObject(opt_includeInstance, this);
    }, proto.flow.entities.ServiceEvent.toObject = function(includeInstance, msg) {
      var obj = {
        type: jspb.Message.getFieldWithDefault(msg, 1, ""),
        payload: msg.getPayload_asB64()
      };
      return includeInstance && (obj.$jspbMessageInstance = msg), obj;
    }), proto.flow.entities.ServiceEvent.deserializeBinary = function(bytes) {
      var reader = new jspb.BinaryReader(bytes), msg = new proto.flow.entities.ServiceEvent;
      return proto.flow.entities.ServiceEvent.deserializeBinaryFromReader(msg, reader);
    }, proto.flow.entities.ServiceEvent.deserializeBinaryFromReader = function(msg, reader) {
      for (var value; reader.nextField() && !reader.isEndGroup(); ) switch (reader.getFieldNumber()) {
       case 1:
        value = reader.readString(), msg.setType(value);
        break;
       case 2:
        value = reader.readBytes(), msg.setPayload(value);
        break;
       default:
        reader.skipField();
      }
      return msg;
    }, proto.flow.entities.ServiceEvent.prototype.serializeBinary = function() {
      var writer = new jspb.BinaryWriter;
      return proto.flow.entities.ServiceEvent.serializeBinaryToWriter(this, writer), writer.getResultBuffer();
    }, proto.flow.entities.ServiceEvent.serializeBinaryToWriter = function(message, writer) {
      var f = void 0;
      (f = message.getType()).length > 0 && writer.writeString(1, f), (f = message.getPayload_asU8()).length > 0 && writer.writeBytes(2, f);
    }, proto.flow.entities.ServiceEvent.prototype.getType = function() {
      return jspb.Message.getFieldWithDefault(this, 1, "");
    }, proto.flow.entities.ServiceEvent.prototype.setType = function(value) {
      return jspb.Message.setProto3StringField(this, 1, value);
    }, proto.flow.entities.ServiceEvent.prototype.getPayload = function() {
      return jspb.Message.getFieldWithDefault(this, 2, "");
    }, proto.flow.entities.ServiceEvent.prototype.getPayload_asB64 = function() {
      return jspb.Message.bytesAsB64(this.getPayload());
    }, proto.flow.entities.ServiceEvent.prototype.getPayload_asU8 = function() {
      return jspb.Message.bytesAsU8(this.getPayload());
    }, proto.flow.entities.ServiceEvent.prototype.setPayload = function(value) {
      return jspb.Message.setProto3BytesField(this, 2, value);
    }, proto.flow.entities.ExecutionReceiptMeta.repeatedFields_ = [ 3 ], jspb.Message.GENERATE_TO_OBJECT && (proto.flow.entities.ExecutionReceiptMeta.prototype.toObject = function(opt_includeInstance) {
      return proto.flow.entities.ExecutionReceiptMeta.toObject(opt_includeInstance, this);
    }, proto.flow.entities.ExecutionReceiptMeta.toObject = function(includeInstance, msg) {
      var obj = {
        executorId: msg.getExecutorId_asB64(),
        resultId: msg.getResultId_asB64(),
        spocksList: msg.getSpocksList_asB64(),
        executorSignature: msg.getExecutorSignature_asB64()
      };
      return includeInstance && (obj.$jspbMessageInstance = msg), obj;
    }), proto.flow.entities.ExecutionReceiptMeta.deserializeBinary = function(bytes) {
      var reader = new jspb.BinaryReader(bytes), msg = new proto.flow.entities.ExecutionReceiptMeta;
      return proto.flow.entities.ExecutionReceiptMeta.deserializeBinaryFromReader(msg, reader);
    }, proto.flow.entities.ExecutionReceiptMeta.deserializeBinaryFromReader = function(msg, reader) {
      for (var value; reader.nextField() && !reader.isEndGroup(); ) switch (reader.getFieldNumber()) {
       case 1:
        value = reader.readBytes(), msg.setExecutorId(value);
        break;
       case 2:
        value = reader.readBytes(), msg.setResultId(value);
        break;
       case 3:
        value = reader.readBytes(), msg.addSpocks(value);
        break;
       case 4:
        value = reader.readBytes(), msg.setExecutorSignature(value);
        break;
       default:
        reader.skipField();
      }
      return msg;
    }, proto.flow.entities.ExecutionReceiptMeta.prototype.serializeBinary = function() {
      var writer = new jspb.BinaryWriter;
      return proto.flow.entities.ExecutionReceiptMeta.serializeBinaryToWriter(this, writer), 
      writer.getResultBuffer();
    }, proto.flow.entities.ExecutionReceiptMeta.serializeBinaryToWriter = function(message, writer) {
      var f = void 0;
      (f = message.getExecutorId_asU8()).length > 0 && writer.writeBytes(1, f), (f = message.getResultId_asU8()).length > 0 && writer.writeBytes(2, f), 
      (f = message.getSpocksList_asU8()).length > 0 && writer.writeRepeatedBytes(3, f), 
      (f = message.getExecutorSignature_asU8()).length > 0 && writer.writeBytes(4, f);
    }, proto.flow.entities.ExecutionReceiptMeta.prototype.getExecutorId = function() {
      return jspb.Message.getFieldWithDefault(this, 1, "");
    }, proto.flow.entities.ExecutionReceiptMeta.prototype.getExecutorId_asB64 = function() {
      return jspb.Message.bytesAsB64(this.getExecutorId());
    }, proto.flow.entities.ExecutionReceiptMeta.prototype.getExecutorId_asU8 = function() {
      return jspb.Message.bytesAsU8(this.getExecutorId());
    }, proto.flow.entities.ExecutionReceiptMeta.prototype.setExecutorId = function(value) {
      return jspb.Message.setProto3BytesField(this, 1, value);
    }, proto.flow.entities.ExecutionReceiptMeta.prototype.getResultId = function() {
      return jspb.Message.getFieldWithDefault(this, 2, "");
    }, proto.flow.entities.ExecutionReceiptMeta.prototype.getResultId_asB64 = function() {
      return jspb.Message.bytesAsB64(this.getResultId());
    }, proto.flow.entities.ExecutionReceiptMeta.prototype.getResultId_asU8 = function() {
      return jspb.Message.bytesAsU8(this.getResultId());
    }, proto.flow.entities.ExecutionReceiptMeta.prototype.setResultId = function(value) {
      return jspb.Message.setProto3BytesField(this, 2, value);
    }, proto.flow.entities.ExecutionReceiptMeta.prototype.getSpocksList = function() {
      return jspb.Message.getRepeatedField(this, 3);
    }, proto.flow.entities.ExecutionReceiptMeta.prototype.getSpocksList_asB64 = function() {
      return jspb.Message.bytesListAsB64(this.getSpocksList());
    }, proto.flow.entities.ExecutionReceiptMeta.prototype.getSpocksList_asU8 = function() {
      return jspb.Message.bytesListAsU8(this.getSpocksList());
    }, proto.flow.entities.ExecutionReceiptMeta.prototype.setSpocksList = function(value) {
      return jspb.Message.setField(this, 3, value || []);
    }, proto.flow.entities.ExecutionReceiptMeta.prototype.addSpocks = function(value, opt_index) {
      return jspb.Message.addToRepeatedField(this, 3, value, opt_index);
    }, proto.flow.entities.ExecutionReceiptMeta.prototype.clearSpocksList = function() {
      return this.setSpocksList([]);
    }, proto.flow.entities.ExecutionReceiptMeta.prototype.getExecutorSignature = function() {
      return jspb.Message.getFieldWithDefault(this, 4, "");
    }, proto.flow.entities.ExecutionReceiptMeta.prototype.getExecutorSignature_asB64 = function() {
      return jspb.Message.bytesAsB64(this.getExecutorSignature());
    }, proto.flow.entities.ExecutionReceiptMeta.prototype.getExecutorSignature_asU8 = function() {
      return jspb.Message.bytesAsU8(this.getExecutorSignature());
    }, proto.flow.entities.ExecutionReceiptMeta.prototype.setExecutorSignature = function(value) {
      return jspb.Message.setProto3BytesField(this, 4, value);
    }, goog.object.extend(exports, proto.flow.entities);
  }(execution_result_pb)), execution_result_pb;
}
function requireBlock_pb() {
  return hasRequiredBlock_pb || (hasRequiredBlock_pb = 1, function(exports) {
    var flow_entities_collection_pb, flow_entities_block_seal_pb, flow_entities_execution_result_pb, flow_entities_block_header_pb, jspb = require$$0, goog = jspb, global = "undefined" != typeof globalThis && globalThis || "undefined" != typeof window && window || void 0 !== global && global || "undefined" != typeof self && self || function() {
      return this;
    }.call(null) || Function("return this")(), google_protobuf_timestamp_pb = require$$10;
    goog.object.extend(proto, google_protobuf_timestamp_pb), flow_entities_collection_pb = requireCollection_pb(), 
    goog.object.extend(proto, flow_entities_collection_pb), flow_entities_block_seal_pb = requireBlock_seal_pb(), 
    goog.object.extend(proto, flow_entities_block_seal_pb), flow_entities_execution_result_pb = requireExecution_result_pb(), 
    goog.object.extend(proto, flow_entities_execution_result_pb), flow_entities_block_header_pb = requireBlock_header_pb(), 
    goog.object.extend(proto, flow_entities_block_header_pb), goog.exportSymbol("proto.flow.entities.Block", null, global), 
    goog.exportSymbol("proto.flow.entities.BlockStatus", null, global), proto.flow.entities.Block = function(opt_data) {
      jspb.Message.initialize(this, opt_data, 0, -1, proto.flow.entities.Block.repeatedFields_, null);
    }, goog.inherits(proto.flow.entities.Block, jspb.Message), goog.DEBUG && !COMPILED && (proto.flow.entities.Block.displayName = "proto.flow.entities.Block"), 
    proto.flow.entities.Block.repeatedFields_ = [ 5, 6, 7, 8, 9 ], jspb.Message.GENERATE_TO_OBJECT && (proto.flow.entities.Block.prototype.toObject = function(opt_includeInstance) {
      return proto.flow.entities.Block.toObject(opt_includeInstance, this);
    }, proto.flow.entities.Block.toObject = function(includeInstance, msg) {
      var f, obj = {
        id: msg.getId_asB64(),
        parentId: msg.getParentId_asB64(),
        height: jspb.Message.getFieldWithDefault(msg, 3, 0),
        timestamp: (f = msg.getTimestamp()) && google_protobuf_timestamp_pb.Timestamp.toObject(includeInstance, f),
        collectionGuaranteesList: jspb.Message.toObjectList(msg.getCollectionGuaranteesList(), flow_entities_collection_pb.CollectionGuarantee.toObject, includeInstance),
        blockSealsList: jspb.Message.toObjectList(msg.getBlockSealsList(), flow_entities_block_seal_pb.BlockSeal.toObject, includeInstance),
        signaturesList: msg.getSignaturesList_asB64(),
        executionReceiptMetalistList: jspb.Message.toObjectList(msg.getExecutionReceiptMetalistList(), flow_entities_execution_result_pb.ExecutionReceiptMeta.toObject, includeInstance),
        executionResultListList: jspb.Message.toObjectList(msg.getExecutionResultListList(), flow_entities_execution_result_pb.ExecutionResult.toObject, includeInstance),
        blockHeader: (f = msg.getBlockHeader()) && flow_entities_block_header_pb.BlockHeader.toObject(includeInstance, f),
        protocolStateId: msg.getProtocolStateId_asB64()
      };
      return includeInstance && (obj.$jspbMessageInstance = msg), obj;
    }), proto.flow.entities.Block.deserializeBinary = function(bytes) {
      var reader = new jspb.BinaryReader(bytes), msg = new proto.flow.entities.Block;
      return proto.flow.entities.Block.deserializeBinaryFromReader(msg, reader);
    }, proto.flow.entities.Block.deserializeBinaryFromReader = function(msg, reader) {
      for (var value; reader.nextField() && !reader.isEndGroup(); ) switch (reader.getFieldNumber()) {
       case 1:
        value = reader.readBytes(), msg.setId(value);
        break;
       case 2:
        value = reader.readBytes(), msg.setParentId(value);
        break;
       case 3:
        value = reader.readUint64(), msg.setHeight(value);
        break;
       case 4:
        value = new google_protobuf_timestamp_pb.Timestamp, reader.readMessage(value, google_protobuf_timestamp_pb.Timestamp.deserializeBinaryFromReader), 
        msg.setTimestamp(value);
        break;
       case 5:
        value = new flow_entities_collection_pb.CollectionGuarantee, reader.readMessage(value, flow_entities_collection_pb.CollectionGuarantee.deserializeBinaryFromReader), 
        msg.addCollectionGuarantees(value);
        break;
       case 6:
        value = new flow_entities_block_seal_pb.BlockSeal, reader.readMessage(value, flow_entities_block_seal_pb.BlockSeal.deserializeBinaryFromReader), 
        msg.addBlockSeals(value);
        break;
       case 7:
        value = reader.readBytes(), msg.addSignatures(value);
        break;
       case 8:
        value = new flow_entities_execution_result_pb.ExecutionReceiptMeta, reader.readMessage(value, flow_entities_execution_result_pb.ExecutionReceiptMeta.deserializeBinaryFromReader), 
        msg.addExecutionReceiptMetalist(value);
        break;
       case 9:
        value = new flow_entities_execution_result_pb.ExecutionResult, reader.readMessage(value, flow_entities_execution_result_pb.ExecutionResult.deserializeBinaryFromReader), 
        msg.addExecutionResultList(value);
        break;
       case 10:
        value = new flow_entities_block_header_pb.BlockHeader, reader.readMessage(value, flow_entities_block_header_pb.BlockHeader.deserializeBinaryFromReader), 
        msg.setBlockHeader(value);
        break;
       case 11:
        value = reader.readBytes(), msg.setProtocolStateId(value);
        break;
       default:
        reader.skipField();
      }
      return msg;
    }, proto.flow.entities.Block.prototype.serializeBinary = function() {
      var writer = new jspb.BinaryWriter;
      return proto.flow.entities.Block.serializeBinaryToWriter(this, writer), writer.getResultBuffer();
    }, proto.flow.entities.Block.serializeBinaryToWriter = function(message, writer) {
      var f = void 0;
      (f = message.getId_asU8()).length > 0 && writer.writeBytes(1, f), (f = message.getParentId_asU8()).length > 0 && writer.writeBytes(2, f), 
      0 !== (f = message.getHeight()) && writer.writeUint64(3, f), null != (f = message.getTimestamp()) && writer.writeMessage(4, f, google_protobuf_timestamp_pb.Timestamp.serializeBinaryToWriter), 
      (f = message.getCollectionGuaranteesList()).length > 0 && writer.writeRepeatedMessage(5, f, flow_entities_collection_pb.CollectionGuarantee.serializeBinaryToWriter), 
      (f = message.getBlockSealsList()).length > 0 && writer.writeRepeatedMessage(6, f, flow_entities_block_seal_pb.BlockSeal.serializeBinaryToWriter), 
      (f = message.getSignaturesList_asU8()).length > 0 && writer.writeRepeatedBytes(7, f), 
      (f = message.getExecutionReceiptMetalistList()).length > 0 && writer.writeRepeatedMessage(8, f, flow_entities_execution_result_pb.ExecutionReceiptMeta.serializeBinaryToWriter), 
      (f = message.getExecutionResultListList()).length > 0 && writer.writeRepeatedMessage(9, f, flow_entities_execution_result_pb.ExecutionResult.serializeBinaryToWriter), 
      null != (f = message.getBlockHeader()) && writer.writeMessage(10, f, flow_entities_block_header_pb.BlockHeader.serializeBinaryToWriter), 
      (f = message.getProtocolStateId_asU8()).length > 0 && writer.writeBytes(11, f);
    }, proto.flow.entities.Block.prototype.getId = function() {
      return jspb.Message.getFieldWithDefault(this, 1, "");
    }, proto.flow.entities.Block.prototype.getId_asB64 = function() {
      return jspb.Message.bytesAsB64(this.getId());
    }, proto.flow.entities.Block.prototype.getId_asU8 = function() {
      return jspb.Message.bytesAsU8(this.getId());
    }, proto.flow.entities.Block.prototype.setId = function(value) {
      return jspb.Message.setProto3BytesField(this, 1, value);
    }, proto.flow.entities.Block.prototype.getParentId = function() {
      return jspb.Message.getFieldWithDefault(this, 2, "");
    }, proto.flow.entities.Block.prototype.getParentId_asB64 = function() {
      return jspb.Message.bytesAsB64(this.getParentId());
    }, proto.flow.entities.Block.prototype.getParentId_asU8 = function() {
      return jspb.Message.bytesAsU8(this.getParentId());
    }, proto.flow.entities.Block.prototype.setParentId = function(value) {
      return jspb.Message.setProto3BytesField(this, 2, value);
    }, proto.flow.entities.Block.prototype.getHeight = function() {
      return jspb.Message.getFieldWithDefault(this, 3, 0);
    }, proto.flow.entities.Block.prototype.setHeight = function(value) {
      return jspb.Message.setProto3IntField(this, 3, value);
    }, proto.flow.entities.Block.prototype.getTimestamp = function() {
      return jspb.Message.getWrapperField(this, google_protobuf_timestamp_pb.Timestamp, 4);
    }, proto.flow.entities.Block.prototype.setTimestamp = function(value) {
      return jspb.Message.setWrapperField(this, 4, value);
    }, proto.flow.entities.Block.prototype.clearTimestamp = function() {
      return this.setTimestamp(void 0);
    }, proto.flow.entities.Block.prototype.hasTimestamp = function() {
      return null != jspb.Message.getField(this, 4);
    }, proto.flow.entities.Block.prototype.getCollectionGuaranteesList = function() {
      return jspb.Message.getRepeatedWrapperField(this, flow_entities_collection_pb.CollectionGuarantee, 5);
    }, proto.flow.entities.Block.prototype.setCollectionGuaranteesList = function(value) {
      return jspb.Message.setRepeatedWrapperField(this, 5, value);
    }, proto.flow.entities.Block.prototype.addCollectionGuarantees = function(opt_value, opt_index) {
      return jspb.Message.addToRepeatedWrapperField(this, 5, opt_value, proto.flow.entities.CollectionGuarantee, opt_index);
    }, proto.flow.entities.Block.prototype.clearCollectionGuaranteesList = function() {
      return this.setCollectionGuaranteesList([]);
    }, proto.flow.entities.Block.prototype.getBlockSealsList = function() {
      return jspb.Message.getRepeatedWrapperField(this, flow_entities_block_seal_pb.BlockSeal, 6);
    }, proto.flow.entities.Block.prototype.setBlockSealsList = function(value) {
      return jspb.Message.setRepeatedWrapperField(this, 6, value);
    }, proto.flow.entities.Block.prototype.addBlockSeals = function(opt_value, opt_index) {
      return jspb.Message.addToRepeatedWrapperField(this, 6, opt_value, proto.flow.entities.BlockSeal, opt_index);
    }, proto.flow.entities.Block.prototype.clearBlockSealsList = function() {
      return this.setBlockSealsList([]);
    }, proto.flow.entities.Block.prototype.getSignaturesList = function() {
      return jspb.Message.getRepeatedField(this, 7);
    }, proto.flow.entities.Block.prototype.getSignaturesList_asB64 = function() {
      return jspb.Message.bytesListAsB64(this.getSignaturesList());
    }, proto.flow.entities.Block.prototype.getSignaturesList_asU8 = function() {
      return jspb.Message.bytesListAsU8(this.getSignaturesList());
    }, proto.flow.entities.Block.prototype.setSignaturesList = function(value) {
      return jspb.Message.setField(this, 7, value || []);
    }, proto.flow.entities.Block.prototype.addSignatures = function(value, opt_index) {
      return jspb.Message.addToRepeatedField(this, 7, value, opt_index);
    }, proto.flow.entities.Block.prototype.clearSignaturesList = function() {
      return this.setSignaturesList([]);
    }, proto.flow.entities.Block.prototype.getExecutionReceiptMetalistList = function() {
      return jspb.Message.getRepeatedWrapperField(this, flow_entities_execution_result_pb.ExecutionReceiptMeta, 8);
    }, proto.flow.entities.Block.prototype.setExecutionReceiptMetalistList = function(value) {
      return jspb.Message.setRepeatedWrapperField(this, 8, value);
    }, proto.flow.entities.Block.prototype.addExecutionReceiptMetalist = function(opt_value, opt_index) {
      return jspb.Message.addToRepeatedWrapperField(this, 8, opt_value, proto.flow.entities.ExecutionReceiptMeta, opt_index);
    }, proto.flow.entities.Block.prototype.clearExecutionReceiptMetalistList = function() {
      return this.setExecutionReceiptMetalistList([]);
    }, proto.flow.entities.Block.prototype.getExecutionResultListList = function() {
      return jspb.Message.getRepeatedWrapperField(this, flow_entities_execution_result_pb.ExecutionResult, 9);
    }, proto.flow.entities.Block.prototype.setExecutionResultListList = function(value) {
      return jspb.Message.setRepeatedWrapperField(this, 9, value);
    }, proto.flow.entities.Block.prototype.addExecutionResultList = function(opt_value, opt_index) {
      return jspb.Message.addToRepeatedWrapperField(this, 9, opt_value, proto.flow.entities.ExecutionResult, opt_index);
    }, proto.flow.entities.Block.prototype.clearExecutionResultListList = function() {
      return this.setExecutionResultListList([]);
    }, proto.flow.entities.Block.prototype.getBlockHeader = function() {
      return jspb.Message.getWrapperField(this, flow_entities_block_header_pb.BlockHeader, 10);
    }, proto.flow.entities.Block.prototype.setBlockHeader = function(value) {
      return jspb.Message.setWrapperField(this, 10, value);
    }, proto.flow.entities.Block.prototype.clearBlockHeader = function() {
      return this.setBlockHeader(void 0);
    }, proto.flow.entities.Block.prototype.hasBlockHeader = function() {
      return null != jspb.Message.getField(this, 10);
    }, proto.flow.entities.Block.prototype.getProtocolStateId = function() {
      return jspb.Message.getFieldWithDefault(this, 11, "");
    }, proto.flow.entities.Block.prototype.getProtocolStateId_asB64 = function() {
      return jspb.Message.bytesAsB64(this.getProtocolStateId());
    }, proto.flow.entities.Block.prototype.getProtocolStateId_asU8 = function() {
      return jspb.Message.bytesAsU8(this.getProtocolStateId());
    }, proto.flow.entities.Block.prototype.setProtocolStateId = function(value) {
      return jspb.Message.setProto3BytesField(this, 11, value);
    }, proto.flow.entities.BlockStatus = {
      BLOCK_UNKNOWN: 0,
      BLOCK_FINALIZED: 1,
      BLOCK_SEALED: 2
    }, goog.object.extend(exports, proto.flow.entities);
  }(block_pb)), block_pb;
}
function requireMetadata_pb() {
  return hasRequiredMetadata_pb || (hasRequiredMetadata_pb = 1, function(exports) {
    var jspb = require$$0, goog = jspb, global = "undefined" != typeof globalThis && globalThis || "undefined" != typeof window && window || void 0 !== global && global || "undefined" != typeof self && self || function() {
      return this;
    }.call(null) || Function("return this")();
    goog.exportSymbol("proto.flow.entities.Metadata", null, global), proto.flow.entities.Metadata = function(opt_data) {
      jspb.Message.initialize(this, opt_data, 0, -1, null, null);
    }, goog.inherits(proto.flow.entities.Metadata, jspb.Message), goog.DEBUG && !COMPILED && (proto.flow.entities.Metadata.displayName = "proto.flow.entities.Metadata"), 
    jspb.Message.GENERATE_TO_OBJECT && (proto.flow.entities.Metadata.prototype.toObject = function(opt_includeInstance) {
      return proto.flow.entities.Metadata.toObject(opt_includeInstance, this);
    }, proto.flow.entities.Metadata.toObject = function(includeInstance, msg) {
      var obj = {
        latestFinalizedBlockId: msg.getLatestFinalizedBlockId_asB64(),
        latestFinalizedHeight: jspb.Message.getFieldWithDefault(msg, 2, 0),
        nodeId: msg.getNodeId_asB64()
      };
      return includeInstance && (obj.$jspbMessageInstance = msg), obj;
    }), proto.flow.entities.Metadata.deserializeBinary = function(bytes) {
      var reader = new jspb.BinaryReader(bytes), msg = new proto.flow.entities.Metadata;
      return proto.flow.entities.Metadata.deserializeBinaryFromReader(msg, reader);
    }, proto.flow.entities.Metadata.deserializeBinaryFromReader = function(msg, reader) {
      for (var value; reader.nextField() && !reader.isEndGroup(); ) switch (reader.getFieldNumber()) {
       case 1:
        value = reader.readBytes(), msg.setLatestFinalizedBlockId(value);
        break;
       case 2:
        value = reader.readUint64(), msg.setLatestFinalizedHeight(value);
        break;
       case 3:
        value = reader.readBytes(), msg.setNodeId(value);
        break;
       default:
        reader.skipField();
      }
      return msg;
    }, proto.flow.entities.Metadata.prototype.serializeBinary = function() {
      var writer = new jspb.BinaryWriter;
      return proto.flow.entities.Metadata.serializeBinaryToWriter(this, writer), writer.getResultBuffer();
    }, proto.flow.entities.Metadata.serializeBinaryToWriter = function(message, writer) {
      var f = void 0;
      (f = message.getLatestFinalizedBlockId_asU8()).length > 0 && writer.writeBytes(1, f), 
      0 !== (f = message.getLatestFinalizedHeight()) && writer.writeUint64(2, f), (f = message.getNodeId_asU8()).length > 0 && writer.writeBytes(3, f);
    }, proto.flow.entities.Metadata.prototype.getLatestFinalizedBlockId = function() {
      return jspb.Message.getFieldWithDefault(this, 1, "");
    }, proto.flow.entities.Metadata.prototype.getLatestFinalizedBlockId_asB64 = function() {
      return jspb.Message.bytesAsB64(this.getLatestFinalizedBlockId());
    }, proto.flow.entities.Metadata.prototype.getLatestFinalizedBlockId_asU8 = function() {
      return jspb.Message.bytesAsU8(this.getLatestFinalizedBlockId());
    }, proto.flow.entities.Metadata.prototype.setLatestFinalizedBlockId = function(value) {
      return jspb.Message.setProto3BytesField(this, 1, value);
    }, proto.flow.entities.Metadata.prototype.getLatestFinalizedHeight = function() {
      return jspb.Message.getFieldWithDefault(this, 2, 0);
    }, proto.flow.entities.Metadata.prototype.setLatestFinalizedHeight = function(value) {
      return jspb.Message.setProto3IntField(this, 2, value);
    }, proto.flow.entities.Metadata.prototype.getNodeId = function() {
      return jspb.Message.getFieldWithDefault(this, 3, "");
    }, proto.flow.entities.Metadata.prototype.getNodeId_asB64 = function() {
      return jspb.Message.bytesAsB64(this.getNodeId());
    }, proto.flow.entities.Metadata.prototype.getNodeId_asU8 = function() {
      return jspb.Message.bytesAsU8(this.getNodeId());
    }, proto.flow.entities.Metadata.prototype.setNodeId = function(value) {
      return jspb.Message.setProto3BytesField(this, 3, value);
    }, goog.object.extend(exports, proto.flow.entities);
  }(metadata_pb)), metadata_pb;
}
function requireNode_version_info_pb() {
  return hasRequiredNode_version_info_pb || (hasRequiredNode_version_info_pb = 1, 
  function(exports) {
    var jspb = require$$0, goog = jspb, global = "undefined" != typeof globalThis && globalThis || "undefined" != typeof window && window || void 0 !== global && global || "undefined" != typeof self && self || function() {
      return this;
    }.call(null) || Function("return this")();
    goog.exportSymbol("proto.flow.entities.NodeVersionInfo", null, global), proto.flow.entities.NodeVersionInfo = function(opt_data) {
      jspb.Message.initialize(this, opt_data, 0, -1, null, null);
    }, goog.inherits(proto.flow.entities.NodeVersionInfo, jspb.Message), goog.DEBUG && !COMPILED && (proto.flow.entities.NodeVersionInfo.displayName = "proto.flow.entities.NodeVersionInfo"), 
    jspb.Message.GENERATE_TO_OBJECT && (proto.flow.entities.NodeVersionInfo.prototype.toObject = function(opt_includeInstance) {
      return proto.flow.entities.NodeVersionInfo.toObject(opt_includeInstance, this);
    }, proto.flow.entities.NodeVersionInfo.toObject = function(includeInstance, msg) {
      var obj = {
        semver: jspb.Message.getFieldWithDefault(msg, 1, ""),
        commit: jspb.Message.getFieldWithDefault(msg, 2, ""),
        sporkId: msg.getSporkId_asB64(),
        protocolVersion: jspb.Message.getFieldWithDefault(msg, 4, 0),
        sporkRootBlockHeight: jspb.Message.getFieldWithDefault(msg, 5, 0),
        nodeRootBlockHeight: jspb.Message.getFieldWithDefault(msg, 6, 0)
      };
      return includeInstance && (obj.$jspbMessageInstance = msg), obj;
    }), proto.flow.entities.NodeVersionInfo.deserializeBinary = function(bytes) {
      var reader = new jspb.BinaryReader(bytes), msg = new proto.flow.entities.NodeVersionInfo;
      return proto.flow.entities.NodeVersionInfo.deserializeBinaryFromReader(msg, reader);
    }, proto.flow.entities.NodeVersionInfo.deserializeBinaryFromReader = function(msg, reader) {
      for (var value; reader.nextField() && !reader.isEndGroup(); ) switch (reader.getFieldNumber()) {
       case 1:
        value = reader.readString(), msg.setSemver(value);
        break;
       case 2:
        value = reader.readString(), msg.setCommit(value);
        break;
       case 3:
        value = reader.readBytes(), msg.setSporkId(value);
        break;
       case 4:
        value = reader.readUint64(), msg.setProtocolVersion(value);
        break;
       case 5:
        value = reader.readUint64(), msg.setSporkRootBlockHeight(value);
        break;
       case 6:
        value = reader.readUint64(), msg.setNodeRootBlockHeight(value);
        break;
       default:
        reader.skipField();
      }
      return msg;
    }, proto.flow.entities.NodeVersionInfo.prototype.serializeBinary = function() {
      var writer = new jspb.BinaryWriter;
      return proto.flow.entities.NodeVersionInfo.serializeBinaryToWriter(this, writer), 
      writer.getResultBuffer();
    }, proto.flow.entities.NodeVersionInfo.serializeBinaryToWriter = function(message, writer) {
      var f = void 0;
      (f = message.getSemver()).length > 0 && writer.writeString(1, f), (f = message.getCommit()).length > 0 && writer.writeString(2, f), 
      (f = message.getSporkId_asU8()).length > 0 && writer.writeBytes(3, f), 0 !== (f = message.getProtocolVersion()) && writer.writeUint64(4, f), 
      0 !== (f = message.getSporkRootBlockHeight()) && writer.writeUint64(5, f), 0 !== (f = message.getNodeRootBlockHeight()) && writer.writeUint64(6, f);
    }, proto.flow.entities.NodeVersionInfo.prototype.getSemver = function() {
      return jspb.Message.getFieldWithDefault(this, 1, "");
    }, proto.flow.entities.NodeVersionInfo.prototype.setSemver = function(value) {
      return jspb.Message.setProto3StringField(this, 1, value);
    }, proto.flow.entities.NodeVersionInfo.prototype.getCommit = function() {
      return jspb.Message.getFieldWithDefault(this, 2, "");
    }, proto.flow.entities.NodeVersionInfo.prototype.setCommit = function(value) {
      return jspb.Message.setProto3StringField(this, 2, value);
    }, proto.flow.entities.NodeVersionInfo.prototype.getSporkId = function() {
      return jspb.Message.getFieldWithDefault(this, 3, "");
    }, proto.flow.entities.NodeVersionInfo.prototype.getSporkId_asB64 = function() {
      return jspb.Message.bytesAsB64(this.getSporkId());
    }, proto.flow.entities.NodeVersionInfo.prototype.getSporkId_asU8 = function() {
      return jspb.Message.bytesAsU8(this.getSporkId());
    }, proto.flow.entities.NodeVersionInfo.prototype.setSporkId = function(value) {
      return jspb.Message.setProto3BytesField(this, 3, value);
    }, proto.flow.entities.NodeVersionInfo.prototype.getProtocolVersion = function() {
      return jspb.Message.getFieldWithDefault(this, 4, 0);
    }, proto.flow.entities.NodeVersionInfo.prototype.setProtocolVersion = function(value) {
      return jspb.Message.setProto3IntField(this, 4, value);
    }, proto.flow.entities.NodeVersionInfo.prototype.getSporkRootBlockHeight = function() {
      return jspb.Message.getFieldWithDefault(this, 5, 0);
    }, proto.flow.entities.NodeVersionInfo.prototype.setSporkRootBlockHeight = function(value) {
      return jspb.Message.setProto3IntField(this, 5, value);
    }, proto.flow.entities.NodeVersionInfo.prototype.getNodeRootBlockHeight = function() {
      return jspb.Message.getFieldWithDefault(this, 6, 0);
    }, proto.flow.entities.NodeVersionInfo.prototype.setNodeRootBlockHeight = function(value) {
      return jspb.Message.setProto3IntField(this, 6, value);
    }, goog.object.extend(exports, proto.flow.entities);
  }(node_version_info_pb)), node_version_info_pb;
}
function requireAccess_pb() {
  return hasRequiredAccess_pb || (hasRequiredAccess_pb = 1, function(exports) {
    var flow_entities_block_header_pb, flow_entities_block_pb, flow_entities_collection_pb, flow_entities_event_pb, flow_entities_execution_result_pb, flow_entities_metadata_pb, flow_entities_node_version_info_pb, flow_entities_transaction_pb, google_protobuf_timestamp_pb, jspb = require$$0, goog = jspb, global = "undefined" != typeof globalThis && globalThis || "undefined" != typeof window && window || void 0 !== global && global || "undefined" != typeof self && self || function() {
      return this;
    }.call(null) || Function("return this")(), flow_entities_account_pb = requireAccount_pb();
    goog.object.extend(proto, flow_entities_account_pb), flow_entities_block_header_pb = requireBlock_header_pb(), 
    goog.object.extend(proto, flow_entities_block_header_pb), flow_entities_block_pb = requireBlock_pb(), 
    goog.object.extend(proto, flow_entities_block_pb), flow_entities_collection_pb = requireCollection_pb(), 
    goog.object.extend(proto, flow_entities_collection_pb), flow_entities_event_pb = requireEvent_pb(), 
    goog.object.extend(proto, flow_entities_event_pb), flow_entities_execution_result_pb = requireExecution_result_pb(), 
    goog.object.extend(proto, flow_entities_execution_result_pb), flow_entities_metadata_pb = requireMetadata_pb(), 
    goog.object.extend(proto, flow_entities_metadata_pb), flow_entities_node_version_info_pb = requireNode_version_info_pb(), 
    goog.object.extend(proto, flow_entities_node_version_info_pb), flow_entities_transaction_pb = requireTransaction_pb(), 
    goog.object.extend(proto, flow_entities_transaction_pb), google_protobuf_timestamp_pb = require$$10, 
    goog.object.extend(proto, google_protobuf_timestamp_pb), goog.exportSymbol("proto.flow.access.AccountResponse", null, global), 
    goog.exportSymbol("proto.flow.access.BlockHeaderResponse", null, global), goog.exportSymbol("proto.flow.access.BlockResponse", null, global), 
    goog.exportSymbol("proto.flow.access.CollectionResponse", null, global), goog.exportSymbol("proto.flow.access.EventsResponse", null, global), 
    goog.exportSymbol("proto.flow.access.EventsResponse.Result", null, global), goog.exportSymbol("proto.flow.access.ExecuteScriptAtBlockHeightRequest", null, global), 
    goog.exportSymbol("proto.flow.access.ExecuteScriptAtBlockIDRequest", null, global), 
    goog.exportSymbol("proto.flow.access.ExecuteScriptAtLatestBlockRequest", null, global), 
    goog.exportSymbol("proto.flow.access.ExecuteScriptResponse", null, global), goog.exportSymbol("proto.flow.access.ExecutionResultByIDResponse", null, global), 
    goog.exportSymbol("proto.flow.access.ExecutionResultForBlockIDResponse", null, global), 
    goog.exportSymbol("proto.flow.access.GetAccountAtBlockHeightRequest", null, global), 
    goog.exportSymbol("proto.flow.access.GetAccountAtLatestBlockRequest", null, global), 
    goog.exportSymbol("proto.flow.access.GetAccountRequest", null, global), goog.exportSymbol("proto.flow.access.GetAccountResponse", null, global), 
    goog.exportSymbol("proto.flow.access.GetBlockByHeightRequest", null, global), goog.exportSymbol("proto.flow.access.GetBlockByIDRequest", null, global), 
    goog.exportSymbol("proto.flow.access.GetBlockHeaderByHeightRequest", null, global), 
    goog.exportSymbol("proto.flow.access.GetBlockHeaderByIDRequest", null, global), 
    goog.exportSymbol("proto.flow.access.GetCollectionByIDRequest", null, global), goog.exportSymbol("proto.flow.access.GetEventsForBlockIDsRequest", null, global), 
    goog.exportSymbol("proto.flow.access.GetEventsForHeightRangeRequest", null, global), 
    goog.exportSymbol("proto.flow.access.GetExecutionResultByIDRequest", null, global), 
    goog.exportSymbol("proto.flow.access.GetExecutionResultForBlockIDRequest", null, global), 
    goog.exportSymbol("proto.flow.access.GetLatestBlockHeaderRequest", null, global), 
    goog.exportSymbol("proto.flow.access.GetLatestBlockRequest", null, global), goog.exportSymbol("proto.flow.access.GetLatestProtocolStateSnapshotRequest", null, global), 
    goog.exportSymbol("proto.flow.access.GetNetworkParametersRequest", null, global), 
    goog.exportSymbol("proto.flow.access.GetNetworkParametersResponse", null, global), 
    goog.exportSymbol("proto.flow.access.GetNodeVersionInfoRequest", null, global), 
    goog.exportSymbol("proto.flow.access.GetNodeVersionInfoResponse", null, global), 
    goog.exportSymbol("proto.flow.access.GetProtocolStateSnapshotByBlockIDRequest", null, global), 
    goog.exportSymbol("proto.flow.access.GetProtocolStateSnapshotByHeightRequest", null, global), 
    goog.exportSymbol("proto.flow.access.GetSystemTransactionRequest", null, global), 
    goog.exportSymbol("proto.flow.access.GetSystemTransactionResultRequest", null, global), 
    goog.exportSymbol("proto.flow.access.GetTransactionByIndexRequest", null, global), 
    goog.exportSymbol("proto.flow.access.GetTransactionRequest", null, global), goog.exportSymbol("proto.flow.access.GetTransactionsByBlockIDRequest", null, global), 
    goog.exportSymbol("proto.flow.access.PingRequest", null, global), goog.exportSymbol("proto.flow.access.PingResponse", null, global), 
    goog.exportSymbol("proto.flow.access.ProtocolStateSnapshotResponse", null, global), 
    goog.exportSymbol("proto.flow.access.SendAndSubscribeTransactionStatusesRequest", null, global), 
    goog.exportSymbol("proto.flow.access.SendAndSubscribeTransactionStatusesResponse", null, global), 
    goog.exportSymbol("proto.flow.access.SendTransactionRequest", null, global), goog.exportSymbol("proto.flow.access.SendTransactionResponse", null, global), 
    goog.exportSymbol("proto.flow.access.SubscribeBlockDigestsFromLatestRequest", null, global), 
    goog.exportSymbol("proto.flow.access.SubscribeBlockDigestsFromStartBlockIDRequest", null, global), 
    goog.exportSymbol("proto.flow.access.SubscribeBlockDigestsFromStartHeightRequest", null, global), 
    goog.exportSymbol("proto.flow.access.SubscribeBlockDigestsResponse", null, global), 
    goog.exportSymbol("proto.flow.access.SubscribeBlockHeadersFromLatestRequest", null, global), 
    goog.exportSymbol("proto.flow.access.SubscribeBlockHeadersFromStartBlockIDRequest", null, global), 
    goog.exportSymbol("proto.flow.access.SubscribeBlockHeadersFromStartHeightRequest", null, global), 
    goog.exportSymbol("proto.flow.access.SubscribeBlockHeadersResponse", null, global), 
    goog.exportSymbol("proto.flow.access.SubscribeBlocksFromLatestRequest", null, global), 
    goog.exportSymbol("proto.flow.access.SubscribeBlocksFromStartBlockIDRequest", null, global), 
    goog.exportSymbol("proto.flow.access.SubscribeBlocksFromStartHeightRequest", null, global), 
    goog.exportSymbol("proto.flow.access.SubscribeBlocksResponse", null, global), goog.exportSymbol("proto.flow.access.TransactionResponse", null, global), 
    goog.exportSymbol("proto.flow.access.TransactionResultResponse", null, global), 
    goog.exportSymbol("proto.flow.access.TransactionResultsResponse", null, global), 
    goog.exportSymbol("proto.flow.access.TransactionsResponse", null, global), proto.flow.access.PingRequest = function(opt_data) {
      jspb.Message.initialize(this, opt_data, 0, -1, null, null);
    }, goog.inherits(proto.flow.access.PingRequest, jspb.Message), goog.DEBUG && !COMPILED && (proto.flow.access.PingRequest.displayName = "proto.flow.access.PingRequest"), 
    proto.flow.access.PingResponse = function(opt_data) {
      jspb.Message.initialize(this, opt_data, 0, -1, null, null);
    }, goog.inherits(proto.flow.access.PingResponse, jspb.Message), goog.DEBUG && !COMPILED && (proto.flow.access.PingResponse.displayName = "proto.flow.access.PingResponse"), 
    proto.flow.access.GetNodeVersionInfoRequest = function(opt_data) {
      jspb.Message.initialize(this, opt_data, 0, -1, null, null);
    }, goog.inherits(proto.flow.access.GetNodeVersionInfoRequest, jspb.Message), goog.DEBUG && !COMPILED && (proto.flow.access.GetNodeVersionInfoRequest.displayName = "proto.flow.access.GetNodeVersionInfoRequest"), 
    proto.flow.access.GetNodeVersionInfoResponse = function(opt_data) {
      jspb.Message.initialize(this, opt_data, 0, -1, null, null);
    }, goog.inherits(proto.flow.access.GetNodeVersionInfoResponse, jspb.Message), goog.DEBUG && !COMPILED && (proto.flow.access.GetNodeVersionInfoResponse.displayName = "proto.flow.access.GetNodeVersionInfoResponse"), 
    proto.flow.access.GetLatestBlockHeaderRequest = function(opt_data) {
      jspb.Message.initialize(this, opt_data, 0, -1, null, null);
    }, goog.inherits(proto.flow.access.GetLatestBlockHeaderRequest, jspb.Message), goog.DEBUG && !COMPILED && (proto.flow.access.GetLatestBlockHeaderRequest.displayName = "proto.flow.access.GetLatestBlockHeaderRequest"), 
    proto.flow.access.GetBlockHeaderByIDRequest = function(opt_data) {
      jspb.Message.initialize(this, opt_data, 0, -1, null, null);
    }, goog.inherits(proto.flow.access.GetBlockHeaderByIDRequest, jspb.Message), goog.DEBUG && !COMPILED && (proto.flow.access.GetBlockHeaderByIDRequest.displayName = "proto.flow.access.GetBlockHeaderByIDRequest"), 
    proto.flow.access.GetBlockHeaderByHeightRequest = function(opt_data) {
      jspb.Message.initialize(this, opt_data, 0, -1, null, null);
    }, goog.inherits(proto.flow.access.GetBlockHeaderByHeightRequest, jspb.Message), 
    goog.DEBUG && !COMPILED && (proto.flow.access.GetBlockHeaderByHeightRequest.displayName = "proto.flow.access.GetBlockHeaderByHeightRequest"), 
    proto.flow.access.BlockHeaderResponse = function(opt_data) {
      jspb.Message.initialize(this, opt_data, 0, -1, null, null);
    }, goog.inherits(proto.flow.access.BlockHeaderResponse, jspb.Message), goog.DEBUG && !COMPILED && (proto.flow.access.BlockHeaderResponse.displayName = "proto.flow.access.BlockHeaderResponse"), 
    proto.flow.access.GetLatestBlockRequest = function(opt_data) {
      jspb.Message.initialize(this, opt_data, 0, -1, null, null);
    }, goog.inherits(proto.flow.access.GetLatestBlockRequest, jspb.Message), goog.DEBUG && !COMPILED && (proto.flow.access.GetLatestBlockRequest.displayName = "proto.flow.access.GetLatestBlockRequest"), 
    proto.flow.access.GetBlockByIDRequest = function(opt_data) {
      jspb.Message.initialize(this, opt_data, 0, -1, null, null);
    }, goog.inherits(proto.flow.access.GetBlockByIDRequest, jspb.Message), goog.DEBUG && !COMPILED && (proto.flow.access.GetBlockByIDRequest.displayName = "proto.flow.access.GetBlockByIDRequest"), 
    proto.flow.access.GetBlockByHeightRequest = function(opt_data) {
      jspb.Message.initialize(this, opt_data, 0, -1, null, null);
    }, goog.inherits(proto.flow.access.GetBlockByHeightRequest, jspb.Message), goog.DEBUG && !COMPILED && (proto.flow.access.GetBlockByHeightRequest.displayName = "proto.flow.access.GetBlockByHeightRequest"), 
    proto.flow.access.BlockResponse = function(opt_data) {
      jspb.Message.initialize(this, opt_data, 0, -1, null, null);
    }, goog.inherits(proto.flow.access.BlockResponse, jspb.Message), goog.DEBUG && !COMPILED && (proto.flow.access.BlockResponse.displayName = "proto.flow.access.BlockResponse"), 
    proto.flow.access.GetCollectionByIDRequest = function(opt_data) {
      jspb.Message.initialize(this, opt_data, 0, -1, null, null);
    }, goog.inherits(proto.flow.access.GetCollectionByIDRequest, jspb.Message), goog.DEBUG && !COMPILED && (proto.flow.access.GetCollectionByIDRequest.displayName = "proto.flow.access.GetCollectionByIDRequest"), 
    proto.flow.access.CollectionResponse = function(opt_data) {
      jspb.Message.initialize(this, opt_data, 0, -1, null, null);
    }, goog.inherits(proto.flow.access.CollectionResponse, jspb.Message), goog.DEBUG && !COMPILED && (proto.flow.access.CollectionResponse.displayName = "proto.flow.access.CollectionResponse"), 
    proto.flow.access.SendTransactionRequest = function(opt_data) {
      jspb.Message.initialize(this, opt_data, 0, -1, null, null);
    }, goog.inherits(proto.flow.access.SendTransactionRequest, jspb.Message), goog.DEBUG && !COMPILED && (proto.flow.access.SendTransactionRequest.displayName = "proto.flow.access.SendTransactionRequest"), 
    proto.flow.access.SendTransactionResponse = function(opt_data) {
      jspb.Message.initialize(this, opt_data, 0, -1, null, null);
    }, goog.inherits(proto.flow.access.SendTransactionResponse, jspb.Message), goog.DEBUG && !COMPILED && (proto.flow.access.SendTransactionResponse.displayName = "proto.flow.access.SendTransactionResponse"), 
    proto.flow.access.GetTransactionRequest = function(opt_data) {
      jspb.Message.initialize(this, opt_data, 0, -1, null, null);
    }, goog.inherits(proto.flow.access.GetTransactionRequest, jspb.Message), goog.DEBUG && !COMPILED && (proto.flow.access.GetTransactionRequest.displayName = "proto.flow.access.GetTransactionRequest"), 
    proto.flow.access.GetSystemTransactionRequest = function(opt_data) {
      jspb.Message.initialize(this, opt_data, 0, -1, null, null);
    }, goog.inherits(proto.flow.access.GetSystemTransactionRequest, jspb.Message), goog.DEBUG && !COMPILED && (proto.flow.access.GetSystemTransactionRequest.displayName = "proto.flow.access.GetSystemTransactionRequest"), 
    proto.flow.access.GetSystemTransactionResultRequest = function(opt_data) {
      jspb.Message.initialize(this, opt_data, 0, -1, null, null);
    }, goog.inherits(proto.flow.access.GetSystemTransactionResultRequest, jspb.Message), 
    goog.DEBUG && !COMPILED && (proto.flow.access.GetSystemTransactionResultRequest.displayName = "proto.flow.access.GetSystemTransactionResultRequest"), 
    proto.flow.access.GetTransactionByIndexRequest = function(opt_data) {
      jspb.Message.initialize(this, opt_data, 0, -1, null, null);
    }, goog.inherits(proto.flow.access.GetTransactionByIndexRequest, jspb.Message), 
    goog.DEBUG && !COMPILED && (proto.flow.access.GetTransactionByIndexRequest.displayName = "proto.flow.access.GetTransactionByIndexRequest"), 
    proto.flow.access.GetTransactionsByBlockIDRequest = function(opt_data) {
      jspb.Message.initialize(this, opt_data, 0, -1, null, null);
    }, goog.inherits(proto.flow.access.GetTransactionsByBlockIDRequest, jspb.Message), 
    goog.DEBUG && !COMPILED && (proto.flow.access.GetTransactionsByBlockIDRequest.displayName = "proto.flow.access.GetTransactionsByBlockIDRequest"), 
    proto.flow.access.TransactionResultsResponse = function(opt_data) {
      jspb.Message.initialize(this, opt_data, 0, -1, proto.flow.access.TransactionResultsResponse.repeatedFields_, null);
    }, goog.inherits(proto.flow.access.TransactionResultsResponse, jspb.Message), goog.DEBUG && !COMPILED && (proto.flow.access.TransactionResultsResponse.displayName = "proto.flow.access.TransactionResultsResponse"), 
    proto.flow.access.TransactionsResponse = function(opt_data) {
      jspb.Message.initialize(this, opt_data, 0, -1, proto.flow.access.TransactionsResponse.repeatedFields_, null);
    }, goog.inherits(proto.flow.access.TransactionsResponse, jspb.Message), goog.DEBUG && !COMPILED && (proto.flow.access.TransactionsResponse.displayName = "proto.flow.access.TransactionsResponse"), 
    proto.flow.access.TransactionResponse = function(opt_data) {
      jspb.Message.initialize(this, opt_data, 0, -1, null, null);
    }, goog.inherits(proto.flow.access.TransactionResponse, jspb.Message), goog.DEBUG && !COMPILED && (proto.flow.access.TransactionResponse.displayName = "proto.flow.access.TransactionResponse"), 
    proto.flow.access.TransactionResultResponse = function(opt_data) {
      jspb.Message.initialize(this, opt_data, 0, -1, proto.flow.access.TransactionResultResponse.repeatedFields_, null);
    }, goog.inherits(proto.flow.access.TransactionResultResponse, jspb.Message), goog.DEBUG && !COMPILED && (proto.flow.access.TransactionResultResponse.displayName = "proto.flow.access.TransactionResultResponse"), 
    proto.flow.access.GetAccountRequest = function(opt_data) {
      jspb.Message.initialize(this, opt_data, 0, -1, null, null);
    }, goog.inherits(proto.flow.access.GetAccountRequest, jspb.Message), goog.DEBUG && !COMPILED && (proto.flow.access.GetAccountRequest.displayName = "proto.flow.access.GetAccountRequest"), 
    proto.flow.access.GetAccountResponse = function(opt_data) {
      jspb.Message.initialize(this, opt_data, 0, -1, null, null);
    }, goog.inherits(proto.flow.access.GetAccountResponse, jspb.Message), goog.DEBUG && !COMPILED && (proto.flow.access.GetAccountResponse.displayName = "proto.flow.access.GetAccountResponse"), 
    proto.flow.access.GetAccountAtLatestBlockRequest = function(opt_data) {
      jspb.Message.initialize(this, opt_data, 0, -1, null, null);
    }, goog.inherits(proto.flow.access.GetAccountAtLatestBlockRequest, jspb.Message), 
    goog.DEBUG && !COMPILED && (proto.flow.access.GetAccountAtLatestBlockRequest.displayName = "proto.flow.access.GetAccountAtLatestBlockRequest"), 
    proto.flow.access.AccountResponse = function(opt_data) {
      jspb.Message.initialize(this, opt_data, 0, -1, null, null);
    }, goog.inherits(proto.flow.access.AccountResponse, jspb.Message), goog.DEBUG && !COMPILED && (proto.flow.access.AccountResponse.displayName = "proto.flow.access.AccountResponse"), 
    proto.flow.access.GetAccountAtBlockHeightRequest = function(opt_data) {
      jspb.Message.initialize(this, opt_data, 0, -1, null, null);
    }, goog.inherits(proto.flow.access.GetAccountAtBlockHeightRequest, jspb.Message), 
    goog.DEBUG && !COMPILED && (proto.flow.access.GetAccountAtBlockHeightRequest.displayName = "proto.flow.access.GetAccountAtBlockHeightRequest"), 
    proto.flow.access.ExecuteScriptAtLatestBlockRequest = function(opt_data) {
      jspb.Message.initialize(this, opt_data, 0, -1, proto.flow.access.ExecuteScriptAtLatestBlockRequest.repeatedFields_, null);
    }, goog.inherits(proto.flow.access.ExecuteScriptAtLatestBlockRequest, jspb.Message), 
    goog.DEBUG && !COMPILED && (proto.flow.access.ExecuteScriptAtLatestBlockRequest.displayName = "proto.flow.access.ExecuteScriptAtLatestBlockRequest"), 
    proto.flow.access.ExecuteScriptAtBlockIDRequest = function(opt_data) {
      jspb.Message.initialize(this, opt_data, 0, -1, proto.flow.access.ExecuteScriptAtBlockIDRequest.repeatedFields_, null);
    }, goog.inherits(proto.flow.access.ExecuteScriptAtBlockIDRequest, jspb.Message), 
    goog.DEBUG && !COMPILED && (proto.flow.access.ExecuteScriptAtBlockIDRequest.displayName = "proto.flow.access.ExecuteScriptAtBlockIDRequest"), 
    proto.flow.access.ExecuteScriptAtBlockHeightRequest = function(opt_data) {
      jspb.Message.initialize(this, opt_data, 0, -1, proto.flow.access.ExecuteScriptAtBlockHeightRequest.repeatedFields_, null);
    }, goog.inherits(proto.flow.access.ExecuteScriptAtBlockHeightRequest, jspb.Message), 
    goog.DEBUG && !COMPILED && (proto.flow.access.ExecuteScriptAtBlockHeightRequest.displayName = "proto.flow.access.ExecuteScriptAtBlockHeightRequest"), 
    proto.flow.access.ExecuteScriptResponse = function(opt_data) {
      jspb.Message.initialize(this, opt_data, 0, -1, null, null);
    }, goog.inherits(proto.flow.access.ExecuteScriptResponse, jspb.Message), goog.DEBUG && !COMPILED && (proto.flow.access.ExecuteScriptResponse.displayName = "proto.flow.access.ExecuteScriptResponse"), 
    proto.flow.access.GetEventsForHeightRangeRequest = function(opt_data) {
      jspb.Message.initialize(this, opt_data, 0, -1, null, null);
    }, goog.inherits(proto.flow.access.GetEventsForHeightRangeRequest, jspb.Message), 
    goog.DEBUG && !COMPILED && (proto.flow.access.GetEventsForHeightRangeRequest.displayName = "proto.flow.access.GetEventsForHeightRangeRequest"), 
    proto.flow.access.GetEventsForBlockIDsRequest = function(opt_data) {
      jspb.Message.initialize(this, opt_data, 0, -1, proto.flow.access.GetEventsForBlockIDsRequest.repeatedFields_, null);
    }, goog.inherits(proto.flow.access.GetEventsForBlockIDsRequest, jspb.Message), goog.DEBUG && !COMPILED && (proto.flow.access.GetEventsForBlockIDsRequest.displayName = "proto.flow.access.GetEventsForBlockIDsRequest"), 
    proto.flow.access.EventsResponse = function(opt_data) {
      jspb.Message.initialize(this, opt_data, 0, -1, proto.flow.access.EventsResponse.repeatedFields_, null);
    }, goog.inherits(proto.flow.access.EventsResponse, jspb.Message), goog.DEBUG && !COMPILED && (proto.flow.access.EventsResponse.displayName = "proto.flow.access.EventsResponse"), 
    proto.flow.access.EventsResponse.Result = function(opt_data) {
      jspb.Message.initialize(this, opt_data, 0, -1, proto.flow.access.EventsResponse.Result.repeatedFields_, null);
    }, goog.inherits(proto.flow.access.EventsResponse.Result, jspb.Message), goog.DEBUG && !COMPILED && (proto.flow.access.EventsResponse.Result.displayName = "proto.flow.access.EventsResponse.Result"), 
    proto.flow.access.GetNetworkParametersRequest = function(opt_data) {
      jspb.Message.initialize(this, opt_data, 0, -1, null, null);
    }, goog.inherits(proto.flow.access.GetNetworkParametersRequest, jspb.Message), goog.DEBUG && !COMPILED && (proto.flow.access.GetNetworkParametersRequest.displayName = "proto.flow.access.GetNetworkParametersRequest"), 
    proto.flow.access.GetNetworkParametersResponse = function(opt_data) {
      jspb.Message.initialize(this, opt_data, 0, -1, null, null);
    }, goog.inherits(proto.flow.access.GetNetworkParametersResponse, jspb.Message), 
    goog.DEBUG && !COMPILED && (proto.flow.access.GetNetworkParametersResponse.displayName = "proto.flow.access.GetNetworkParametersResponse"), 
    proto.flow.access.GetLatestProtocolStateSnapshotRequest = function(opt_data) {
      jspb.Message.initialize(this, opt_data, 0, -1, null, null);
    }, goog.inherits(proto.flow.access.GetLatestProtocolStateSnapshotRequest, jspb.Message), 
    goog.DEBUG && !COMPILED && (proto.flow.access.GetLatestProtocolStateSnapshotRequest.displayName = "proto.flow.access.GetLatestProtocolStateSnapshotRequest"), 
    proto.flow.access.GetProtocolStateSnapshotByBlockIDRequest = function(opt_data) {
      jspb.Message.initialize(this, opt_data, 0, -1, null, null);
    }, goog.inherits(proto.flow.access.GetProtocolStateSnapshotByBlockIDRequest, jspb.Message), 
    goog.DEBUG && !COMPILED && (proto.flow.access.GetProtocolStateSnapshotByBlockIDRequest.displayName = "proto.flow.access.GetProtocolStateSnapshotByBlockIDRequest"), 
    proto.flow.access.GetProtocolStateSnapshotByHeightRequest = function(opt_data) {
      jspb.Message.initialize(this, opt_data, 0, -1, null, null);
    }, goog.inherits(proto.flow.access.GetProtocolStateSnapshotByHeightRequest, jspb.Message), 
    goog.DEBUG && !COMPILED && (proto.flow.access.GetProtocolStateSnapshotByHeightRequest.displayName = "proto.flow.access.GetProtocolStateSnapshotByHeightRequest"), 
    proto.flow.access.ProtocolStateSnapshotResponse = function(opt_data) {
      jspb.Message.initialize(this, opt_data, 0, -1, null, null);
    }, goog.inherits(proto.flow.access.ProtocolStateSnapshotResponse, jspb.Message), 
    goog.DEBUG && !COMPILED && (proto.flow.access.ProtocolStateSnapshotResponse.displayName = "proto.flow.access.ProtocolStateSnapshotResponse"), 
    proto.flow.access.GetExecutionResultForBlockIDRequest = function(opt_data) {
      jspb.Message.initialize(this, opt_data, 0, -1, null, null);
    }, goog.inherits(proto.flow.access.GetExecutionResultForBlockIDRequest, jspb.Message), 
    goog.DEBUG && !COMPILED && (proto.flow.access.GetExecutionResultForBlockIDRequest.displayName = "proto.flow.access.GetExecutionResultForBlockIDRequest"), 
    proto.flow.access.ExecutionResultForBlockIDResponse = function(opt_data) {
      jspb.Message.initialize(this, opt_data, 0, -1, null, null);
    }, goog.inherits(proto.flow.access.ExecutionResultForBlockIDResponse, jspb.Message), 
    goog.DEBUG && !COMPILED && (proto.flow.access.ExecutionResultForBlockIDResponse.displayName = "proto.flow.access.ExecutionResultForBlockIDResponse"), 
    proto.flow.access.GetExecutionResultByIDRequest = function(opt_data) {
      jspb.Message.initialize(this, opt_data, 0, -1, null, null);
    }, goog.inherits(proto.flow.access.GetExecutionResultByIDRequest, jspb.Message), 
    goog.DEBUG && !COMPILED && (proto.flow.access.GetExecutionResultByIDRequest.displayName = "proto.flow.access.GetExecutionResultByIDRequest"), 
    proto.flow.access.ExecutionResultByIDResponse = function(opt_data) {
      jspb.Message.initialize(this, opt_data, 0, -1, null, null);
    }, goog.inherits(proto.flow.access.ExecutionResultByIDResponse, jspb.Message), goog.DEBUG && !COMPILED && (proto.flow.access.ExecutionResultByIDResponse.displayName = "proto.flow.access.ExecutionResultByIDResponse"), 
    proto.flow.access.SubscribeBlocksFromStartBlockIDRequest = function(opt_data) {
      jspb.Message.initialize(this, opt_data, 0, -1, null, null);
    }, goog.inherits(proto.flow.access.SubscribeBlocksFromStartBlockIDRequest, jspb.Message), 
    goog.DEBUG && !COMPILED && (proto.flow.access.SubscribeBlocksFromStartBlockIDRequest.displayName = "proto.flow.access.SubscribeBlocksFromStartBlockIDRequest"), 
    proto.flow.access.SubscribeBlocksFromStartHeightRequest = function(opt_data) {
      jspb.Message.initialize(this, opt_data, 0, -1, null, null);
    }, goog.inherits(proto.flow.access.SubscribeBlocksFromStartHeightRequest, jspb.Message), 
    goog.DEBUG && !COMPILED && (proto.flow.access.SubscribeBlocksFromStartHeightRequest.displayName = "proto.flow.access.SubscribeBlocksFromStartHeightRequest"), 
    proto.flow.access.SubscribeBlocksFromLatestRequest = function(opt_data) {
      jspb.Message.initialize(this, opt_data, 0, -1, null, null);
    }, goog.inherits(proto.flow.access.SubscribeBlocksFromLatestRequest, jspb.Message), 
    goog.DEBUG && !COMPILED && (proto.flow.access.SubscribeBlocksFromLatestRequest.displayName = "proto.flow.access.SubscribeBlocksFromLatestRequest"), 
    proto.flow.access.SubscribeBlocksResponse = function(opt_data) {
      jspb.Message.initialize(this, opt_data, 0, -1, null, null);
    }, goog.inherits(proto.flow.access.SubscribeBlocksResponse, jspb.Message), goog.DEBUG && !COMPILED && (proto.flow.access.SubscribeBlocksResponse.displayName = "proto.flow.access.SubscribeBlocksResponse"), 
    proto.flow.access.SubscribeBlockHeadersFromStartBlockIDRequest = function(opt_data) {
      jspb.Message.initialize(this, opt_data, 0, -1, null, null);
    }, goog.inherits(proto.flow.access.SubscribeBlockHeadersFromStartBlockIDRequest, jspb.Message), 
    goog.DEBUG && !COMPILED && (proto.flow.access.SubscribeBlockHeadersFromStartBlockIDRequest.displayName = "proto.flow.access.SubscribeBlockHeadersFromStartBlockIDRequest"), 
    proto.flow.access.SubscribeBlockHeadersFromStartHeightRequest = function(opt_data) {
      jspb.Message.initialize(this, opt_data, 0, -1, null, null);
    }, goog.inherits(proto.flow.access.SubscribeBlockHeadersFromStartHeightRequest, jspb.Message), 
    goog.DEBUG && !COMPILED && (proto.flow.access.SubscribeBlockHeadersFromStartHeightRequest.displayName = "proto.flow.access.SubscribeBlockHeadersFromStartHeightRequest"), 
    proto.flow.access.SubscribeBlockHeadersFromLatestRequest = function(opt_data) {
      jspb.Message.initialize(this, opt_data, 0, -1, null, null);
    }, goog.inherits(proto.flow.access.SubscribeBlockHeadersFromLatestRequest, jspb.Message), 
    goog.DEBUG && !COMPILED && (proto.flow.access.SubscribeBlockHeadersFromLatestRequest.displayName = "proto.flow.access.SubscribeBlockHeadersFromLatestRequest"), 
    proto.flow.access.SubscribeBlockHeadersResponse = function(opt_data) {
      jspb.Message.initialize(this, opt_data, 0, -1, null, null);
    }, goog.inherits(proto.flow.access.SubscribeBlockHeadersResponse, jspb.Message), 
    goog.DEBUG && !COMPILED && (proto.flow.access.SubscribeBlockHeadersResponse.displayName = "proto.flow.access.SubscribeBlockHeadersResponse"), 
    proto.flow.access.SubscribeBlockDigestsFromStartBlockIDRequest = function(opt_data) {
      jspb.Message.initialize(this, opt_data, 0, -1, null, null);
    }, goog.inherits(proto.flow.access.SubscribeBlockDigestsFromStartBlockIDRequest, jspb.Message), 
    goog.DEBUG && !COMPILED && (proto.flow.access.SubscribeBlockDigestsFromStartBlockIDRequest.displayName = "proto.flow.access.SubscribeBlockDigestsFromStartBlockIDRequest"), 
    proto.flow.access.SubscribeBlockDigestsFromStartHeightRequest = function(opt_data) {
      jspb.Message.initialize(this, opt_data, 0, -1, null, null);
    }, goog.inherits(proto.flow.access.SubscribeBlockDigestsFromStartHeightRequest, jspb.Message), 
    goog.DEBUG && !COMPILED && (proto.flow.access.SubscribeBlockDigestsFromStartHeightRequest.displayName = "proto.flow.access.SubscribeBlockDigestsFromStartHeightRequest"), 
    proto.flow.access.SubscribeBlockDigestsFromLatestRequest = function(opt_data) {
      jspb.Message.initialize(this, opt_data, 0, -1, null, null);
    }, goog.inherits(proto.flow.access.SubscribeBlockDigestsFromLatestRequest, jspb.Message), 
    goog.DEBUG && !COMPILED && (proto.flow.access.SubscribeBlockDigestsFromLatestRequest.displayName = "proto.flow.access.SubscribeBlockDigestsFromLatestRequest"), 
    proto.flow.access.SubscribeBlockDigestsResponse = function(opt_data) {
      jspb.Message.initialize(this, opt_data, 0, -1, null, null);
    }, goog.inherits(proto.flow.access.SubscribeBlockDigestsResponse, jspb.Message), 
    goog.DEBUG && !COMPILED && (proto.flow.access.SubscribeBlockDigestsResponse.displayName = "proto.flow.access.SubscribeBlockDigestsResponse"), 
    proto.flow.access.SendAndSubscribeTransactionStatusesRequest = function(opt_data) {
      jspb.Message.initialize(this, opt_data, 0, -1, null, null);
    }, goog.inherits(proto.flow.access.SendAndSubscribeTransactionStatusesRequest, jspb.Message), 
    goog.DEBUG && !COMPILED && (proto.flow.access.SendAndSubscribeTransactionStatusesRequest.displayName = "proto.flow.access.SendAndSubscribeTransactionStatusesRequest"), 
    proto.flow.access.SendAndSubscribeTransactionStatusesResponse = function(opt_data) {
      jspb.Message.initialize(this, opt_data, 0, -1, null, null);
    }, goog.inherits(proto.flow.access.SendAndSubscribeTransactionStatusesResponse, jspb.Message), 
    goog.DEBUG && !COMPILED && (proto.flow.access.SendAndSubscribeTransactionStatusesResponse.displayName = "proto.flow.access.SendAndSubscribeTransactionStatusesResponse"), 
    jspb.Message.GENERATE_TO_OBJECT && (proto.flow.access.PingRequest.prototype.toObject = function(opt_includeInstance) {
      return proto.flow.access.PingRequest.toObject(opt_includeInstance, this);
    }, proto.flow.access.PingRequest.toObject = function(includeInstance, msg) {
      var obj = {};
      return includeInstance && (obj.$jspbMessageInstance = msg), obj;
    }), proto.flow.access.PingRequest.deserializeBinary = function(bytes) {
      var reader = new jspb.BinaryReader(bytes), msg = new proto.flow.access.PingRequest;
      return proto.flow.access.PingRequest.deserializeBinaryFromReader(msg, reader);
    }, proto.flow.access.PingRequest.deserializeBinaryFromReader = function(msg, reader) {
      for (;reader.nextField() && !reader.isEndGroup(); ) {
        reader.getFieldNumber();
        reader.skipField();
      }
      return msg;
    }, proto.flow.access.PingRequest.prototype.serializeBinary = function() {
      var writer = new jspb.BinaryWriter;
      return proto.flow.access.PingRequest.serializeBinaryToWriter(this, writer), writer.getResultBuffer();
    }, proto.flow.access.PingRequest.serializeBinaryToWriter = function(message, writer) {}, 
    jspb.Message.GENERATE_TO_OBJECT && (proto.flow.access.PingResponse.prototype.toObject = function(opt_includeInstance) {
      return proto.flow.access.PingResponse.toObject(opt_includeInstance, this);
    }, proto.flow.access.PingResponse.toObject = function(includeInstance, msg) {
      var obj = {};
      return includeInstance && (obj.$jspbMessageInstance = msg), obj;
    }), proto.flow.access.PingResponse.deserializeBinary = function(bytes) {
      var reader = new jspb.BinaryReader(bytes), msg = new proto.flow.access.PingResponse;
      return proto.flow.access.PingResponse.deserializeBinaryFromReader(msg, reader);
    }, proto.flow.access.PingResponse.deserializeBinaryFromReader = function(msg, reader) {
      for (;reader.nextField() && !reader.isEndGroup(); ) {
        reader.getFieldNumber();
        reader.skipField();
      }
      return msg;
    }, proto.flow.access.PingResponse.prototype.serializeBinary = function() {
      var writer = new jspb.BinaryWriter;
      return proto.flow.access.PingResponse.serializeBinaryToWriter(this, writer), writer.getResultBuffer();
    }, proto.flow.access.PingResponse.serializeBinaryToWriter = function(message, writer) {}, 
    jspb.Message.GENERATE_TO_OBJECT && (proto.flow.access.GetNodeVersionInfoRequest.prototype.toObject = function(opt_includeInstance) {
      return proto.flow.access.GetNodeVersionInfoRequest.toObject(opt_includeInstance, this);
    }, proto.flow.access.GetNodeVersionInfoRequest.toObject = function(includeInstance, msg) {
      var obj = {};
      return includeInstance && (obj.$jspbMessageInstance = msg), obj;
    }), proto.flow.access.GetNodeVersionInfoRequest.deserializeBinary = function(bytes) {
      var reader = new jspb.BinaryReader(bytes), msg = new proto.flow.access.GetNodeVersionInfoRequest;
      return proto.flow.access.GetNodeVersionInfoRequest.deserializeBinaryFromReader(msg, reader);
    }, proto.flow.access.GetNodeVersionInfoRequest.deserializeBinaryFromReader = function(msg, reader) {
      for (;reader.nextField() && !reader.isEndGroup(); ) {
        reader.getFieldNumber();
        reader.skipField();
      }
      return msg;
    }, proto.flow.access.GetNodeVersionInfoRequest.prototype.serializeBinary = function() {
      var writer = new jspb.BinaryWriter;
      return proto.flow.access.GetNodeVersionInfoRequest.serializeBinaryToWriter(this, writer), 
      writer.getResultBuffer();
    }, proto.flow.access.GetNodeVersionInfoRequest.serializeBinaryToWriter = function(message, writer) {}, 
    jspb.Message.GENERATE_TO_OBJECT && (proto.flow.access.GetNodeVersionInfoResponse.prototype.toObject = function(opt_includeInstance) {
      return proto.flow.access.GetNodeVersionInfoResponse.toObject(opt_includeInstance, this);
    }, proto.flow.access.GetNodeVersionInfoResponse.toObject = function(includeInstance, msg) {
      var f, obj = {
        info: (f = msg.getInfo()) && flow_entities_node_version_info_pb.NodeVersionInfo.toObject(includeInstance, f)
      };
      return includeInstance && (obj.$jspbMessageInstance = msg), obj;
    }), proto.flow.access.GetNodeVersionInfoResponse.deserializeBinary = function(bytes) {
      var reader = new jspb.BinaryReader(bytes), msg = new proto.flow.access.GetNodeVersionInfoResponse;
      return proto.flow.access.GetNodeVersionInfoResponse.deserializeBinaryFromReader(msg, reader);
    }, proto.flow.access.GetNodeVersionInfoResponse.deserializeBinaryFromReader = function(msg, reader) {
      for (var value; reader.nextField() && !reader.isEndGroup(); ) if (1 === reader.getFieldNumber()) value = new flow_entities_node_version_info_pb.NodeVersionInfo, 
      reader.readMessage(value, flow_entities_node_version_info_pb.NodeVersionInfo.deserializeBinaryFromReader), 
      msg.setInfo(value); else reader.skipField();
      return msg;
    }, proto.flow.access.GetNodeVersionInfoResponse.prototype.serializeBinary = function() {
      var writer = new jspb.BinaryWriter;
      return proto.flow.access.GetNodeVersionInfoResponse.serializeBinaryToWriter(this, writer), 
      writer.getResultBuffer();
    }, proto.flow.access.GetNodeVersionInfoResponse.serializeBinaryToWriter = function(message, writer) {
      var f;
      null != (f = message.getInfo()) && writer.writeMessage(1, f, flow_entities_node_version_info_pb.NodeVersionInfo.serializeBinaryToWriter);
    }, proto.flow.access.GetNodeVersionInfoResponse.prototype.getInfo = function() {
      return jspb.Message.getWrapperField(this, flow_entities_node_version_info_pb.NodeVersionInfo, 1);
    }, proto.flow.access.GetNodeVersionInfoResponse.prototype.setInfo = function(value) {
      return jspb.Message.setWrapperField(this, 1, value);
    }, proto.flow.access.GetNodeVersionInfoResponse.prototype.clearInfo = function() {
      return this.setInfo(void 0);
    }, proto.flow.access.GetNodeVersionInfoResponse.prototype.hasInfo = function() {
      return null != jspb.Message.getField(this, 1);
    }, jspb.Message.GENERATE_TO_OBJECT && (proto.flow.access.GetLatestBlockHeaderRequest.prototype.toObject = function(opt_includeInstance) {
      return proto.flow.access.GetLatestBlockHeaderRequest.toObject(opt_includeInstance, this);
    }, proto.flow.access.GetLatestBlockHeaderRequest.toObject = function(includeInstance, msg) {
      var obj = {
        isSealed: jspb.Message.getBooleanFieldWithDefault(msg, 1, !1)
      };
      return includeInstance && (obj.$jspbMessageInstance = msg), obj;
    }), proto.flow.access.GetLatestBlockHeaderRequest.deserializeBinary = function(bytes) {
      var reader = new jspb.BinaryReader(bytes), msg = new proto.flow.access.GetLatestBlockHeaderRequest;
      return proto.flow.access.GetLatestBlockHeaderRequest.deserializeBinaryFromReader(msg, reader);
    }, proto.flow.access.GetLatestBlockHeaderRequest.deserializeBinaryFromReader = function(msg, reader) {
      for (var value; reader.nextField() && !reader.isEndGroup(); ) if (1 === reader.getFieldNumber()) value = reader.readBool(), 
      msg.setIsSealed(value); else reader.skipField();
      return msg;
    }, proto.flow.access.GetLatestBlockHeaderRequest.prototype.serializeBinary = function() {
      var writer = new jspb.BinaryWriter;
      return proto.flow.access.GetLatestBlockHeaderRequest.serializeBinaryToWriter(this, writer), 
      writer.getResultBuffer();
    }, proto.flow.access.GetLatestBlockHeaderRequest.serializeBinaryToWriter = function(message, writer) {
      var f;
      (f = message.getIsSealed()) && writer.writeBool(1, f);
    }, proto.flow.access.GetLatestBlockHeaderRequest.prototype.getIsSealed = function() {
      return jspb.Message.getBooleanFieldWithDefault(this, 1, !1);
    }, proto.flow.access.GetLatestBlockHeaderRequest.prototype.setIsSealed = function(value) {
      return jspb.Message.setProto3BooleanField(this, 1, value);
    }, jspb.Message.GENERATE_TO_OBJECT && (proto.flow.access.GetBlockHeaderByIDRequest.prototype.toObject = function(opt_includeInstance) {
      return proto.flow.access.GetBlockHeaderByIDRequest.toObject(opt_includeInstance, this);
    }, proto.flow.access.GetBlockHeaderByIDRequest.toObject = function(includeInstance, msg) {
      var obj = {
        id: msg.getId_asB64()
      };
      return includeInstance && (obj.$jspbMessageInstance = msg), obj;
    }), proto.flow.access.GetBlockHeaderByIDRequest.deserializeBinary = function(bytes) {
      var reader = new jspb.BinaryReader(bytes), msg = new proto.flow.access.GetBlockHeaderByIDRequest;
      return proto.flow.access.GetBlockHeaderByIDRequest.deserializeBinaryFromReader(msg, reader);
    }, proto.flow.access.GetBlockHeaderByIDRequest.deserializeBinaryFromReader = function(msg, reader) {
      for (var value; reader.nextField() && !reader.isEndGroup(); ) if (1 === reader.getFieldNumber()) value = reader.readBytes(), 
      msg.setId(value); else reader.skipField();
      return msg;
    }, proto.flow.access.GetBlockHeaderByIDRequest.prototype.serializeBinary = function() {
      var writer = new jspb.BinaryWriter;
      return proto.flow.access.GetBlockHeaderByIDRequest.serializeBinaryToWriter(this, writer), 
      writer.getResultBuffer();
    }, proto.flow.access.GetBlockHeaderByIDRequest.serializeBinaryToWriter = function(message, writer) {
      var f;
      (f = message.getId_asU8()).length > 0 && writer.writeBytes(1, f);
    }, proto.flow.access.GetBlockHeaderByIDRequest.prototype.getId = function() {
      return jspb.Message.getFieldWithDefault(this, 1, "");
    }, proto.flow.access.GetBlockHeaderByIDRequest.prototype.getId_asB64 = function() {
      return jspb.Message.bytesAsB64(this.getId());
    }, proto.flow.access.GetBlockHeaderByIDRequest.prototype.getId_asU8 = function() {
      return jspb.Message.bytesAsU8(this.getId());
    }, proto.flow.access.GetBlockHeaderByIDRequest.prototype.setId = function(value) {
      return jspb.Message.setProto3BytesField(this, 1, value);
    }, jspb.Message.GENERATE_TO_OBJECT && (proto.flow.access.GetBlockHeaderByHeightRequest.prototype.toObject = function(opt_includeInstance) {
      return proto.flow.access.GetBlockHeaderByHeightRequest.toObject(opt_includeInstance, this);
    }, proto.flow.access.GetBlockHeaderByHeightRequest.toObject = function(includeInstance, msg) {
      var obj = {
        height: jspb.Message.getFieldWithDefault(msg, 1, 0)
      };
      return includeInstance && (obj.$jspbMessageInstance = msg), obj;
    }), proto.flow.access.GetBlockHeaderByHeightRequest.deserializeBinary = function(bytes) {
      var reader = new jspb.BinaryReader(bytes), msg = new proto.flow.access.GetBlockHeaderByHeightRequest;
      return proto.flow.access.GetBlockHeaderByHeightRequest.deserializeBinaryFromReader(msg, reader);
    }, proto.flow.access.GetBlockHeaderByHeightRequest.deserializeBinaryFromReader = function(msg, reader) {
      for (var value; reader.nextField() && !reader.isEndGroup(); ) if (1 === reader.getFieldNumber()) value = reader.readUint64(), 
      msg.setHeight(value); else reader.skipField();
      return msg;
    }, proto.flow.access.GetBlockHeaderByHeightRequest.prototype.serializeBinary = function() {
      var writer = new jspb.BinaryWriter;
      return proto.flow.access.GetBlockHeaderByHeightRequest.serializeBinaryToWriter(this, writer), 
      writer.getResultBuffer();
    }, proto.flow.access.GetBlockHeaderByHeightRequest.serializeBinaryToWriter = function(message, writer) {
      var f;
      0 !== (f = message.getHeight()) && writer.writeUint64(1, f);
    }, proto.flow.access.GetBlockHeaderByHeightRequest.prototype.getHeight = function() {
      return jspb.Message.getFieldWithDefault(this, 1, 0);
    }, proto.flow.access.GetBlockHeaderByHeightRequest.prototype.setHeight = function(value) {
      return jspb.Message.setProto3IntField(this, 1, value);
    }, jspb.Message.GENERATE_TO_OBJECT && (proto.flow.access.BlockHeaderResponse.prototype.toObject = function(opt_includeInstance) {
      return proto.flow.access.BlockHeaderResponse.toObject(opt_includeInstance, this);
    }, proto.flow.access.BlockHeaderResponse.toObject = function(includeInstance, msg) {
      var f, obj = {
        block: (f = msg.getBlock()) && flow_entities_block_header_pb.BlockHeader.toObject(includeInstance, f),
        blockStatus: jspb.Message.getFieldWithDefault(msg, 2, 0),
        metadata: (f = msg.getMetadata()) && flow_entities_metadata_pb.Metadata.toObject(includeInstance, f)
      };
      return includeInstance && (obj.$jspbMessageInstance = msg), obj;
    }), proto.flow.access.BlockHeaderResponse.deserializeBinary = function(bytes) {
      var reader = new jspb.BinaryReader(bytes), msg = new proto.flow.access.BlockHeaderResponse;
      return proto.flow.access.BlockHeaderResponse.deserializeBinaryFromReader(msg, reader);
    }, proto.flow.access.BlockHeaderResponse.deserializeBinaryFromReader = function(msg, reader) {
      for (var value; reader.nextField() && !reader.isEndGroup(); ) switch (reader.getFieldNumber()) {
       case 1:
        value = new flow_entities_block_header_pb.BlockHeader, reader.readMessage(value, flow_entities_block_header_pb.BlockHeader.deserializeBinaryFromReader), 
        msg.setBlock(value);
        break;
       case 2:
        value = reader.readEnum(), msg.setBlockStatus(value);
        break;
       case 3:
        value = new flow_entities_metadata_pb.Metadata, reader.readMessage(value, flow_entities_metadata_pb.Metadata.deserializeBinaryFromReader), 
        msg.setMetadata(value);
        break;
       default:
        reader.skipField();
      }
      return msg;
    }, proto.flow.access.BlockHeaderResponse.prototype.serializeBinary = function() {
      var writer = new jspb.BinaryWriter;
      return proto.flow.access.BlockHeaderResponse.serializeBinaryToWriter(this, writer), 
      writer.getResultBuffer();
    }, proto.flow.access.BlockHeaderResponse.serializeBinaryToWriter = function(message, writer) {
      var f = void 0;
      null != (f = message.getBlock()) && writer.writeMessage(1, f, flow_entities_block_header_pb.BlockHeader.serializeBinaryToWriter), 
      0 !== (f = message.getBlockStatus()) && writer.writeEnum(2, f), null != (f = message.getMetadata()) && writer.writeMessage(3, f, flow_entities_metadata_pb.Metadata.serializeBinaryToWriter);
    }, proto.flow.access.BlockHeaderResponse.prototype.getBlock = function() {
      return jspb.Message.getWrapperField(this, flow_entities_block_header_pb.BlockHeader, 1);
    }, proto.flow.access.BlockHeaderResponse.prototype.setBlock = function(value) {
      return jspb.Message.setWrapperField(this, 1, value);
    }, proto.flow.access.BlockHeaderResponse.prototype.clearBlock = function() {
      return this.setBlock(void 0);
    }, proto.flow.access.BlockHeaderResponse.prototype.hasBlock = function() {
      return null != jspb.Message.getField(this, 1);
    }, proto.flow.access.BlockHeaderResponse.prototype.getBlockStatus = function() {
      return jspb.Message.getFieldWithDefault(this, 2, 0);
    }, proto.flow.access.BlockHeaderResponse.prototype.setBlockStatus = function(value) {
      return jspb.Message.setProto3EnumField(this, 2, value);
    }, proto.flow.access.BlockHeaderResponse.prototype.getMetadata = function() {
      return jspb.Message.getWrapperField(this, flow_entities_metadata_pb.Metadata, 3);
    }, proto.flow.access.BlockHeaderResponse.prototype.setMetadata = function(value) {
      return jspb.Message.setWrapperField(this, 3, value);
    }, proto.flow.access.BlockHeaderResponse.prototype.clearMetadata = function() {
      return this.setMetadata(void 0);
    }, proto.flow.access.BlockHeaderResponse.prototype.hasMetadata = function() {
      return null != jspb.Message.getField(this, 3);
    }, jspb.Message.GENERATE_TO_OBJECT && (proto.flow.access.GetLatestBlockRequest.prototype.toObject = function(opt_includeInstance) {
      return proto.flow.access.GetLatestBlockRequest.toObject(opt_includeInstance, this);
    }, proto.flow.access.GetLatestBlockRequest.toObject = function(includeInstance, msg) {
      var obj = {
        isSealed: jspb.Message.getBooleanFieldWithDefault(msg, 1, !1),
        fullBlockResponse: jspb.Message.getBooleanFieldWithDefault(msg, 2, !1)
      };
      return includeInstance && (obj.$jspbMessageInstance = msg), obj;
    }), proto.flow.access.GetLatestBlockRequest.deserializeBinary = function(bytes) {
      var reader = new jspb.BinaryReader(bytes), msg = new proto.flow.access.GetLatestBlockRequest;
      return proto.flow.access.GetLatestBlockRequest.deserializeBinaryFromReader(msg, reader);
    }, proto.flow.access.GetLatestBlockRequest.deserializeBinaryFromReader = function(msg, reader) {
      for (var value; reader.nextField() && !reader.isEndGroup(); ) switch (reader.getFieldNumber()) {
       case 1:
        value = reader.readBool(), msg.setIsSealed(value);
        break;
       case 2:
        value = reader.readBool(), msg.setFullBlockResponse(value);
        break;
       default:
        reader.skipField();
      }
      return msg;
    }, proto.flow.access.GetLatestBlockRequest.prototype.serializeBinary = function() {
      var writer = new jspb.BinaryWriter;
      return proto.flow.access.GetLatestBlockRequest.serializeBinaryToWriter(this, writer), 
      writer.getResultBuffer();
    }, proto.flow.access.GetLatestBlockRequest.serializeBinaryToWriter = function(message, writer) {
      var f = void 0;
      (f = message.getIsSealed()) && writer.writeBool(1, f), (f = message.getFullBlockResponse()) && writer.writeBool(2, f);
    }, proto.flow.access.GetLatestBlockRequest.prototype.getIsSealed = function() {
      return jspb.Message.getBooleanFieldWithDefault(this, 1, !1);
    }, proto.flow.access.GetLatestBlockRequest.prototype.setIsSealed = function(value) {
      return jspb.Message.setProto3BooleanField(this, 1, value);
    }, proto.flow.access.GetLatestBlockRequest.prototype.getFullBlockResponse = function() {
      return jspb.Message.getBooleanFieldWithDefault(this, 2, !1);
    }, proto.flow.access.GetLatestBlockRequest.prototype.setFullBlockResponse = function(value) {
      return jspb.Message.setProto3BooleanField(this, 2, value);
    }, jspb.Message.GENERATE_TO_OBJECT && (proto.flow.access.GetBlockByIDRequest.prototype.toObject = function(opt_includeInstance) {
      return proto.flow.access.GetBlockByIDRequest.toObject(opt_includeInstance, this);
    }, proto.flow.access.GetBlockByIDRequest.toObject = function(includeInstance, msg) {
      var obj = {
        id: msg.getId_asB64(),
        fullBlockResponse: jspb.Message.getBooleanFieldWithDefault(msg, 2, !1)
      };
      return includeInstance && (obj.$jspbMessageInstance = msg), obj;
    }), proto.flow.access.GetBlockByIDRequest.deserializeBinary = function(bytes) {
      var reader = new jspb.BinaryReader(bytes), msg = new proto.flow.access.GetBlockByIDRequest;
      return proto.flow.access.GetBlockByIDRequest.deserializeBinaryFromReader(msg, reader);
    }, proto.flow.access.GetBlockByIDRequest.deserializeBinaryFromReader = function(msg, reader) {
      for (var value; reader.nextField() && !reader.isEndGroup(); ) switch (reader.getFieldNumber()) {
       case 1:
        value = reader.readBytes(), msg.setId(value);
        break;
       case 2:
        value = reader.readBool(), msg.setFullBlockResponse(value);
        break;
       default:
        reader.skipField();
      }
      return msg;
    }, proto.flow.access.GetBlockByIDRequest.prototype.serializeBinary = function() {
      var writer = new jspb.BinaryWriter;
      return proto.flow.access.GetBlockByIDRequest.serializeBinaryToWriter(this, writer), 
      writer.getResultBuffer();
    }, proto.flow.access.GetBlockByIDRequest.serializeBinaryToWriter = function(message, writer) {
      var f = void 0;
      (f = message.getId_asU8()).length > 0 && writer.writeBytes(1, f), (f = message.getFullBlockResponse()) && writer.writeBool(2, f);
    }, proto.flow.access.GetBlockByIDRequest.prototype.getId = function() {
      return jspb.Message.getFieldWithDefault(this, 1, "");
    }, proto.flow.access.GetBlockByIDRequest.prototype.getId_asB64 = function() {
      return jspb.Message.bytesAsB64(this.getId());
    }, proto.flow.access.GetBlockByIDRequest.prototype.getId_asU8 = function() {
      return jspb.Message.bytesAsU8(this.getId());
    }, proto.flow.access.GetBlockByIDRequest.prototype.setId = function(value) {
      return jspb.Message.setProto3BytesField(this, 1, value);
    }, proto.flow.access.GetBlockByIDRequest.prototype.getFullBlockResponse = function() {
      return jspb.Message.getBooleanFieldWithDefault(this, 2, !1);
    }, proto.flow.access.GetBlockByIDRequest.prototype.setFullBlockResponse = function(value) {
      return jspb.Message.setProto3BooleanField(this, 2, value);
    }, jspb.Message.GENERATE_TO_OBJECT && (proto.flow.access.GetBlockByHeightRequest.prototype.toObject = function(opt_includeInstance) {
      return proto.flow.access.GetBlockByHeightRequest.toObject(opt_includeInstance, this);
    }, proto.flow.access.GetBlockByHeightRequest.toObject = function(includeInstance, msg) {
      var obj = {
        height: jspb.Message.getFieldWithDefault(msg, 1, 0),
        fullBlockResponse: jspb.Message.getBooleanFieldWithDefault(msg, 2, !1)
      };
      return includeInstance && (obj.$jspbMessageInstance = msg), obj;
    }), proto.flow.access.GetBlockByHeightRequest.deserializeBinary = function(bytes) {
      var reader = new jspb.BinaryReader(bytes), msg = new proto.flow.access.GetBlockByHeightRequest;
      return proto.flow.access.GetBlockByHeightRequest.deserializeBinaryFromReader(msg, reader);
    }, proto.flow.access.GetBlockByHeightRequest.deserializeBinaryFromReader = function(msg, reader) {
      for (var value; reader.nextField() && !reader.isEndGroup(); ) switch (reader.getFieldNumber()) {
       case 1:
        value = reader.readUint64(), msg.setHeight(value);
        break;
       case 2:
        value = reader.readBool(), msg.setFullBlockResponse(value);
        break;
       default:
        reader.skipField();
      }
      return msg;
    }, proto.flow.access.GetBlockByHeightRequest.prototype.serializeBinary = function() {
      var writer = new jspb.BinaryWriter;
      return proto.flow.access.GetBlockByHeightRequest.serializeBinaryToWriter(this, writer), 
      writer.getResultBuffer();
    }, proto.flow.access.GetBlockByHeightRequest.serializeBinaryToWriter = function(message, writer) {
      var f = void 0;
      0 !== (f = message.getHeight()) && writer.writeUint64(1, f), (f = message.getFullBlockResponse()) && writer.writeBool(2, f);
    }, proto.flow.access.GetBlockByHeightRequest.prototype.getHeight = function() {
      return jspb.Message.getFieldWithDefault(this, 1, 0);
    }, proto.flow.access.GetBlockByHeightRequest.prototype.setHeight = function(value) {
      return jspb.Message.setProto3IntField(this, 1, value);
    }, proto.flow.access.GetBlockByHeightRequest.prototype.getFullBlockResponse = function() {
      return jspb.Message.getBooleanFieldWithDefault(this, 2, !1);
    }, proto.flow.access.GetBlockByHeightRequest.prototype.setFullBlockResponse = function(value) {
      return jspb.Message.setProto3BooleanField(this, 2, value);
    }, jspb.Message.GENERATE_TO_OBJECT && (proto.flow.access.BlockResponse.prototype.toObject = function(opt_includeInstance) {
      return proto.flow.access.BlockResponse.toObject(opt_includeInstance, this);
    }, proto.flow.access.BlockResponse.toObject = function(includeInstance, msg) {
      var f, obj = {
        block: (f = msg.getBlock()) && flow_entities_block_pb.Block.toObject(includeInstance, f),
        blockStatus: jspb.Message.getFieldWithDefault(msg, 2, 0),
        metadata: (f = msg.getMetadata()) && flow_entities_metadata_pb.Metadata.toObject(includeInstance, f)
      };
      return includeInstance && (obj.$jspbMessageInstance = msg), obj;
    }), proto.flow.access.BlockResponse.deserializeBinary = function(bytes) {
      var reader = new jspb.BinaryReader(bytes), msg = new proto.flow.access.BlockResponse;
      return proto.flow.access.BlockResponse.deserializeBinaryFromReader(msg, reader);
    }, proto.flow.access.BlockResponse.deserializeBinaryFromReader = function(msg, reader) {
      for (var value; reader.nextField() && !reader.isEndGroup(); ) switch (reader.getFieldNumber()) {
       case 1:
        value = new flow_entities_block_pb.Block, reader.readMessage(value, flow_entities_block_pb.Block.deserializeBinaryFromReader), 
        msg.setBlock(value);
        break;
       case 2:
        value = reader.readEnum(), msg.setBlockStatus(value);
        break;
       case 3:
        value = new flow_entities_metadata_pb.Metadata, reader.readMessage(value, flow_entities_metadata_pb.Metadata.deserializeBinaryFromReader), 
        msg.setMetadata(value);
        break;
       default:
        reader.skipField();
      }
      return msg;
    }, proto.flow.access.BlockResponse.prototype.serializeBinary = function() {
      var writer = new jspb.BinaryWriter;
      return proto.flow.access.BlockResponse.serializeBinaryToWriter(this, writer), writer.getResultBuffer();
    }, proto.flow.access.BlockResponse.serializeBinaryToWriter = function(message, writer) {
      var f = void 0;
      null != (f = message.getBlock()) && writer.writeMessage(1, f, flow_entities_block_pb.Block.serializeBinaryToWriter), 
      0 !== (f = message.getBlockStatus()) && writer.writeEnum(2, f), null != (f = message.getMetadata()) && writer.writeMessage(3, f, flow_entities_metadata_pb.Metadata.serializeBinaryToWriter);
    }, proto.flow.access.BlockResponse.prototype.getBlock = function() {
      return jspb.Message.getWrapperField(this, flow_entities_block_pb.Block, 1);
    }, proto.flow.access.BlockResponse.prototype.setBlock = function(value) {
      return jspb.Message.setWrapperField(this, 1, value);
    }, proto.flow.access.BlockResponse.prototype.clearBlock = function() {
      return this.setBlock(void 0);
    }, proto.flow.access.BlockResponse.prototype.hasBlock = function() {
      return null != jspb.Message.getField(this, 1);
    }, proto.flow.access.BlockResponse.prototype.getBlockStatus = function() {
      return jspb.Message.getFieldWithDefault(this, 2, 0);
    }, proto.flow.access.BlockResponse.prototype.setBlockStatus = function(value) {
      return jspb.Message.setProto3EnumField(this, 2, value);
    }, proto.flow.access.BlockResponse.prototype.getMetadata = function() {
      return jspb.Message.getWrapperField(this, flow_entities_metadata_pb.Metadata, 3);
    }, proto.flow.access.BlockResponse.prototype.setMetadata = function(value) {
      return jspb.Message.setWrapperField(this, 3, value);
    }, proto.flow.access.BlockResponse.prototype.clearMetadata = function() {
      return this.setMetadata(void 0);
    }, proto.flow.access.BlockResponse.prototype.hasMetadata = function() {
      return null != jspb.Message.getField(this, 3);
    }, jspb.Message.GENERATE_TO_OBJECT && (proto.flow.access.GetCollectionByIDRequest.prototype.toObject = function(opt_includeInstance) {
      return proto.flow.access.GetCollectionByIDRequest.toObject(opt_includeInstance, this);
    }, proto.flow.access.GetCollectionByIDRequest.toObject = function(includeInstance, msg) {
      var obj = {
        id: msg.getId_asB64()
      };
      return includeInstance && (obj.$jspbMessageInstance = msg), obj;
    }), proto.flow.access.GetCollectionByIDRequest.deserializeBinary = function(bytes) {
      var reader = new jspb.BinaryReader(bytes), msg = new proto.flow.access.GetCollectionByIDRequest;
      return proto.flow.access.GetCollectionByIDRequest.deserializeBinaryFromReader(msg, reader);
    }, proto.flow.access.GetCollectionByIDRequest.deserializeBinaryFromReader = function(msg, reader) {
      for (var value; reader.nextField() && !reader.isEndGroup(); ) if (1 === reader.getFieldNumber()) value = reader.readBytes(), 
      msg.setId(value); else reader.skipField();
      return msg;
    }, proto.flow.access.GetCollectionByIDRequest.prototype.serializeBinary = function() {
      var writer = new jspb.BinaryWriter;
      return proto.flow.access.GetCollectionByIDRequest.serializeBinaryToWriter(this, writer), 
      writer.getResultBuffer();
    }, proto.flow.access.GetCollectionByIDRequest.serializeBinaryToWriter = function(message, writer) {
      var f;
      (f = message.getId_asU8()).length > 0 && writer.writeBytes(1, f);
    }, proto.flow.access.GetCollectionByIDRequest.prototype.getId = function() {
      return jspb.Message.getFieldWithDefault(this, 1, "");
    }, proto.flow.access.GetCollectionByIDRequest.prototype.getId_asB64 = function() {
      return jspb.Message.bytesAsB64(this.getId());
    }, proto.flow.access.GetCollectionByIDRequest.prototype.getId_asU8 = function() {
      return jspb.Message.bytesAsU8(this.getId());
    }, proto.flow.access.GetCollectionByIDRequest.prototype.setId = function(value) {
      return jspb.Message.setProto3BytesField(this, 1, value);
    }, jspb.Message.GENERATE_TO_OBJECT && (proto.flow.access.CollectionResponse.prototype.toObject = function(opt_includeInstance) {
      return proto.flow.access.CollectionResponse.toObject(opt_includeInstance, this);
    }, proto.flow.access.CollectionResponse.toObject = function(includeInstance, msg) {
      var f, obj = {
        collection: (f = msg.getCollection()) && flow_entities_collection_pb.Collection.toObject(includeInstance, f),
        metadata: (f = msg.getMetadata()) && flow_entities_metadata_pb.Metadata.toObject(includeInstance, f)
      };
      return includeInstance && (obj.$jspbMessageInstance = msg), obj;
    }), proto.flow.access.CollectionResponse.deserializeBinary = function(bytes) {
      var reader = new jspb.BinaryReader(bytes), msg = new proto.flow.access.CollectionResponse;
      return proto.flow.access.CollectionResponse.deserializeBinaryFromReader(msg, reader);
    }, proto.flow.access.CollectionResponse.deserializeBinaryFromReader = function(msg, reader) {
      for (var value; reader.nextField() && !reader.isEndGroup(); ) switch (reader.getFieldNumber()) {
       case 1:
        value = new flow_entities_collection_pb.Collection, reader.readMessage(value, flow_entities_collection_pb.Collection.deserializeBinaryFromReader), 
        msg.setCollection(value);
        break;
       case 2:
        value = new flow_entities_metadata_pb.Metadata, reader.readMessage(value, flow_entities_metadata_pb.Metadata.deserializeBinaryFromReader), 
        msg.setMetadata(value);
        break;
       default:
        reader.skipField();
      }
      return msg;
    }, proto.flow.access.CollectionResponse.prototype.serializeBinary = function() {
      var writer = new jspb.BinaryWriter;
      return proto.flow.access.CollectionResponse.serializeBinaryToWriter(this, writer), 
      writer.getResultBuffer();
    }, proto.flow.access.CollectionResponse.serializeBinaryToWriter = function(message, writer) {
      var f = void 0;
      null != (f = message.getCollection()) && writer.writeMessage(1, f, flow_entities_collection_pb.Collection.serializeBinaryToWriter), 
      null != (f = message.getMetadata()) && writer.writeMessage(2, f, flow_entities_metadata_pb.Metadata.serializeBinaryToWriter);
    }, proto.flow.access.CollectionResponse.prototype.getCollection = function() {
      return jspb.Message.getWrapperField(this, flow_entities_collection_pb.Collection, 1);
    }, proto.flow.access.CollectionResponse.prototype.setCollection = function(value) {
      return jspb.Message.setWrapperField(this, 1, value);
    }, proto.flow.access.CollectionResponse.prototype.clearCollection = function() {
      return this.setCollection(void 0);
    }, proto.flow.access.CollectionResponse.prototype.hasCollection = function() {
      return null != jspb.Message.getField(this, 1);
    }, proto.flow.access.CollectionResponse.prototype.getMetadata = function() {
      return jspb.Message.getWrapperField(this, flow_entities_metadata_pb.Metadata, 2);
    }, proto.flow.access.CollectionResponse.prototype.setMetadata = function(value) {
      return jspb.Message.setWrapperField(this, 2, value);
    }, proto.flow.access.CollectionResponse.prototype.clearMetadata = function() {
      return this.setMetadata(void 0);
    }, proto.flow.access.CollectionResponse.prototype.hasMetadata = function() {
      return null != jspb.Message.getField(this, 2);
    }, jspb.Message.GENERATE_TO_OBJECT && (proto.flow.access.SendTransactionRequest.prototype.toObject = function(opt_includeInstance) {
      return proto.flow.access.SendTransactionRequest.toObject(opt_includeInstance, this);
    }, proto.flow.access.SendTransactionRequest.toObject = function(includeInstance, msg) {
      var f, obj = {
        transaction: (f = msg.getTransaction()) && flow_entities_transaction_pb.Transaction.toObject(includeInstance, f)
      };
      return includeInstance && (obj.$jspbMessageInstance = msg), obj;
    }), proto.flow.access.SendTransactionRequest.deserializeBinary = function(bytes) {
      var reader = new jspb.BinaryReader(bytes), msg = new proto.flow.access.SendTransactionRequest;
      return proto.flow.access.SendTransactionRequest.deserializeBinaryFromReader(msg, reader);
    }, proto.flow.access.SendTransactionRequest.deserializeBinaryFromReader = function(msg, reader) {
      for (var value; reader.nextField() && !reader.isEndGroup(); ) if (1 === reader.getFieldNumber()) value = new flow_entities_transaction_pb.Transaction, 
      reader.readMessage(value, flow_entities_transaction_pb.Transaction.deserializeBinaryFromReader), 
      msg.setTransaction(value); else reader.skipField();
      return msg;
    }, proto.flow.access.SendTransactionRequest.prototype.serializeBinary = function() {
      var writer = new jspb.BinaryWriter;
      return proto.flow.access.SendTransactionRequest.serializeBinaryToWriter(this, writer), 
      writer.getResultBuffer();
    }, proto.flow.access.SendTransactionRequest.serializeBinaryToWriter = function(message, writer) {
      var f;
      null != (f = message.getTransaction()) && writer.writeMessage(1, f, flow_entities_transaction_pb.Transaction.serializeBinaryToWriter);
    }, proto.flow.access.SendTransactionRequest.prototype.getTransaction = function() {
      return jspb.Message.getWrapperField(this, flow_entities_transaction_pb.Transaction, 1);
    }, proto.flow.access.SendTransactionRequest.prototype.setTransaction = function(value) {
      return jspb.Message.setWrapperField(this, 1, value);
    }, proto.flow.access.SendTransactionRequest.prototype.clearTransaction = function() {
      return this.setTransaction(void 0);
    }, proto.flow.access.SendTransactionRequest.prototype.hasTransaction = function() {
      return null != jspb.Message.getField(this, 1);
    }, jspb.Message.GENERATE_TO_OBJECT && (proto.flow.access.SendTransactionResponse.prototype.toObject = function(opt_includeInstance) {
      return proto.flow.access.SendTransactionResponse.toObject(opt_includeInstance, this);
    }, proto.flow.access.SendTransactionResponse.toObject = function(includeInstance, msg) {
      var f, obj = {
        id: msg.getId_asB64(),
        metadata: (f = msg.getMetadata()) && flow_entities_metadata_pb.Metadata.toObject(includeInstance, f)
      };
      return includeInstance && (obj.$jspbMessageInstance = msg), obj;
    }), proto.flow.access.SendTransactionResponse.deserializeBinary = function(bytes) {
      var reader = new jspb.BinaryReader(bytes), msg = new proto.flow.access.SendTransactionResponse;
      return proto.flow.access.SendTransactionResponse.deserializeBinaryFromReader(msg, reader);
    }, proto.flow.access.SendTransactionResponse.deserializeBinaryFromReader = function(msg, reader) {
      for (var value; reader.nextField() && !reader.isEndGroup(); ) switch (reader.getFieldNumber()) {
       case 1:
        value = reader.readBytes(), msg.setId(value);
        break;
       case 2:
        value = new flow_entities_metadata_pb.Metadata, reader.readMessage(value, flow_entities_metadata_pb.Metadata.deserializeBinaryFromReader), 
        msg.setMetadata(value);
        break;
       default:
        reader.skipField();
      }
      return msg;
    }, proto.flow.access.SendTransactionResponse.prototype.serializeBinary = function() {
      var writer = new jspb.BinaryWriter;
      return proto.flow.access.SendTransactionResponse.serializeBinaryToWriter(this, writer), 
      writer.getResultBuffer();
    }, proto.flow.access.SendTransactionResponse.serializeBinaryToWriter = function(message, writer) {
      var f = void 0;
      (f = message.getId_asU8()).length > 0 && writer.writeBytes(1, f), null != (f = message.getMetadata()) && writer.writeMessage(2, f, flow_entities_metadata_pb.Metadata.serializeBinaryToWriter);
    }, proto.flow.access.SendTransactionResponse.prototype.getId = function() {
      return jspb.Message.getFieldWithDefault(this, 1, "");
    }, proto.flow.access.SendTransactionResponse.prototype.getId_asB64 = function() {
      return jspb.Message.bytesAsB64(this.getId());
    }, proto.flow.access.SendTransactionResponse.prototype.getId_asU8 = function() {
      return jspb.Message.bytesAsU8(this.getId());
    }, proto.flow.access.SendTransactionResponse.prototype.setId = function(value) {
      return jspb.Message.setProto3BytesField(this, 1, value);
    }, proto.flow.access.SendTransactionResponse.prototype.getMetadata = function() {
      return jspb.Message.getWrapperField(this, flow_entities_metadata_pb.Metadata, 2);
    }, proto.flow.access.SendTransactionResponse.prototype.setMetadata = function(value) {
      return jspb.Message.setWrapperField(this, 2, value);
    }, proto.flow.access.SendTransactionResponse.prototype.clearMetadata = function() {
      return this.setMetadata(void 0);
    }, proto.flow.access.SendTransactionResponse.prototype.hasMetadata = function() {
      return null != jspb.Message.getField(this, 2);
    }, jspb.Message.GENERATE_TO_OBJECT && (proto.flow.access.GetTransactionRequest.prototype.toObject = function(opt_includeInstance) {
      return proto.flow.access.GetTransactionRequest.toObject(opt_includeInstance, this);
    }, proto.flow.access.GetTransactionRequest.toObject = function(includeInstance, msg) {
      var obj = {
        id: msg.getId_asB64(),
        blockId: msg.getBlockId_asB64(),
        collectionId: msg.getCollectionId_asB64(),
        eventEncodingVersion: jspb.Message.getFieldWithDefault(msg, 4, 0)
      };
      return includeInstance && (obj.$jspbMessageInstance = msg), obj;
    }), proto.flow.access.GetTransactionRequest.deserializeBinary = function(bytes) {
      var reader = new jspb.BinaryReader(bytes), msg = new proto.flow.access.GetTransactionRequest;
      return proto.flow.access.GetTransactionRequest.deserializeBinaryFromReader(msg, reader);
    }, proto.flow.access.GetTransactionRequest.deserializeBinaryFromReader = function(msg, reader) {
      for (var value; reader.nextField() && !reader.isEndGroup(); ) switch (reader.getFieldNumber()) {
       case 1:
        value = reader.readBytes(), msg.setId(value);
        break;
       case 2:
        value = reader.readBytes(), msg.setBlockId(value);
        break;
       case 3:
        value = reader.readBytes(), msg.setCollectionId(value);
        break;
       case 4:
        value = reader.readEnum(), msg.setEventEncodingVersion(value);
        break;
       default:
        reader.skipField();
      }
      return msg;
    }, proto.flow.access.GetTransactionRequest.prototype.serializeBinary = function() {
      var writer = new jspb.BinaryWriter;
      return proto.flow.access.GetTransactionRequest.serializeBinaryToWriter(this, writer), 
      writer.getResultBuffer();
    }, proto.flow.access.GetTransactionRequest.serializeBinaryToWriter = function(message, writer) {
      var f = void 0;
      (f = message.getId_asU8()).length > 0 && writer.writeBytes(1, f), (f = message.getBlockId_asU8()).length > 0 && writer.writeBytes(2, f), 
      (f = message.getCollectionId_asU8()).length > 0 && writer.writeBytes(3, f), 0 !== (f = message.getEventEncodingVersion()) && writer.writeEnum(4, f);
    }, proto.flow.access.GetTransactionRequest.prototype.getId = function() {
      return jspb.Message.getFieldWithDefault(this, 1, "");
    }, proto.flow.access.GetTransactionRequest.prototype.getId_asB64 = function() {
      return jspb.Message.bytesAsB64(this.getId());
    }, proto.flow.access.GetTransactionRequest.prototype.getId_asU8 = function() {
      return jspb.Message.bytesAsU8(this.getId());
    }, proto.flow.access.GetTransactionRequest.prototype.setId = function(value) {
      return jspb.Message.setProto3BytesField(this, 1, value);
    }, proto.flow.access.GetTransactionRequest.prototype.getBlockId = function() {
      return jspb.Message.getFieldWithDefault(this, 2, "");
    }, proto.flow.access.GetTransactionRequest.prototype.getBlockId_asB64 = function() {
      return jspb.Message.bytesAsB64(this.getBlockId());
    }, proto.flow.access.GetTransactionRequest.prototype.getBlockId_asU8 = function() {
      return jspb.Message.bytesAsU8(this.getBlockId());
    }, proto.flow.access.GetTransactionRequest.prototype.setBlockId = function(value) {
      return jspb.Message.setProto3BytesField(this, 2, value);
    }, proto.flow.access.GetTransactionRequest.prototype.getCollectionId = function() {
      return jspb.Message.getFieldWithDefault(this, 3, "");
    }, proto.flow.access.GetTransactionRequest.prototype.getCollectionId_asB64 = function() {
      return jspb.Message.bytesAsB64(this.getCollectionId());
    }, proto.flow.access.GetTransactionRequest.prototype.getCollectionId_asU8 = function() {
      return jspb.Message.bytesAsU8(this.getCollectionId());
    }, proto.flow.access.GetTransactionRequest.prototype.setCollectionId = function(value) {
      return jspb.Message.setProto3BytesField(this, 3, value);
    }, proto.flow.access.GetTransactionRequest.prototype.getEventEncodingVersion = function() {
      return jspb.Message.getFieldWithDefault(this, 4, 0);
    }, proto.flow.access.GetTransactionRequest.prototype.setEventEncodingVersion = function(value) {
      return jspb.Message.setProto3EnumField(this, 4, value);
    }, jspb.Message.GENERATE_TO_OBJECT && (proto.flow.access.GetSystemTransactionRequest.prototype.toObject = function(opt_includeInstance) {
      return proto.flow.access.GetSystemTransactionRequest.toObject(opt_includeInstance, this);
    }, proto.flow.access.GetSystemTransactionRequest.toObject = function(includeInstance, msg) {
      var obj = {
        blockId: msg.getBlockId_asB64()
      };
      return includeInstance && (obj.$jspbMessageInstance = msg), obj;
    }), proto.flow.access.GetSystemTransactionRequest.deserializeBinary = function(bytes) {
      var reader = new jspb.BinaryReader(bytes), msg = new proto.flow.access.GetSystemTransactionRequest;
      return proto.flow.access.GetSystemTransactionRequest.deserializeBinaryFromReader(msg, reader);
    }, proto.flow.access.GetSystemTransactionRequest.deserializeBinaryFromReader = function(msg, reader) {
      for (var value; reader.nextField() && !reader.isEndGroup(); ) if (1 === reader.getFieldNumber()) value = reader.readBytes(), 
      msg.setBlockId(value); else reader.skipField();
      return msg;
    }, proto.flow.access.GetSystemTransactionRequest.prototype.serializeBinary = function() {
      var writer = new jspb.BinaryWriter;
      return proto.flow.access.GetSystemTransactionRequest.serializeBinaryToWriter(this, writer), 
      writer.getResultBuffer();
    }, proto.flow.access.GetSystemTransactionRequest.serializeBinaryToWriter = function(message, writer) {
      var f;
      (f = message.getBlockId_asU8()).length > 0 && writer.writeBytes(1, f);
    }, proto.flow.access.GetSystemTransactionRequest.prototype.getBlockId = function() {
      return jspb.Message.getFieldWithDefault(this, 1, "");
    }, proto.flow.access.GetSystemTransactionRequest.prototype.getBlockId_asB64 = function() {
      return jspb.Message.bytesAsB64(this.getBlockId());
    }, proto.flow.access.GetSystemTransactionRequest.prototype.getBlockId_asU8 = function() {
      return jspb.Message.bytesAsU8(this.getBlockId());
    }, proto.flow.access.GetSystemTransactionRequest.prototype.setBlockId = function(value) {
      return jspb.Message.setProto3BytesField(this, 1, value);
    }, jspb.Message.GENERATE_TO_OBJECT && (proto.flow.access.GetSystemTransactionResultRequest.prototype.toObject = function(opt_includeInstance) {
      return proto.flow.access.GetSystemTransactionResultRequest.toObject(opt_includeInstance, this);
    }, proto.flow.access.GetSystemTransactionResultRequest.toObject = function(includeInstance, msg) {
      var obj = {
        blockId: msg.getBlockId_asB64(),
        eventEncodingVersion: jspb.Message.getFieldWithDefault(msg, 2, 0)
      };
      return includeInstance && (obj.$jspbMessageInstance = msg), obj;
    }), proto.flow.access.GetSystemTransactionResultRequest.deserializeBinary = function(bytes) {
      var reader = new jspb.BinaryReader(bytes), msg = new proto.flow.access.GetSystemTransactionResultRequest;
      return proto.flow.access.GetSystemTransactionResultRequest.deserializeBinaryFromReader(msg, reader);
    }, proto.flow.access.GetSystemTransactionResultRequest.deserializeBinaryFromReader = function(msg, reader) {
      for (var value; reader.nextField() && !reader.isEndGroup(); ) switch (reader.getFieldNumber()) {
       case 1:
        value = reader.readBytes(), msg.setBlockId(value);
        break;
       case 2:
        value = reader.readEnum(), msg.setEventEncodingVersion(value);
        break;
       default:
        reader.skipField();
      }
      return msg;
    }, proto.flow.access.GetSystemTransactionResultRequest.prototype.serializeBinary = function() {
      var writer = new jspb.BinaryWriter;
      return proto.flow.access.GetSystemTransactionResultRequest.serializeBinaryToWriter(this, writer), 
      writer.getResultBuffer();
    }, proto.flow.access.GetSystemTransactionResultRequest.serializeBinaryToWriter = function(message, writer) {
      var f = void 0;
      (f = message.getBlockId_asU8()).length > 0 && writer.writeBytes(1, f), 0 !== (f = message.getEventEncodingVersion()) && writer.writeEnum(2, f);
    }, proto.flow.access.GetSystemTransactionResultRequest.prototype.getBlockId = function() {
      return jspb.Message.getFieldWithDefault(this, 1, "");
    }, proto.flow.access.GetSystemTransactionResultRequest.prototype.getBlockId_asB64 = function() {
      return jspb.Message.bytesAsB64(this.getBlockId());
    }, proto.flow.access.GetSystemTransactionResultRequest.prototype.getBlockId_asU8 = function() {
      return jspb.Message.bytesAsU8(this.getBlockId());
    }, proto.flow.access.GetSystemTransactionResultRequest.prototype.setBlockId = function(value) {
      return jspb.Message.setProto3BytesField(this, 1, value);
    }, proto.flow.access.GetSystemTransactionResultRequest.prototype.getEventEncodingVersion = function() {
      return jspb.Message.getFieldWithDefault(this, 2, 0);
    }, proto.flow.access.GetSystemTransactionResultRequest.prototype.setEventEncodingVersion = function(value) {
      return jspb.Message.setProto3EnumField(this, 2, value);
    }, jspb.Message.GENERATE_TO_OBJECT && (proto.flow.access.GetTransactionByIndexRequest.prototype.toObject = function(opt_includeInstance) {
      return proto.flow.access.GetTransactionByIndexRequest.toObject(opt_includeInstance, this);
    }, proto.flow.access.GetTransactionByIndexRequest.toObject = function(includeInstance, msg) {
      var obj = {
        blockId: msg.getBlockId_asB64(),
        index: jspb.Message.getFieldWithDefault(msg, 2, 0),
        eventEncodingVersion: jspb.Message.getFieldWithDefault(msg, 3, 0)
      };
      return includeInstance && (obj.$jspbMessageInstance = msg), obj;
    }), proto.flow.access.GetTransactionByIndexRequest.deserializeBinary = function(bytes) {
      var reader = new jspb.BinaryReader(bytes), msg = new proto.flow.access.GetTransactionByIndexRequest;
      return proto.flow.access.GetTransactionByIndexRequest.deserializeBinaryFromReader(msg, reader);
    }, proto.flow.access.GetTransactionByIndexRequest.deserializeBinaryFromReader = function(msg, reader) {
      for (var value; reader.nextField() && !reader.isEndGroup(); ) switch (reader.getFieldNumber()) {
       case 1:
        value = reader.readBytes(), msg.setBlockId(value);
        break;
       case 2:
        value = reader.readUint32(), msg.setIndex(value);
        break;
       case 3:
        value = reader.readEnum(), msg.setEventEncodingVersion(value);
        break;
       default:
        reader.skipField();
      }
      return msg;
    }, proto.flow.access.GetTransactionByIndexRequest.prototype.serializeBinary = function() {
      var writer = new jspb.BinaryWriter;
      return proto.flow.access.GetTransactionByIndexRequest.serializeBinaryToWriter(this, writer), 
      writer.getResultBuffer();
    }, proto.flow.access.GetTransactionByIndexRequest.serializeBinaryToWriter = function(message, writer) {
      var f = void 0;
      (f = message.getBlockId_asU8()).length > 0 && writer.writeBytes(1, f), 0 !== (f = message.getIndex()) && writer.writeUint32(2, f), 
      0 !== (f = message.getEventEncodingVersion()) && writer.writeEnum(3, f);
    }, proto.flow.access.GetTransactionByIndexRequest.prototype.getBlockId = function() {
      return jspb.Message.getFieldWithDefault(this, 1, "");
    }, proto.flow.access.GetTransactionByIndexRequest.prototype.getBlockId_asB64 = function() {
      return jspb.Message.bytesAsB64(this.getBlockId());
    }, proto.flow.access.GetTransactionByIndexRequest.prototype.getBlockId_asU8 = function() {
      return jspb.Message.bytesAsU8(this.getBlockId());
    }, proto.flow.access.GetTransactionByIndexRequest.prototype.setBlockId = function(value) {
      return jspb.Message.setProto3BytesField(this, 1, value);
    }, proto.flow.access.GetTransactionByIndexRequest.prototype.getIndex = function() {
      return jspb.Message.getFieldWithDefault(this, 2, 0);
    }, proto.flow.access.GetTransactionByIndexRequest.prototype.setIndex = function(value) {
      return jspb.Message.setProto3IntField(this, 2, value);
    }, proto.flow.access.GetTransactionByIndexRequest.prototype.getEventEncodingVersion = function() {
      return jspb.Message.getFieldWithDefault(this, 3, 0);
    }, proto.flow.access.GetTransactionByIndexRequest.prototype.setEventEncodingVersion = function(value) {
      return jspb.Message.setProto3EnumField(this, 3, value);
    }, jspb.Message.GENERATE_TO_OBJECT && (proto.flow.access.GetTransactionsByBlockIDRequest.prototype.toObject = function(opt_includeInstance) {
      return proto.flow.access.GetTransactionsByBlockIDRequest.toObject(opt_includeInstance, this);
    }, proto.flow.access.GetTransactionsByBlockIDRequest.toObject = function(includeInstance, msg) {
      var obj = {
        blockId: msg.getBlockId_asB64(),
        eventEncodingVersion: jspb.Message.getFieldWithDefault(msg, 2, 0)
      };
      return includeInstance && (obj.$jspbMessageInstance = msg), obj;
    }), proto.flow.access.GetTransactionsByBlockIDRequest.deserializeBinary = function(bytes) {
      var reader = new jspb.BinaryReader(bytes), msg = new proto.flow.access.GetTransactionsByBlockIDRequest;
      return proto.flow.access.GetTransactionsByBlockIDRequest.deserializeBinaryFromReader(msg, reader);
    }, proto.flow.access.GetTransactionsByBlockIDRequest.deserializeBinaryFromReader = function(msg, reader) {
      for (var value; reader.nextField() && !reader.isEndGroup(); ) switch (reader.getFieldNumber()) {
       case 1:
        value = reader.readBytes(), msg.setBlockId(value);
        break;
       case 2:
        value = reader.readEnum(), msg.setEventEncodingVersion(value);
        break;
       default:
        reader.skipField();
      }
      return msg;
    }, proto.flow.access.GetTransactionsByBlockIDRequest.prototype.serializeBinary = function() {
      var writer = new jspb.BinaryWriter;
      return proto.flow.access.GetTransactionsByBlockIDRequest.serializeBinaryToWriter(this, writer), 
      writer.getResultBuffer();
    }, proto.flow.access.GetTransactionsByBlockIDRequest.serializeBinaryToWriter = function(message, writer) {
      var f = void 0;
      (f = message.getBlockId_asU8()).length > 0 && writer.writeBytes(1, f), 0 !== (f = message.getEventEncodingVersion()) && writer.writeEnum(2, f);
    }, proto.flow.access.GetTransactionsByBlockIDRequest.prototype.getBlockId = function() {
      return jspb.Message.getFieldWithDefault(this, 1, "");
    }, proto.flow.access.GetTransactionsByBlockIDRequest.prototype.getBlockId_asB64 = function() {
      return jspb.Message.bytesAsB64(this.getBlockId());
    }, proto.flow.access.GetTransactionsByBlockIDRequest.prototype.getBlockId_asU8 = function() {
      return jspb.Message.bytesAsU8(this.getBlockId());
    }, proto.flow.access.GetTransactionsByBlockIDRequest.prototype.setBlockId = function(value) {
      return jspb.Message.setProto3BytesField(this, 1, value);
    }, proto.flow.access.GetTransactionsByBlockIDRequest.prototype.getEventEncodingVersion = function() {
      return jspb.Message.getFieldWithDefault(this, 2, 0);
    }, proto.flow.access.GetTransactionsByBlockIDRequest.prototype.setEventEncodingVersion = function(value) {
      return jspb.Message.setProto3EnumField(this, 2, value);
    }, proto.flow.access.TransactionResultsResponse.repeatedFields_ = [ 1 ], jspb.Message.GENERATE_TO_OBJECT && (proto.flow.access.TransactionResultsResponse.prototype.toObject = function(opt_includeInstance) {
      return proto.flow.access.TransactionResultsResponse.toObject(opt_includeInstance, this);
    }, proto.flow.access.TransactionResultsResponse.toObject = function(includeInstance, msg) {
      var f, obj = {
        transactionResultsList: jspb.Message.toObjectList(msg.getTransactionResultsList(), proto.flow.access.TransactionResultResponse.toObject, includeInstance),
        metadata: (f = msg.getMetadata()) && flow_entities_metadata_pb.Metadata.toObject(includeInstance, f)
      };
      return includeInstance && (obj.$jspbMessageInstance = msg), obj;
    }), proto.flow.access.TransactionResultsResponse.deserializeBinary = function(bytes) {
      var reader = new jspb.BinaryReader(bytes), msg = new proto.flow.access.TransactionResultsResponse;
      return proto.flow.access.TransactionResultsResponse.deserializeBinaryFromReader(msg, reader);
    }, proto.flow.access.TransactionResultsResponse.deserializeBinaryFromReader = function(msg, reader) {
      for (var value; reader.nextField() && !reader.isEndGroup(); ) switch (reader.getFieldNumber()) {
       case 1:
        value = new proto.flow.access.TransactionResultResponse, reader.readMessage(value, proto.flow.access.TransactionResultResponse.deserializeBinaryFromReader), 
        msg.addTransactionResults(value);
        break;
       case 2:
        value = new flow_entities_metadata_pb.Metadata, reader.readMessage(value, flow_entities_metadata_pb.Metadata.deserializeBinaryFromReader), 
        msg.setMetadata(value);
        break;
       default:
        reader.skipField();
      }
      return msg;
    }, proto.flow.access.TransactionResultsResponse.prototype.serializeBinary = function() {
      var writer = new jspb.BinaryWriter;
      return proto.flow.access.TransactionResultsResponse.serializeBinaryToWriter(this, writer), 
      writer.getResultBuffer();
    }, proto.flow.access.TransactionResultsResponse.serializeBinaryToWriter = function(message, writer) {
      var f = void 0;
      (f = message.getTransactionResultsList()).length > 0 && writer.writeRepeatedMessage(1, f, proto.flow.access.TransactionResultResponse.serializeBinaryToWriter), 
      null != (f = message.getMetadata()) && writer.writeMessage(2, f, flow_entities_metadata_pb.Metadata.serializeBinaryToWriter);
    }, proto.flow.access.TransactionResultsResponse.prototype.getTransactionResultsList = function() {
      return jspb.Message.getRepeatedWrapperField(this, proto.flow.access.TransactionResultResponse, 1);
    }, proto.flow.access.TransactionResultsResponse.prototype.setTransactionResultsList = function(value) {
      return jspb.Message.setRepeatedWrapperField(this, 1, value);
    }, proto.flow.access.TransactionResultsResponse.prototype.addTransactionResults = function(opt_value, opt_index) {
      return jspb.Message.addToRepeatedWrapperField(this, 1, opt_value, proto.flow.access.TransactionResultResponse, opt_index);
    }, proto.flow.access.TransactionResultsResponse.prototype.clearTransactionResultsList = function() {
      return this.setTransactionResultsList([]);
    }, proto.flow.access.TransactionResultsResponse.prototype.getMetadata = function() {
      return jspb.Message.getWrapperField(this, flow_entities_metadata_pb.Metadata, 2);
    }, proto.flow.access.TransactionResultsResponse.prototype.setMetadata = function(value) {
      return jspb.Message.setWrapperField(this, 2, value);
    }, proto.flow.access.TransactionResultsResponse.prototype.clearMetadata = function() {
      return this.setMetadata(void 0);
    }, proto.flow.access.TransactionResultsResponse.prototype.hasMetadata = function() {
      return null != jspb.Message.getField(this, 2);
    }, proto.flow.access.TransactionsResponse.repeatedFields_ = [ 1 ], jspb.Message.GENERATE_TO_OBJECT && (proto.flow.access.TransactionsResponse.prototype.toObject = function(opt_includeInstance) {
      return proto.flow.access.TransactionsResponse.toObject(opt_includeInstance, this);
    }, proto.flow.access.TransactionsResponse.toObject = function(includeInstance, msg) {
      var f, obj = {
        transactionsList: jspb.Message.toObjectList(msg.getTransactionsList(), flow_entities_transaction_pb.Transaction.toObject, includeInstance),
        metadata: (f = msg.getMetadata()) && flow_entities_metadata_pb.Metadata.toObject(includeInstance, f)
      };
      return includeInstance && (obj.$jspbMessageInstance = msg), obj;
    }), proto.flow.access.TransactionsResponse.deserializeBinary = function(bytes) {
      var reader = new jspb.BinaryReader(bytes), msg = new proto.flow.access.TransactionsResponse;
      return proto.flow.access.TransactionsResponse.deserializeBinaryFromReader(msg, reader);
    }, proto.flow.access.TransactionsResponse.deserializeBinaryFromReader = function(msg, reader) {
      for (var value; reader.nextField() && !reader.isEndGroup(); ) switch (reader.getFieldNumber()) {
       case 1:
        value = new flow_entities_transaction_pb.Transaction, reader.readMessage(value, flow_entities_transaction_pb.Transaction.deserializeBinaryFromReader), 
        msg.addTransactions(value);
        break;
       case 2:
        value = new flow_entities_metadata_pb.Metadata, reader.readMessage(value, flow_entities_metadata_pb.Metadata.deserializeBinaryFromReader), 
        msg.setMetadata(value);
        break;
       default:
        reader.skipField();
      }
      return msg;
    }, proto.flow.access.TransactionsResponse.prototype.serializeBinary = function() {
      var writer = new jspb.BinaryWriter;
      return proto.flow.access.TransactionsResponse.serializeBinaryToWriter(this, writer), 
      writer.getResultBuffer();
    }, proto.flow.access.TransactionsResponse.serializeBinaryToWriter = function(message, writer) {
      var f = void 0;
      (f = message.getTransactionsList()).length > 0 && writer.writeRepeatedMessage(1, f, flow_entities_transaction_pb.Transaction.serializeBinaryToWriter), 
      null != (f = message.getMetadata()) && writer.writeMessage(2, f, flow_entities_metadata_pb.Metadata.serializeBinaryToWriter);
    }, proto.flow.access.TransactionsResponse.prototype.getTransactionsList = function() {
      return jspb.Message.getRepeatedWrapperField(this, flow_entities_transaction_pb.Transaction, 1);
    }, proto.flow.access.TransactionsResponse.prototype.setTransactionsList = function(value) {
      return jspb.Message.setRepeatedWrapperField(this, 1, value);
    }, proto.flow.access.TransactionsResponse.prototype.addTransactions = function(opt_value, opt_index) {
      return jspb.Message.addToRepeatedWrapperField(this, 1, opt_value, proto.flow.entities.Transaction, opt_index);
    }, proto.flow.access.TransactionsResponse.prototype.clearTransactionsList = function() {
      return this.setTransactionsList([]);
    }, proto.flow.access.TransactionsResponse.prototype.getMetadata = function() {
      return jspb.Message.getWrapperField(this, flow_entities_metadata_pb.Metadata, 2);
    }, proto.flow.access.TransactionsResponse.prototype.setMetadata = function(value) {
      return jspb.Message.setWrapperField(this, 2, value);
    }, proto.flow.access.TransactionsResponse.prototype.clearMetadata = function() {
      return this.setMetadata(void 0);
    }, proto.flow.access.TransactionsResponse.prototype.hasMetadata = function() {
      return null != jspb.Message.getField(this, 2);
    }, jspb.Message.GENERATE_TO_OBJECT && (proto.flow.access.TransactionResponse.prototype.toObject = function(opt_includeInstance) {
      return proto.flow.access.TransactionResponse.toObject(opt_includeInstance, this);
    }, proto.flow.access.TransactionResponse.toObject = function(includeInstance, msg) {
      var f, obj = {
        transaction: (f = msg.getTransaction()) && flow_entities_transaction_pb.Transaction.toObject(includeInstance, f),
        metadata: (f = msg.getMetadata()) && flow_entities_metadata_pb.Metadata.toObject(includeInstance, f)
      };
      return includeInstance && (obj.$jspbMessageInstance = msg), obj;
    }), proto.flow.access.TransactionResponse.deserializeBinary = function(bytes) {
      var reader = new jspb.BinaryReader(bytes), msg = new proto.flow.access.TransactionResponse;
      return proto.flow.access.TransactionResponse.deserializeBinaryFromReader(msg, reader);
    }, proto.flow.access.TransactionResponse.deserializeBinaryFromReader = function(msg, reader) {
      for (var value; reader.nextField() && !reader.isEndGroup(); ) switch (reader.getFieldNumber()) {
       case 1:
        value = new flow_entities_transaction_pb.Transaction, reader.readMessage(value, flow_entities_transaction_pb.Transaction.deserializeBinaryFromReader), 
        msg.setTransaction(value);
        break;
       case 2:
        value = new flow_entities_metadata_pb.Metadata, reader.readMessage(value, flow_entities_metadata_pb.Metadata.deserializeBinaryFromReader), 
        msg.setMetadata(value);
        break;
       default:
        reader.skipField();
      }
      return msg;
    }, proto.flow.access.TransactionResponse.prototype.serializeBinary = function() {
      var writer = new jspb.BinaryWriter;
      return proto.flow.access.TransactionResponse.serializeBinaryToWriter(this, writer), 
      writer.getResultBuffer();
    }, proto.flow.access.TransactionResponse.serializeBinaryToWriter = function(message, writer) {
      var f = void 0;
      null != (f = message.getTransaction()) && writer.writeMessage(1, f, flow_entities_transaction_pb.Transaction.serializeBinaryToWriter), 
      null != (f = message.getMetadata()) && writer.writeMessage(2, f, flow_entities_metadata_pb.Metadata.serializeBinaryToWriter);
    }, proto.flow.access.TransactionResponse.prototype.getTransaction = function() {
      return jspb.Message.getWrapperField(this, flow_entities_transaction_pb.Transaction, 1);
    }, proto.flow.access.TransactionResponse.prototype.setTransaction = function(value) {
      return jspb.Message.setWrapperField(this, 1, value);
    }, proto.flow.access.TransactionResponse.prototype.clearTransaction = function() {
      return this.setTransaction(void 0);
    }, proto.flow.access.TransactionResponse.prototype.hasTransaction = function() {
      return null != jspb.Message.getField(this, 1);
    }, proto.flow.access.TransactionResponse.prototype.getMetadata = function() {
      return jspb.Message.getWrapperField(this, flow_entities_metadata_pb.Metadata, 2);
    }, proto.flow.access.TransactionResponse.prototype.setMetadata = function(value) {
      return jspb.Message.setWrapperField(this, 2, value);
    }, proto.flow.access.TransactionResponse.prototype.clearMetadata = function() {
      return this.setMetadata(void 0);
    }, proto.flow.access.TransactionResponse.prototype.hasMetadata = function() {
      return null != jspb.Message.getField(this, 2);
    }, proto.flow.access.TransactionResultResponse.repeatedFields_ = [ 4 ], jspb.Message.GENERATE_TO_OBJECT && (proto.flow.access.TransactionResultResponse.prototype.toObject = function(opt_includeInstance) {
      return proto.flow.access.TransactionResultResponse.toObject(opt_includeInstance, this);
    }, proto.flow.access.TransactionResultResponse.toObject = function(includeInstance, msg) {
      var f, obj = {
        status: jspb.Message.getFieldWithDefault(msg, 1, 0),
        statusCode: jspb.Message.getFieldWithDefault(msg, 2, 0),
        errorMessage: jspb.Message.getFieldWithDefault(msg, 3, ""),
        eventsList: jspb.Message.toObjectList(msg.getEventsList(), flow_entities_event_pb.Event.toObject, includeInstance),
        blockId: msg.getBlockId_asB64(),
        transactionId: msg.getTransactionId_asB64(),
        collectionId: msg.getCollectionId_asB64(),
        blockHeight: jspb.Message.getFieldWithDefault(msg, 8, 0),
        metadata: (f = msg.getMetadata()) && flow_entities_metadata_pb.Metadata.toObject(includeInstance, f),
        computationUsage: jspb.Message.getFieldWithDefault(msg, 10, 0)
      };
      return includeInstance && (obj.$jspbMessageInstance = msg), obj;
    }), proto.flow.access.TransactionResultResponse.deserializeBinary = function(bytes) {
      var reader = new jspb.BinaryReader(bytes), msg = new proto.flow.access.TransactionResultResponse;
      return proto.flow.access.TransactionResultResponse.deserializeBinaryFromReader(msg, reader);
    }, proto.flow.access.TransactionResultResponse.deserializeBinaryFromReader = function(msg, reader) {
      for (var value; reader.nextField() && !reader.isEndGroup(); ) switch (reader.getFieldNumber()) {
       case 1:
        value = reader.readEnum(), msg.setStatus(value);
        break;
       case 2:
        value = reader.readUint32(), msg.setStatusCode(value);
        break;
       case 3:
        value = reader.readString(), msg.setErrorMessage(value);
        break;
       case 4:
        value = new flow_entities_event_pb.Event, reader.readMessage(value, flow_entities_event_pb.Event.deserializeBinaryFromReader), 
        msg.addEvents(value);
        break;
       case 5:
        value = reader.readBytes(), msg.setBlockId(value);
        break;
       case 6:
        value = reader.readBytes(), msg.setTransactionId(value);
        break;
       case 7:
        value = reader.readBytes(), msg.setCollectionId(value);
        break;
       case 8:
        value = reader.readUint64(), msg.setBlockHeight(value);
        break;
       case 9:
        value = new flow_entities_metadata_pb.Metadata, reader.readMessage(value, flow_entities_metadata_pb.Metadata.deserializeBinaryFromReader), 
        msg.setMetadata(value);
        break;
       case 10:
        value = reader.readUint64(), msg.setComputationUsage(value);
        break;
       default:
        reader.skipField();
      }
      return msg;
    }, proto.flow.access.TransactionResultResponse.prototype.serializeBinary = function() {
      var writer = new jspb.BinaryWriter;
      return proto.flow.access.TransactionResultResponse.serializeBinaryToWriter(this, writer), 
      writer.getResultBuffer();
    }, proto.flow.access.TransactionResultResponse.serializeBinaryToWriter = function(message, writer) {
      var f = void 0;
      0 !== (f = message.getStatus()) && writer.writeEnum(1, f), 0 !== (f = message.getStatusCode()) && writer.writeUint32(2, f), 
      (f = message.getErrorMessage()).length > 0 && writer.writeString(3, f), (f = message.getEventsList()).length > 0 && writer.writeRepeatedMessage(4, f, flow_entities_event_pb.Event.serializeBinaryToWriter), 
      (f = message.getBlockId_asU8()).length > 0 && writer.writeBytes(5, f), (f = message.getTransactionId_asU8()).length > 0 && writer.writeBytes(6, f), 
      (f = message.getCollectionId_asU8()).length > 0 && writer.writeBytes(7, f), 0 !== (f = message.getBlockHeight()) && writer.writeUint64(8, f), 
      null != (f = message.getMetadata()) && writer.writeMessage(9, f, flow_entities_metadata_pb.Metadata.serializeBinaryToWriter), 
      0 !== (f = message.getComputationUsage()) && writer.writeUint64(10, f);
    }, proto.flow.access.TransactionResultResponse.prototype.getStatus = function() {
      return jspb.Message.getFieldWithDefault(this, 1, 0);
    }, proto.flow.access.TransactionResultResponse.prototype.setStatus = function(value) {
      return jspb.Message.setProto3EnumField(this, 1, value);
    }, proto.flow.access.TransactionResultResponse.prototype.getStatusCode = function() {
      return jspb.Message.getFieldWithDefault(this, 2, 0);
    }, proto.flow.access.TransactionResultResponse.prototype.setStatusCode = function(value) {
      return jspb.Message.setProto3IntField(this, 2, value);
    }, proto.flow.access.TransactionResultResponse.prototype.getErrorMessage = function() {
      return jspb.Message.getFieldWithDefault(this, 3, "");
    }, proto.flow.access.TransactionResultResponse.prototype.setErrorMessage = function(value) {
      return jspb.Message.setProto3StringField(this, 3, value);
    }, proto.flow.access.TransactionResultResponse.prototype.getEventsList = function() {
      return jspb.Message.getRepeatedWrapperField(this, flow_entities_event_pb.Event, 4);
    }, proto.flow.access.TransactionResultResponse.prototype.setEventsList = function(value) {
      return jspb.Message.setRepeatedWrapperField(this, 4, value);
    }, proto.flow.access.TransactionResultResponse.prototype.addEvents = function(opt_value, opt_index) {
      return jspb.Message.addToRepeatedWrapperField(this, 4, opt_value, proto.flow.entities.Event, opt_index);
    }, proto.flow.access.TransactionResultResponse.prototype.clearEventsList = function() {
      return this.setEventsList([]);
    }, proto.flow.access.TransactionResultResponse.prototype.getBlockId = function() {
      return jspb.Message.getFieldWithDefault(this, 5, "");
    }, proto.flow.access.TransactionResultResponse.prototype.getBlockId_asB64 = function() {
      return jspb.Message.bytesAsB64(this.getBlockId());
    }, proto.flow.access.TransactionResultResponse.prototype.getBlockId_asU8 = function() {
      return jspb.Message.bytesAsU8(this.getBlockId());
    }, proto.flow.access.TransactionResultResponse.prototype.setBlockId = function(value) {
      return jspb.Message.setProto3BytesField(this, 5, value);
    }, proto.flow.access.TransactionResultResponse.prototype.getTransactionId = function() {
      return jspb.Message.getFieldWithDefault(this, 6, "");
    }, proto.flow.access.TransactionResultResponse.prototype.getTransactionId_asB64 = function() {
      return jspb.Message.bytesAsB64(this.getTransactionId());
    }, proto.flow.access.TransactionResultResponse.prototype.getTransactionId_asU8 = function() {
      return jspb.Message.bytesAsU8(this.getTransactionId());
    }, proto.flow.access.TransactionResultResponse.prototype.setTransactionId = function(value) {
      return jspb.Message.setProto3BytesField(this, 6, value);
    }, proto.flow.access.TransactionResultResponse.prototype.getCollectionId = function() {
      return jspb.Message.getFieldWithDefault(this, 7, "");
    }, proto.flow.access.TransactionResultResponse.prototype.getCollectionId_asB64 = function() {
      return jspb.Message.bytesAsB64(this.getCollectionId());
    }, proto.flow.access.TransactionResultResponse.prototype.getCollectionId_asU8 = function() {
      return jspb.Message.bytesAsU8(this.getCollectionId());
    }, proto.flow.access.TransactionResultResponse.prototype.setCollectionId = function(value) {
      return jspb.Message.setProto3BytesField(this, 7, value);
    }, proto.flow.access.TransactionResultResponse.prototype.getBlockHeight = function() {
      return jspb.Message.getFieldWithDefault(this, 8, 0);
    }, proto.flow.access.TransactionResultResponse.prototype.setBlockHeight = function(value) {
      return jspb.Message.setProto3IntField(this, 8, value);
    }, proto.flow.access.TransactionResultResponse.prototype.getMetadata = function() {
      return jspb.Message.getWrapperField(this, flow_entities_metadata_pb.Metadata, 9);
    }, proto.flow.access.TransactionResultResponse.prototype.setMetadata = function(value) {
      return jspb.Message.setWrapperField(this, 9, value);
    }, proto.flow.access.TransactionResultResponse.prototype.clearMetadata = function() {
      return this.setMetadata(void 0);
    }, proto.flow.access.TransactionResultResponse.prototype.hasMetadata = function() {
      return null != jspb.Message.getField(this, 9);
    }, proto.flow.access.TransactionResultResponse.prototype.getComputationUsage = function() {
      return jspb.Message.getFieldWithDefault(this, 10, 0);
    }, proto.flow.access.TransactionResultResponse.prototype.setComputationUsage = function(value) {
      return jspb.Message.setProto3IntField(this, 10, value);
    }, jspb.Message.GENERATE_TO_OBJECT && (proto.flow.access.GetAccountRequest.prototype.toObject = function(opt_includeInstance) {
      return proto.flow.access.GetAccountRequest.toObject(opt_includeInstance, this);
    }, proto.flow.access.GetAccountRequest.toObject = function(includeInstance, msg) {
      var obj = {
        address: msg.getAddress_asB64()
      };
      return includeInstance && (obj.$jspbMessageInstance = msg), obj;
    }), proto.flow.access.GetAccountRequest.deserializeBinary = function(bytes) {
      var reader = new jspb.BinaryReader(bytes), msg = new proto.flow.access.GetAccountRequest;
      return proto.flow.access.GetAccountRequest.deserializeBinaryFromReader(msg, reader);
    }, proto.flow.access.GetAccountRequest.deserializeBinaryFromReader = function(msg, reader) {
      for (var value; reader.nextField() && !reader.isEndGroup(); ) if (1 === reader.getFieldNumber()) value = reader.readBytes(), 
      msg.setAddress(value); else reader.skipField();
      return msg;
    }, proto.flow.access.GetAccountRequest.prototype.serializeBinary = function() {
      var writer = new jspb.BinaryWriter;
      return proto.flow.access.GetAccountRequest.serializeBinaryToWriter(this, writer), 
      writer.getResultBuffer();
    }, proto.flow.access.GetAccountRequest.serializeBinaryToWriter = function(message, writer) {
      var f;
      (f = message.getAddress_asU8()).length > 0 && writer.writeBytes(1, f);
    }, proto.flow.access.GetAccountRequest.prototype.getAddress = function() {
      return jspb.Message.getFieldWithDefault(this, 1, "");
    }, proto.flow.access.GetAccountRequest.prototype.getAddress_asB64 = function() {
      return jspb.Message.bytesAsB64(this.getAddress());
    }, proto.flow.access.GetAccountRequest.prototype.getAddress_asU8 = function() {
      return jspb.Message.bytesAsU8(this.getAddress());
    }, proto.flow.access.GetAccountRequest.prototype.setAddress = function(value) {
      return jspb.Message.setProto3BytesField(this, 1, value);
    }, jspb.Message.GENERATE_TO_OBJECT && (proto.flow.access.GetAccountResponse.prototype.toObject = function(opt_includeInstance) {
      return proto.flow.access.GetAccountResponse.toObject(opt_includeInstance, this);
    }, proto.flow.access.GetAccountResponse.toObject = function(includeInstance, msg) {
      var f, obj = {
        account: (f = msg.getAccount()) && flow_entities_account_pb.Account.toObject(includeInstance, f),
        metadata: (f = msg.getMetadata()) && flow_entities_metadata_pb.Metadata.toObject(includeInstance, f)
      };
      return includeInstance && (obj.$jspbMessageInstance = msg), obj;
    }), proto.flow.access.GetAccountResponse.deserializeBinary = function(bytes) {
      var reader = new jspb.BinaryReader(bytes), msg = new proto.flow.access.GetAccountResponse;
      return proto.flow.access.GetAccountResponse.deserializeBinaryFromReader(msg, reader);
    }, proto.flow.access.GetAccountResponse.deserializeBinaryFromReader = function(msg, reader) {
      for (var value; reader.nextField() && !reader.isEndGroup(); ) switch (reader.getFieldNumber()) {
       case 1:
        value = new flow_entities_account_pb.Account, reader.readMessage(value, flow_entities_account_pb.Account.deserializeBinaryFromReader), 
        msg.setAccount(value);
        break;
       case 2:
        value = new flow_entities_metadata_pb.Metadata, reader.readMessage(value, flow_entities_metadata_pb.Metadata.deserializeBinaryFromReader), 
        msg.setMetadata(value);
        break;
       default:
        reader.skipField();
      }
      return msg;
    }, proto.flow.access.GetAccountResponse.prototype.serializeBinary = function() {
      var writer = new jspb.BinaryWriter;
      return proto.flow.access.GetAccountResponse.serializeBinaryToWriter(this, writer), 
      writer.getResultBuffer();
    }, proto.flow.access.GetAccountResponse.serializeBinaryToWriter = function(message, writer) {
      var f = void 0;
      null != (f = message.getAccount()) && writer.writeMessage(1, f, flow_entities_account_pb.Account.serializeBinaryToWriter), 
      null != (f = message.getMetadata()) && writer.writeMessage(2, f, flow_entities_metadata_pb.Metadata.serializeBinaryToWriter);
    }, proto.flow.access.GetAccountResponse.prototype.getAccount = function() {
      return jspb.Message.getWrapperField(this, flow_entities_account_pb.Account, 1);
    }, proto.flow.access.GetAccountResponse.prototype.setAccount = function(value) {
      return jspb.Message.setWrapperField(this, 1, value);
    }, proto.flow.access.GetAccountResponse.prototype.clearAccount = function() {
      return this.setAccount(void 0);
    }, proto.flow.access.GetAccountResponse.prototype.hasAccount = function() {
      return null != jspb.Message.getField(this, 1);
    }, proto.flow.access.GetAccountResponse.prototype.getMetadata = function() {
      return jspb.Message.getWrapperField(this, flow_entities_metadata_pb.Metadata, 2);
    }, proto.flow.access.GetAccountResponse.prototype.setMetadata = function(value) {
      return jspb.Message.setWrapperField(this, 2, value);
    }, proto.flow.access.GetAccountResponse.prototype.clearMetadata = function() {
      return this.setMetadata(void 0);
    }, proto.flow.access.GetAccountResponse.prototype.hasMetadata = function() {
      return null != jspb.Message.getField(this, 2);
    }, jspb.Message.GENERATE_TO_OBJECT && (proto.flow.access.GetAccountAtLatestBlockRequest.prototype.toObject = function(opt_includeInstance) {
      return proto.flow.access.GetAccountAtLatestBlockRequest.toObject(opt_includeInstance, this);
    }, proto.flow.access.GetAccountAtLatestBlockRequest.toObject = function(includeInstance, msg) {
      var obj = {
        address: msg.getAddress_asB64()
      };
      return includeInstance && (obj.$jspbMessageInstance = msg), obj;
    }), proto.flow.access.GetAccountAtLatestBlockRequest.deserializeBinary = function(bytes) {
      var reader = new jspb.BinaryReader(bytes), msg = new proto.flow.access.GetAccountAtLatestBlockRequest;
      return proto.flow.access.GetAccountAtLatestBlockRequest.deserializeBinaryFromReader(msg, reader);
    }, proto.flow.access.GetAccountAtLatestBlockRequest.deserializeBinaryFromReader = function(msg, reader) {
      for (var value; reader.nextField() && !reader.isEndGroup(); ) if (1 === reader.getFieldNumber()) value = reader.readBytes(), 
      msg.setAddress(value); else reader.skipField();
      return msg;
    }, proto.flow.access.GetAccountAtLatestBlockRequest.prototype.serializeBinary = function() {
      var writer = new jspb.BinaryWriter;
      return proto.flow.access.GetAccountAtLatestBlockRequest.serializeBinaryToWriter(this, writer), 
      writer.getResultBuffer();
    }, proto.flow.access.GetAccountAtLatestBlockRequest.serializeBinaryToWriter = function(message, writer) {
      var f;
      (f = message.getAddress_asU8()).length > 0 && writer.writeBytes(1, f);
    }, proto.flow.access.GetAccountAtLatestBlockRequest.prototype.getAddress = function() {
      return jspb.Message.getFieldWithDefault(this, 1, "");
    }, proto.flow.access.GetAccountAtLatestBlockRequest.prototype.getAddress_asB64 = function() {
      return jspb.Message.bytesAsB64(this.getAddress());
    }, proto.flow.access.GetAccountAtLatestBlockRequest.prototype.getAddress_asU8 = function() {
      return jspb.Message.bytesAsU8(this.getAddress());
    }, proto.flow.access.GetAccountAtLatestBlockRequest.prototype.setAddress = function(value) {
      return jspb.Message.setProto3BytesField(this, 1, value);
    }, jspb.Message.GENERATE_TO_OBJECT && (proto.flow.access.AccountResponse.prototype.toObject = function(opt_includeInstance) {
      return proto.flow.access.AccountResponse.toObject(opt_includeInstance, this);
    }, proto.flow.access.AccountResponse.toObject = function(includeInstance, msg) {
      var f, obj = {
        account: (f = msg.getAccount()) && flow_entities_account_pb.Account.toObject(includeInstance, f),
        metadata: (f = msg.getMetadata()) && flow_entities_metadata_pb.Metadata.toObject(includeInstance, f)
      };
      return includeInstance && (obj.$jspbMessageInstance = msg), obj;
    }), proto.flow.access.AccountResponse.deserializeBinary = function(bytes) {
      var reader = new jspb.BinaryReader(bytes), msg = new proto.flow.access.AccountResponse;
      return proto.flow.access.AccountResponse.deserializeBinaryFromReader(msg, reader);
    }, proto.flow.access.AccountResponse.deserializeBinaryFromReader = function(msg, reader) {
      for (var value; reader.nextField() && !reader.isEndGroup(); ) switch (reader.getFieldNumber()) {
       case 1:
        value = new flow_entities_account_pb.Account, reader.readMessage(value, flow_entities_account_pb.Account.deserializeBinaryFromReader), 
        msg.setAccount(value);
        break;
       case 2:
        value = new flow_entities_metadata_pb.Metadata, reader.readMessage(value, flow_entities_metadata_pb.Metadata.deserializeBinaryFromReader), 
        msg.setMetadata(value);
        break;
       default:
        reader.skipField();
      }
      return msg;
    }, proto.flow.access.AccountResponse.prototype.serializeBinary = function() {
      var writer = new jspb.BinaryWriter;
      return proto.flow.access.AccountResponse.serializeBinaryToWriter(this, writer), 
      writer.getResultBuffer();
    }, proto.flow.access.AccountResponse.serializeBinaryToWriter = function(message, writer) {
      var f = void 0;
      null != (f = message.getAccount()) && writer.writeMessage(1, f, flow_entities_account_pb.Account.serializeBinaryToWriter), 
      null != (f = message.getMetadata()) && writer.writeMessage(2, f, flow_entities_metadata_pb.Metadata.serializeBinaryToWriter);
    }, proto.flow.access.AccountResponse.prototype.getAccount = function() {
      return jspb.Message.getWrapperField(this, flow_entities_account_pb.Account, 1);
    }, proto.flow.access.AccountResponse.prototype.setAccount = function(value) {
      return jspb.Message.setWrapperField(this, 1, value);
    }, proto.flow.access.AccountResponse.prototype.clearAccount = function() {
      return this.setAccount(void 0);
    }, proto.flow.access.AccountResponse.prototype.hasAccount = function() {
      return null != jspb.Message.getField(this, 1);
    }, proto.flow.access.AccountResponse.prototype.getMetadata = function() {
      return jspb.Message.getWrapperField(this, flow_entities_metadata_pb.Metadata, 2);
    }, proto.flow.access.AccountResponse.prototype.setMetadata = function(value) {
      return jspb.Message.setWrapperField(this, 2, value);
    }, proto.flow.access.AccountResponse.prototype.clearMetadata = function() {
      return this.setMetadata(void 0);
    }, proto.flow.access.AccountResponse.prototype.hasMetadata = function() {
      return null != jspb.Message.getField(this, 2);
    }, jspb.Message.GENERATE_TO_OBJECT && (proto.flow.access.GetAccountAtBlockHeightRequest.prototype.toObject = function(opt_includeInstance) {
      return proto.flow.access.GetAccountAtBlockHeightRequest.toObject(opt_includeInstance, this);
    }, proto.flow.access.GetAccountAtBlockHeightRequest.toObject = function(includeInstance, msg) {
      var obj = {
        address: msg.getAddress_asB64(),
        blockHeight: jspb.Message.getFieldWithDefault(msg, 2, 0)
      };
      return includeInstance && (obj.$jspbMessageInstance = msg), obj;
    }), proto.flow.access.GetAccountAtBlockHeightRequest.deserializeBinary = function(bytes) {
      var reader = new jspb.BinaryReader(bytes), msg = new proto.flow.access.GetAccountAtBlockHeightRequest;
      return proto.flow.access.GetAccountAtBlockHeightRequest.deserializeBinaryFromReader(msg, reader);
    }, proto.flow.access.GetAccountAtBlockHeightRequest.deserializeBinaryFromReader = function(msg, reader) {
      for (var value; reader.nextField() && !reader.isEndGroup(); ) switch (reader.getFieldNumber()) {
       case 1:
        value = reader.readBytes(), msg.setAddress(value);
        break;
       case 2:
        value = reader.readUint64(), msg.setBlockHeight(value);
        break;
       default:
        reader.skipField();
      }
      return msg;
    }, proto.flow.access.GetAccountAtBlockHeightRequest.prototype.serializeBinary = function() {
      var writer = new jspb.BinaryWriter;
      return proto.flow.access.GetAccountAtBlockHeightRequest.serializeBinaryToWriter(this, writer), 
      writer.getResultBuffer();
    }, proto.flow.access.GetAccountAtBlockHeightRequest.serializeBinaryToWriter = function(message, writer) {
      var f = void 0;
      (f = message.getAddress_asU8()).length > 0 && writer.writeBytes(1, f), 0 !== (f = message.getBlockHeight()) && writer.writeUint64(2, f);
    }, proto.flow.access.GetAccountAtBlockHeightRequest.prototype.getAddress = function() {
      return jspb.Message.getFieldWithDefault(this, 1, "");
    }, proto.flow.access.GetAccountAtBlockHeightRequest.prototype.getAddress_asB64 = function() {
      return jspb.Message.bytesAsB64(this.getAddress());
    }, proto.flow.access.GetAccountAtBlockHeightRequest.prototype.getAddress_asU8 = function() {
      return jspb.Message.bytesAsU8(this.getAddress());
    }, proto.flow.access.GetAccountAtBlockHeightRequest.prototype.setAddress = function(value) {
      return jspb.Message.setProto3BytesField(this, 1, value);
    }, proto.flow.access.GetAccountAtBlockHeightRequest.prototype.getBlockHeight = function() {
      return jspb.Message.getFieldWithDefault(this, 2, 0);
    }, proto.flow.access.GetAccountAtBlockHeightRequest.prototype.setBlockHeight = function(value) {
      return jspb.Message.setProto3IntField(this, 2, value);
    }, proto.flow.access.ExecuteScriptAtLatestBlockRequest.repeatedFields_ = [ 2 ], 
    jspb.Message.GENERATE_TO_OBJECT && (proto.flow.access.ExecuteScriptAtLatestBlockRequest.prototype.toObject = function(opt_includeInstance) {
      return proto.flow.access.ExecuteScriptAtLatestBlockRequest.toObject(opt_includeInstance, this);
    }, proto.flow.access.ExecuteScriptAtLatestBlockRequest.toObject = function(includeInstance, msg) {
      var obj = {
        script: msg.getScript_asB64(),
        argumentsList: msg.getArgumentsList_asB64()
      };
      return includeInstance && (obj.$jspbMessageInstance = msg), obj;
    }), proto.flow.access.ExecuteScriptAtLatestBlockRequest.deserializeBinary = function(bytes) {
      var reader = new jspb.BinaryReader(bytes), msg = new proto.flow.access.ExecuteScriptAtLatestBlockRequest;
      return proto.flow.access.ExecuteScriptAtLatestBlockRequest.deserializeBinaryFromReader(msg, reader);
    }, proto.flow.access.ExecuteScriptAtLatestBlockRequest.deserializeBinaryFromReader = function(msg, reader) {
      for (var value; reader.nextField() && !reader.isEndGroup(); ) switch (reader.getFieldNumber()) {
       case 1:
        value = reader.readBytes(), msg.setScript(value);
        break;
       case 2:
        value = reader.readBytes(), msg.addArguments(value);
        break;
       default:
        reader.skipField();
      }
      return msg;
    }, proto.flow.access.ExecuteScriptAtLatestBlockRequest.prototype.serializeBinary = function() {
      var writer = new jspb.BinaryWriter;
      return proto.flow.access.ExecuteScriptAtLatestBlockRequest.serializeBinaryToWriter(this, writer), 
      writer.getResultBuffer();
    }, proto.flow.access.ExecuteScriptAtLatestBlockRequest.serializeBinaryToWriter = function(message, writer) {
      var f = void 0;
      (f = message.getScript_asU8()).length > 0 && writer.writeBytes(1, f), (f = message.getArgumentsList_asU8()).length > 0 && writer.writeRepeatedBytes(2, f);
    }, proto.flow.access.ExecuteScriptAtLatestBlockRequest.prototype.getScript = function() {
      return jspb.Message.getFieldWithDefault(this, 1, "");
    }, proto.flow.access.ExecuteScriptAtLatestBlockRequest.prototype.getScript_asB64 = function() {
      return jspb.Message.bytesAsB64(this.getScript());
    }, proto.flow.access.ExecuteScriptAtLatestBlockRequest.prototype.getScript_asU8 = function() {
      return jspb.Message.bytesAsU8(this.getScript());
    }, proto.flow.access.ExecuteScriptAtLatestBlockRequest.prototype.setScript = function(value) {
      return jspb.Message.setProto3BytesField(this, 1, value);
    }, proto.flow.access.ExecuteScriptAtLatestBlockRequest.prototype.getArgumentsList = function() {
      return jspb.Message.getRepeatedField(this, 2);
    }, proto.flow.access.ExecuteScriptAtLatestBlockRequest.prototype.getArgumentsList_asB64 = function() {
      return jspb.Message.bytesListAsB64(this.getArgumentsList());
    }, proto.flow.access.ExecuteScriptAtLatestBlockRequest.prototype.getArgumentsList_asU8 = function() {
      return jspb.Message.bytesListAsU8(this.getArgumentsList());
    }, proto.flow.access.ExecuteScriptAtLatestBlockRequest.prototype.setArgumentsList = function(value) {
      return jspb.Message.setField(this, 2, value || []);
    }, proto.flow.access.ExecuteScriptAtLatestBlockRequest.prototype.addArguments = function(value, opt_index) {
      return jspb.Message.addToRepeatedField(this, 2, value, opt_index);
    }, proto.flow.access.ExecuteScriptAtLatestBlockRequest.prototype.clearArgumentsList = function() {
      return this.setArgumentsList([]);
    }, proto.flow.access.ExecuteScriptAtBlockIDRequest.repeatedFields_ = [ 3 ], jspb.Message.GENERATE_TO_OBJECT && (proto.flow.access.ExecuteScriptAtBlockIDRequest.prototype.toObject = function(opt_includeInstance) {
      return proto.flow.access.ExecuteScriptAtBlockIDRequest.toObject(opt_includeInstance, this);
    }, proto.flow.access.ExecuteScriptAtBlockIDRequest.toObject = function(includeInstance, msg) {
      var obj = {
        blockId: msg.getBlockId_asB64(),
        script: msg.getScript_asB64(),
        argumentsList: msg.getArgumentsList_asB64()
      };
      return includeInstance && (obj.$jspbMessageInstance = msg), obj;
    }), proto.flow.access.ExecuteScriptAtBlockIDRequest.deserializeBinary = function(bytes) {
      var reader = new jspb.BinaryReader(bytes), msg = new proto.flow.access.ExecuteScriptAtBlockIDRequest;
      return proto.flow.access.ExecuteScriptAtBlockIDRequest.deserializeBinaryFromReader(msg, reader);
    }, proto.flow.access.ExecuteScriptAtBlockIDRequest.deserializeBinaryFromReader = function(msg, reader) {
      for (var value; reader.nextField() && !reader.isEndGroup(); ) switch (reader.getFieldNumber()) {
       case 1:
        value = reader.readBytes(), msg.setBlockId(value);
        break;
       case 2:
        value = reader.readBytes(), msg.setScript(value);
        break;
       case 3:
        value = reader.readBytes(), msg.addArguments(value);
        break;
       default:
        reader.skipField();
      }
      return msg;
    }, proto.flow.access.ExecuteScriptAtBlockIDRequest.prototype.serializeBinary = function() {
      var writer = new jspb.BinaryWriter;
      return proto.flow.access.ExecuteScriptAtBlockIDRequest.serializeBinaryToWriter(this, writer), 
      writer.getResultBuffer();
    }, proto.flow.access.ExecuteScriptAtBlockIDRequest.serializeBinaryToWriter = function(message, writer) {
      var f = void 0;
      (f = message.getBlockId_asU8()).length > 0 && writer.writeBytes(1, f), (f = message.getScript_asU8()).length > 0 && writer.writeBytes(2, f), 
      (f = message.getArgumentsList_asU8()).length > 0 && writer.writeRepeatedBytes(3, f);
    }, proto.flow.access.ExecuteScriptAtBlockIDRequest.prototype.getBlockId = function() {
      return jspb.Message.getFieldWithDefault(this, 1, "");
    }, proto.flow.access.ExecuteScriptAtBlockIDRequest.prototype.getBlockId_asB64 = function() {
      return jspb.Message.bytesAsB64(this.getBlockId());
    }, proto.flow.access.ExecuteScriptAtBlockIDRequest.prototype.getBlockId_asU8 = function() {
      return jspb.Message.bytesAsU8(this.getBlockId());
    }, proto.flow.access.ExecuteScriptAtBlockIDRequest.prototype.setBlockId = function(value) {
      return jspb.Message.setProto3BytesField(this, 1, value);
    }, proto.flow.access.ExecuteScriptAtBlockIDRequest.prototype.getScript = function() {
      return jspb.Message.getFieldWithDefault(this, 2, "");
    }, proto.flow.access.ExecuteScriptAtBlockIDRequest.prototype.getScript_asB64 = function() {
      return jspb.Message.bytesAsB64(this.getScript());
    }, proto.flow.access.ExecuteScriptAtBlockIDRequest.prototype.getScript_asU8 = function() {
      return jspb.Message.bytesAsU8(this.getScript());
    }, proto.flow.access.ExecuteScriptAtBlockIDRequest.prototype.setScript = function(value) {
      return jspb.Message.setProto3BytesField(this, 2, value);
    }, proto.flow.access.ExecuteScriptAtBlockIDRequest.prototype.getArgumentsList = function() {
      return jspb.Message.getRepeatedField(this, 3);
    }, proto.flow.access.ExecuteScriptAtBlockIDRequest.prototype.getArgumentsList_asB64 = function() {
      return jspb.Message.bytesListAsB64(this.getArgumentsList());
    }, proto.flow.access.ExecuteScriptAtBlockIDRequest.prototype.getArgumentsList_asU8 = function() {
      return jspb.Message.bytesListAsU8(this.getArgumentsList());
    }, proto.flow.access.ExecuteScriptAtBlockIDRequest.prototype.setArgumentsList = function(value) {
      return jspb.Message.setField(this, 3, value || []);
    }, proto.flow.access.ExecuteScriptAtBlockIDRequest.prototype.addArguments = function(value, opt_index) {
      return jspb.Message.addToRepeatedField(this, 3, value, opt_index);
    }, proto.flow.access.ExecuteScriptAtBlockIDRequest.prototype.clearArgumentsList = function() {
      return this.setArgumentsList([]);
    }, proto.flow.access.ExecuteScriptAtBlockHeightRequest.repeatedFields_ = [ 3 ], 
    jspb.Message.GENERATE_TO_OBJECT && (proto.flow.access.ExecuteScriptAtBlockHeightRequest.prototype.toObject = function(opt_includeInstance) {
      return proto.flow.access.ExecuteScriptAtBlockHeightRequest.toObject(opt_includeInstance, this);
    }, proto.flow.access.ExecuteScriptAtBlockHeightRequest.toObject = function(includeInstance, msg) {
      var obj = {
        blockHeight: jspb.Message.getFieldWithDefault(msg, 1, 0),
        script: msg.getScript_asB64(),
        argumentsList: msg.getArgumentsList_asB64()
      };
      return includeInstance && (obj.$jspbMessageInstance = msg), obj;
    }), proto.flow.access.ExecuteScriptAtBlockHeightRequest.deserializeBinary = function(bytes) {
      var reader = new jspb.BinaryReader(bytes), msg = new proto.flow.access.ExecuteScriptAtBlockHeightRequest;
      return proto.flow.access.ExecuteScriptAtBlockHeightRequest.deserializeBinaryFromReader(msg, reader);
    }, proto.flow.access.ExecuteScriptAtBlockHeightRequest.deserializeBinaryFromReader = function(msg, reader) {
      for (var value; reader.nextField() && !reader.isEndGroup(); ) switch (reader.getFieldNumber()) {
       case 1:
        value = reader.readUint64(), msg.setBlockHeight(value);
        break;
       case 2:
        value = reader.readBytes(), msg.setScript(value);
        break;
       case 3:
        value = reader.readBytes(), msg.addArguments(value);
        break;
       default:
        reader.skipField();
      }
      return msg;
    }, proto.flow.access.ExecuteScriptAtBlockHeightRequest.prototype.serializeBinary = function() {
      var writer = new jspb.BinaryWriter;
      return proto.flow.access.ExecuteScriptAtBlockHeightRequest.serializeBinaryToWriter(this, writer), 
      writer.getResultBuffer();
    }, proto.flow.access.ExecuteScriptAtBlockHeightRequest.serializeBinaryToWriter = function(message, writer) {
      var f = void 0;
      0 !== (f = message.getBlockHeight()) && writer.writeUint64(1, f), (f = message.getScript_asU8()).length > 0 && writer.writeBytes(2, f), 
      (f = message.getArgumentsList_asU8()).length > 0 && writer.writeRepeatedBytes(3, f);
    }, proto.flow.access.ExecuteScriptAtBlockHeightRequest.prototype.getBlockHeight = function() {
      return jspb.Message.getFieldWithDefault(this, 1, 0);
    }, proto.flow.access.ExecuteScriptAtBlockHeightRequest.prototype.setBlockHeight = function(value) {
      return jspb.Message.setProto3IntField(this, 1, value);
    }, proto.flow.access.ExecuteScriptAtBlockHeightRequest.prototype.getScript = function() {
      return jspb.Message.getFieldWithDefault(this, 2, "");
    }, proto.flow.access.ExecuteScriptAtBlockHeightRequest.prototype.getScript_asB64 = function() {
      return jspb.Message.bytesAsB64(this.getScript());
    }, proto.flow.access.ExecuteScriptAtBlockHeightRequest.prototype.getScript_asU8 = function() {
      return jspb.Message.bytesAsU8(this.getScript());
    }, proto.flow.access.ExecuteScriptAtBlockHeightRequest.prototype.setScript = function(value) {
      return jspb.Message.setProto3BytesField(this, 2, value);
    }, proto.flow.access.ExecuteScriptAtBlockHeightRequest.prototype.getArgumentsList = function() {
      return jspb.Message.getRepeatedField(this, 3);
    }, proto.flow.access.ExecuteScriptAtBlockHeightRequest.prototype.getArgumentsList_asB64 = function() {
      return jspb.Message.bytesListAsB64(this.getArgumentsList());
    }, proto.flow.access.ExecuteScriptAtBlockHeightRequest.prototype.getArgumentsList_asU8 = function() {
      return jspb.Message.bytesListAsU8(this.getArgumentsList());
    }, proto.flow.access.ExecuteScriptAtBlockHeightRequest.prototype.setArgumentsList = function(value) {
      return jspb.Message.setField(this, 3, value || []);
    }, proto.flow.access.ExecuteScriptAtBlockHeightRequest.prototype.addArguments = function(value, opt_index) {
      return jspb.Message.addToRepeatedField(this, 3, value, opt_index);
    }, proto.flow.access.ExecuteScriptAtBlockHeightRequest.prototype.clearArgumentsList = function() {
      return this.setArgumentsList([]);
    }, jspb.Message.GENERATE_TO_OBJECT && (proto.flow.access.ExecuteScriptResponse.prototype.toObject = function(opt_includeInstance) {
      return proto.flow.access.ExecuteScriptResponse.toObject(opt_includeInstance, this);
    }, proto.flow.access.ExecuteScriptResponse.toObject = function(includeInstance, msg) {
      var f, obj = {
        value: msg.getValue_asB64(),
        metadata: (f = msg.getMetadata()) && flow_entities_metadata_pb.Metadata.toObject(includeInstance, f),
        computationUsage: jspb.Message.getFieldWithDefault(msg, 3, 0)
      };
      return includeInstance && (obj.$jspbMessageInstance = msg), obj;
    }), proto.flow.access.ExecuteScriptResponse.deserializeBinary = function(bytes) {
      var reader = new jspb.BinaryReader(bytes), msg = new proto.flow.access.ExecuteScriptResponse;
      return proto.flow.access.ExecuteScriptResponse.deserializeBinaryFromReader(msg, reader);
    }, proto.flow.access.ExecuteScriptResponse.deserializeBinaryFromReader = function(msg, reader) {
      for (var value; reader.nextField() && !reader.isEndGroup(); ) switch (reader.getFieldNumber()) {
       case 1:
        value = reader.readBytes(), msg.setValue(value);
        break;
       case 2:
        value = new flow_entities_metadata_pb.Metadata, reader.readMessage(value, flow_entities_metadata_pb.Metadata.deserializeBinaryFromReader), 
        msg.setMetadata(value);
        break;
       case 3:
        value = reader.readUint64(), msg.setComputationUsage(value);
        break;
       default:
        reader.skipField();
      }
      return msg;
    }, proto.flow.access.ExecuteScriptResponse.prototype.serializeBinary = function() {
      var writer = new jspb.BinaryWriter;
      return proto.flow.access.ExecuteScriptResponse.serializeBinaryToWriter(this, writer), 
      writer.getResultBuffer();
    }, proto.flow.access.ExecuteScriptResponse.serializeBinaryToWriter = function(message, writer) {
      var f = void 0;
      (f = message.getValue_asU8()).length > 0 && writer.writeBytes(1, f), null != (f = message.getMetadata()) && writer.writeMessage(2, f, flow_entities_metadata_pb.Metadata.serializeBinaryToWriter), 
      0 !== (f = message.getComputationUsage()) && writer.writeUint64(3, f);
    }, proto.flow.access.ExecuteScriptResponse.prototype.getValue = function() {
      return jspb.Message.getFieldWithDefault(this, 1, "");
    }, proto.flow.access.ExecuteScriptResponse.prototype.getValue_asB64 = function() {
      return jspb.Message.bytesAsB64(this.getValue());
    }, proto.flow.access.ExecuteScriptResponse.prototype.getValue_asU8 = function() {
      return jspb.Message.bytesAsU8(this.getValue());
    }, proto.flow.access.ExecuteScriptResponse.prototype.setValue = function(value) {
      return jspb.Message.setProto3BytesField(this, 1, value);
    }, proto.flow.access.ExecuteScriptResponse.prototype.getMetadata = function() {
      return jspb.Message.getWrapperField(this, flow_entities_metadata_pb.Metadata, 2);
    }, proto.flow.access.ExecuteScriptResponse.prototype.setMetadata = function(value) {
      return jspb.Message.setWrapperField(this, 2, value);
    }, proto.flow.access.ExecuteScriptResponse.prototype.clearMetadata = function() {
      return this.setMetadata(void 0);
    }, proto.flow.access.ExecuteScriptResponse.prototype.hasMetadata = function() {
      return null != jspb.Message.getField(this, 2);
    }, proto.flow.access.ExecuteScriptResponse.prototype.getComputationUsage = function() {
      return jspb.Message.getFieldWithDefault(this, 3, 0);
    }, proto.flow.access.ExecuteScriptResponse.prototype.setComputationUsage = function(value) {
      return jspb.Message.setProto3IntField(this, 3, value);
    }, jspb.Message.GENERATE_TO_OBJECT && (proto.flow.access.GetEventsForHeightRangeRequest.prototype.toObject = function(opt_includeInstance) {
      return proto.flow.access.GetEventsForHeightRangeRequest.toObject(opt_includeInstance, this);
    }, proto.flow.access.GetEventsForHeightRangeRequest.toObject = function(includeInstance, msg) {
      var obj = {
        type: jspb.Message.getFieldWithDefault(msg, 1, ""),
        startHeight: jspb.Message.getFieldWithDefault(msg, 2, 0),
        endHeight: jspb.Message.getFieldWithDefault(msg, 3, 0),
        eventEncodingVersion: jspb.Message.getFieldWithDefault(msg, 4, 0)
      };
      return includeInstance && (obj.$jspbMessageInstance = msg), obj;
    }), proto.flow.access.GetEventsForHeightRangeRequest.deserializeBinary = function(bytes) {
      var reader = new jspb.BinaryReader(bytes), msg = new proto.flow.access.GetEventsForHeightRangeRequest;
      return proto.flow.access.GetEventsForHeightRangeRequest.deserializeBinaryFromReader(msg, reader);
    }, proto.flow.access.GetEventsForHeightRangeRequest.deserializeBinaryFromReader = function(msg, reader) {
      for (var value; reader.nextField() && !reader.isEndGroup(); ) switch (reader.getFieldNumber()) {
       case 1:
        value = reader.readString(), msg.setType(value);
        break;
       case 2:
        value = reader.readUint64(), msg.setStartHeight(value);
        break;
       case 3:
        value = reader.readUint64(), msg.setEndHeight(value);
        break;
       case 4:
        value = reader.readEnum(), msg.setEventEncodingVersion(value);
        break;
       default:
        reader.skipField();
      }
      return msg;
    }, proto.flow.access.GetEventsForHeightRangeRequest.prototype.serializeBinary = function() {
      var writer = new jspb.BinaryWriter;
      return proto.flow.access.GetEventsForHeightRangeRequest.serializeBinaryToWriter(this, writer), 
      writer.getResultBuffer();
    }, proto.flow.access.GetEventsForHeightRangeRequest.serializeBinaryToWriter = function(message, writer) {
      var f = void 0;
      (f = message.getType()).length > 0 && writer.writeString(1, f), 0 !== (f = message.getStartHeight()) && writer.writeUint64(2, f), 
      0 !== (f = message.getEndHeight()) && writer.writeUint64(3, f), 0 !== (f = message.getEventEncodingVersion()) && writer.writeEnum(4, f);
    }, proto.flow.access.GetEventsForHeightRangeRequest.prototype.getType = function() {
      return jspb.Message.getFieldWithDefault(this, 1, "");
    }, proto.flow.access.GetEventsForHeightRangeRequest.prototype.setType = function(value) {
      return jspb.Message.setProto3StringField(this, 1, value);
    }, proto.flow.access.GetEventsForHeightRangeRequest.prototype.getStartHeight = function() {
      return jspb.Message.getFieldWithDefault(this, 2, 0);
    }, proto.flow.access.GetEventsForHeightRangeRequest.prototype.setStartHeight = function(value) {
      return jspb.Message.setProto3IntField(this, 2, value);
    }, proto.flow.access.GetEventsForHeightRangeRequest.prototype.getEndHeight = function() {
      return jspb.Message.getFieldWithDefault(this, 3, 0);
    }, proto.flow.access.GetEventsForHeightRangeRequest.prototype.setEndHeight = function(value) {
      return jspb.Message.setProto3IntField(this, 3, value);
    }, proto.flow.access.GetEventsForHeightRangeRequest.prototype.getEventEncodingVersion = function() {
      return jspb.Message.getFieldWithDefault(this, 4, 0);
    }, proto.flow.access.GetEventsForHeightRangeRequest.prototype.setEventEncodingVersion = function(value) {
      return jspb.Message.setProto3EnumField(this, 4, value);
    }, proto.flow.access.GetEventsForBlockIDsRequest.repeatedFields_ = [ 2 ], jspb.Message.GENERATE_TO_OBJECT && (proto.flow.access.GetEventsForBlockIDsRequest.prototype.toObject = function(opt_includeInstance) {
      return proto.flow.access.GetEventsForBlockIDsRequest.toObject(opt_includeInstance, this);
    }, proto.flow.access.GetEventsForBlockIDsRequest.toObject = function(includeInstance, msg) {
      var obj = {
        type: jspb.Message.getFieldWithDefault(msg, 1, ""),
        blockIdsList: msg.getBlockIdsList_asB64(),
        eventEncodingVersion: jspb.Message.getFieldWithDefault(msg, 3, 0)
      };
      return includeInstance && (obj.$jspbMessageInstance = msg), obj;
    }), proto.flow.access.GetEventsForBlockIDsRequest.deserializeBinary = function(bytes) {
      var reader = new jspb.BinaryReader(bytes), msg = new proto.flow.access.GetEventsForBlockIDsRequest;
      return proto.flow.access.GetEventsForBlockIDsRequest.deserializeBinaryFromReader(msg, reader);
    }, proto.flow.access.GetEventsForBlockIDsRequest.deserializeBinaryFromReader = function(msg, reader) {
      for (var value; reader.nextField() && !reader.isEndGroup(); ) switch (reader.getFieldNumber()) {
       case 1:
        value = reader.readString(), msg.setType(value);
        break;
       case 2:
        value = reader.readBytes(), msg.addBlockIds(value);
        break;
       case 3:
        value = reader.readEnum(), msg.setEventEncodingVersion(value);
        break;
       default:
        reader.skipField();
      }
      return msg;
    }, proto.flow.access.GetEventsForBlockIDsRequest.prototype.serializeBinary = function() {
      var writer = new jspb.BinaryWriter;
      return proto.flow.access.GetEventsForBlockIDsRequest.serializeBinaryToWriter(this, writer), 
      writer.getResultBuffer();
    }, proto.flow.access.GetEventsForBlockIDsRequest.serializeBinaryToWriter = function(message, writer) {
      var f = void 0;
      (f = message.getType()).length > 0 && writer.writeString(1, f), (f = message.getBlockIdsList_asU8()).length > 0 && writer.writeRepeatedBytes(2, f), 
      0 !== (f = message.getEventEncodingVersion()) && writer.writeEnum(3, f);
    }, proto.flow.access.GetEventsForBlockIDsRequest.prototype.getType = function() {
      return jspb.Message.getFieldWithDefault(this, 1, "");
    }, proto.flow.access.GetEventsForBlockIDsRequest.prototype.setType = function(value) {
      return jspb.Message.setProto3StringField(this, 1, value);
    }, proto.flow.access.GetEventsForBlockIDsRequest.prototype.getBlockIdsList = function() {
      return jspb.Message.getRepeatedField(this, 2);
    }, proto.flow.access.GetEventsForBlockIDsRequest.prototype.getBlockIdsList_asB64 = function() {
      return jspb.Message.bytesListAsB64(this.getBlockIdsList());
    }, proto.flow.access.GetEventsForBlockIDsRequest.prototype.getBlockIdsList_asU8 = function() {
      return jspb.Message.bytesListAsU8(this.getBlockIdsList());
    }, proto.flow.access.GetEventsForBlockIDsRequest.prototype.setBlockIdsList = function(value) {
      return jspb.Message.setField(this, 2, value || []);
    }, proto.flow.access.GetEventsForBlockIDsRequest.prototype.addBlockIds = function(value, opt_index) {
      return jspb.Message.addToRepeatedField(this, 2, value, opt_index);
    }, proto.flow.access.GetEventsForBlockIDsRequest.prototype.clearBlockIdsList = function() {
      return this.setBlockIdsList([]);
    }, proto.flow.access.GetEventsForBlockIDsRequest.prototype.getEventEncodingVersion = function() {
      return jspb.Message.getFieldWithDefault(this, 3, 0);
    }, proto.flow.access.GetEventsForBlockIDsRequest.prototype.setEventEncodingVersion = function(value) {
      return jspb.Message.setProto3EnumField(this, 3, value);
    }, proto.flow.access.EventsResponse.repeatedFields_ = [ 1 ], jspb.Message.GENERATE_TO_OBJECT && (proto.flow.access.EventsResponse.prototype.toObject = function(opt_includeInstance) {
      return proto.flow.access.EventsResponse.toObject(opt_includeInstance, this);
    }, proto.flow.access.EventsResponse.toObject = function(includeInstance, msg) {
      var f, obj = {
        resultsList: jspb.Message.toObjectList(msg.getResultsList(), proto.flow.access.EventsResponse.Result.toObject, includeInstance),
        metadata: (f = msg.getMetadata()) && flow_entities_metadata_pb.Metadata.toObject(includeInstance, f)
      };
      return includeInstance && (obj.$jspbMessageInstance = msg), obj;
    }), proto.flow.access.EventsResponse.deserializeBinary = function(bytes) {
      var reader = new jspb.BinaryReader(bytes), msg = new proto.flow.access.EventsResponse;
      return proto.flow.access.EventsResponse.deserializeBinaryFromReader(msg, reader);
    }, proto.flow.access.EventsResponse.deserializeBinaryFromReader = function(msg, reader) {
      for (var value; reader.nextField() && !reader.isEndGroup(); ) switch (reader.getFieldNumber()) {
       case 1:
        value = new proto.flow.access.EventsResponse.Result, reader.readMessage(value, proto.flow.access.EventsResponse.Result.deserializeBinaryFromReader), 
        msg.addResults(value);
        break;
       case 2:
        value = new flow_entities_metadata_pb.Metadata, reader.readMessage(value, flow_entities_metadata_pb.Metadata.deserializeBinaryFromReader), 
        msg.setMetadata(value);
        break;
       default:
        reader.skipField();
      }
      return msg;
    }, proto.flow.access.EventsResponse.prototype.serializeBinary = function() {
      var writer = new jspb.BinaryWriter;
      return proto.flow.access.EventsResponse.serializeBinaryToWriter(this, writer), writer.getResultBuffer();
    }, proto.flow.access.EventsResponse.serializeBinaryToWriter = function(message, writer) {
      var f = void 0;
      (f = message.getResultsList()).length > 0 && writer.writeRepeatedMessage(1, f, proto.flow.access.EventsResponse.Result.serializeBinaryToWriter), 
      null != (f = message.getMetadata()) && writer.writeMessage(2, f, flow_entities_metadata_pb.Metadata.serializeBinaryToWriter);
    }, proto.flow.access.EventsResponse.Result.repeatedFields_ = [ 3 ], jspb.Message.GENERATE_TO_OBJECT && (proto.flow.access.EventsResponse.Result.prototype.toObject = function(opt_includeInstance) {
      return proto.flow.access.EventsResponse.Result.toObject(opt_includeInstance, this);
    }, proto.flow.access.EventsResponse.Result.toObject = function(includeInstance, msg) {
      var f, obj = {
        blockId: msg.getBlockId_asB64(),
        blockHeight: jspb.Message.getFieldWithDefault(msg, 2, 0),
        eventsList: jspb.Message.toObjectList(msg.getEventsList(), flow_entities_event_pb.Event.toObject, includeInstance),
        blockTimestamp: (f = msg.getBlockTimestamp()) && google_protobuf_timestamp_pb.Timestamp.toObject(includeInstance, f)
      };
      return includeInstance && (obj.$jspbMessageInstance = msg), obj;
    }), proto.flow.access.EventsResponse.Result.deserializeBinary = function(bytes) {
      var reader = new jspb.BinaryReader(bytes), msg = new proto.flow.access.EventsResponse.Result;
      return proto.flow.access.EventsResponse.Result.deserializeBinaryFromReader(msg, reader);
    }, proto.flow.access.EventsResponse.Result.deserializeBinaryFromReader = function(msg, reader) {
      for (var value; reader.nextField() && !reader.isEndGroup(); ) switch (reader.getFieldNumber()) {
       case 1:
        value = reader.readBytes(), msg.setBlockId(value);
        break;
       case 2:
        value = reader.readUint64(), msg.setBlockHeight(value);
        break;
       case 3:
        value = new flow_entities_event_pb.Event, reader.readMessage(value, flow_entities_event_pb.Event.deserializeBinaryFromReader), 
        msg.addEvents(value);
        break;
       case 4:
        value = new google_protobuf_timestamp_pb.Timestamp, reader.readMessage(value, google_protobuf_timestamp_pb.Timestamp.deserializeBinaryFromReader), 
        msg.setBlockTimestamp(value);
        break;
       default:
        reader.skipField();
      }
      return msg;
    }, proto.flow.access.EventsResponse.Result.prototype.serializeBinary = function() {
      var writer = new jspb.BinaryWriter;
      return proto.flow.access.EventsResponse.Result.serializeBinaryToWriter(this, writer), 
      writer.getResultBuffer();
    }, proto.flow.access.EventsResponse.Result.serializeBinaryToWriter = function(message, writer) {
      var f = void 0;
      (f = message.getBlockId_asU8()).length > 0 && writer.writeBytes(1, f), 0 !== (f = message.getBlockHeight()) && writer.writeUint64(2, f), 
      (f = message.getEventsList()).length > 0 && writer.writeRepeatedMessage(3, f, flow_entities_event_pb.Event.serializeBinaryToWriter), 
      null != (f = message.getBlockTimestamp()) && writer.writeMessage(4, f, google_protobuf_timestamp_pb.Timestamp.serializeBinaryToWriter);
    }, proto.flow.access.EventsResponse.Result.prototype.getBlockId = function() {
      return jspb.Message.getFieldWithDefault(this, 1, "");
    }, proto.flow.access.EventsResponse.Result.prototype.getBlockId_asB64 = function() {
      return jspb.Message.bytesAsB64(this.getBlockId());
    }, proto.flow.access.EventsResponse.Result.prototype.getBlockId_asU8 = function() {
      return jspb.Message.bytesAsU8(this.getBlockId());
    }, proto.flow.access.EventsResponse.Result.prototype.setBlockId = function(value) {
      return jspb.Message.setProto3BytesField(this, 1, value);
    }, proto.flow.access.EventsResponse.Result.prototype.getBlockHeight = function() {
      return jspb.Message.getFieldWithDefault(this, 2, 0);
    }, proto.flow.access.EventsResponse.Result.prototype.setBlockHeight = function(value) {
      return jspb.Message.setProto3IntField(this, 2, value);
    }, proto.flow.access.EventsResponse.Result.prototype.getEventsList = function() {
      return jspb.Message.getRepeatedWrapperField(this, flow_entities_event_pb.Event, 3);
    }, proto.flow.access.EventsResponse.Result.prototype.setEventsList = function(value) {
      return jspb.Message.setRepeatedWrapperField(this, 3, value);
    }, proto.flow.access.EventsResponse.Result.prototype.addEvents = function(opt_value, opt_index) {
      return jspb.Message.addToRepeatedWrapperField(this, 3, opt_value, proto.flow.entities.Event, opt_index);
    }, proto.flow.access.EventsResponse.Result.prototype.clearEventsList = function() {
      return this.setEventsList([]);
    }, proto.flow.access.EventsResponse.Result.prototype.getBlockTimestamp = function() {
      return jspb.Message.getWrapperField(this, google_protobuf_timestamp_pb.Timestamp, 4);
    }, proto.flow.access.EventsResponse.Result.prototype.setBlockTimestamp = function(value) {
      return jspb.Message.setWrapperField(this, 4, value);
    }, proto.flow.access.EventsResponse.Result.prototype.clearBlockTimestamp = function() {
      return this.setBlockTimestamp(void 0);
    }, proto.flow.access.EventsResponse.Result.prototype.hasBlockTimestamp = function() {
      return null != jspb.Message.getField(this, 4);
    }, proto.flow.access.EventsResponse.prototype.getResultsList = function() {
      return jspb.Message.getRepeatedWrapperField(this, proto.flow.access.EventsResponse.Result, 1);
    }, proto.flow.access.EventsResponse.prototype.setResultsList = function(value) {
      return jspb.Message.setRepeatedWrapperField(this, 1, value);
    }, proto.flow.access.EventsResponse.prototype.addResults = function(opt_value, opt_index) {
      return jspb.Message.addToRepeatedWrapperField(this, 1, opt_value, proto.flow.access.EventsResponse.Result, opt_index);
    }, proto.flow.access.EventsResponse.prototype.clearResultsList = function() {
      return this.setResultsList([]);
    }, proto.flow.access.EventsResponse.prototype.getMetadata = function() {
      return jspb.Message.getWrapperField(this, flow_entities_metadata_pb.Metadata, 2);
    }, proto.flow.access.EventsResponse.prototype.setMetadata = function(value) {
      return jspb.Message.setWrapperField(this, 2, value);
    }, proto.flow.access.EventsResponse.prototype.clearMetadata = function() {
      return this.setMetadata(void 0);
    }, proto.flow.access.EventsResponse.prototype.hasMetadata = function() {
      return null != jspb.Message.getField(this, 2);
    }, jspb.Message.GENERATE_TO_OBJECT && (proto.flow.access.GetNetworkParametersRequest.prototype.toObject = function(opt_includeInstance) {
      return proto.flow.access.GetNetworkParametersRequest.toObject(opt_includeInstance, this);
    }, proto.flow.access.GetNetworkParametersRequest.toObject = function(includeInstance, msg) {
      var obj = {};
      return includeInstance && (obj.$jspbMessageInstance = msg), obj;
    }), proto.flow.access.GetNetworkParametersRequest.deserializeBinary = function(bytes) {
      var reader = new jspb.BinaryReader(bytes), msg = new proto.flow.access.GetNetworkParametersRequest;
      return proto.flow.access.GetNetworkParametersRequest.deserializeBinaryFromReader(msg, reader);
    }, proto.flow.access.GetNetworkParametersRequest.deserializeBinaryFromReader = function(msg, reader) {
      for (;reader.nextField() && !reader.isEndGroup(); ) {
        reader.getFieldNumber();
        reader.skipField();
      }
      return msg;
    }, proto.flow.access.GetNetworkParametersRequest.prototype.serializeBinary = function() {
      var writer = new jspb.BinaryWriter;
      return proto.flow.access.GetNetworkParametersRequest.serializeBinaryToWriter(this, writer), 
      writer.getResultBuffer();
    }, proto.flow.access.GetNetworkParametersRequest.serializeBinaryToWriter = function(message, writer) {}, 
    jspb.Message.GENERATE_TO_OBJECT && (proto.flow.access.GetNetworkParametersResponse.prototype.toObject = function(opt_includeInstance) {
      return proto.flow.access.GetNetworkParametersResponse.toObject(opt_includeInstance, this);
    }, proto.flow.access.GetNetworkParametersResponse.toObject = function(includeInstance, msg) {
      var obj = {
        chainId: jspb.Message.getFieldWithDefault(msg, 1, "")
      };
      return includeInstance && (obj.$jspbMessageInstance = msg), obj;
    }), proto.flow.access.GetNetworkParametersResponse.deserializeBinary = function(bytes) {
      var reader = new jspb.BinaryReader(bytes), msg = new proto.flow.access.GetNetworkParametersResponse;
      return proto.flow.access.GetNetworkParametersResponse.deserializeBinaryFromReader(msg, reader);
    }, proto.flow.access.GetNetworkParametersResponse.deserializeBinaryFromReader = function(msg, reader) {
      for (var value; reader.nextField() && !reader.isEndGroup(); ) if (1 === reader.getFieldNumber()) value = reader.readString(), 
      msg.setChainId(value); else reader.skipField();
      return msg;
    }, proto.flow.access.GetNetworkParametersResponse.prototype.serializeBinary = function() {
      var writer = new jspb.BinaryWriter;
      return proto.flow.access.GetNetworkParametersResponse.serializeBinaryToWriter(this, writer), 
      writer.getResultBuffer();
    }, proto.flow.access.GetNetworkParametersResponse.serializeBinaryToWriter = function(message, writer) {
      var f;
      (f = message.getChainId()).length > 0 && writer.writeString(1, f);
    }, proto.flow.access.GetNetworkParametersResponse.prototype.getChainId = function() {
      return jspb.Message.getFieldWithDefault(this, 1, "");
    }, proto.flow.access.GetNetworkParametersResponse.prototype.setChainId = function(value) {
      return jspb.Message.setProto3StringField(this, 1, value);
    }, jspb.Message.GENERATE_TO_OBJECT && (proto.flow.access.GetLatestProtocolStateSnapshotRequest.prototype.toObject = function(opt_includeInstance) {
      return proto.flow.access.GetLatestProtocolStateSnapshotRequest.toObject(opt_includeInstance, this);
    }, proto.flow.access.GetLatestProtocolStateSnapshotRequest.toObject = function(includeInstance, msg) {
      var obj = {};
      return includeInstance && (obj.$jspbMessageInstance = msg), obj;
    }), proto.flow.access.GetLatestProtocolStateSnapshotRequest.deserializeBinary = function(bytes) {
      var reader = new jspb.BinaryReader(bytes), msg = new proto.flow.access.GetLatestProtocolStateSnapshotRequest;
      return proto.flow.access.GetLatestProtocolStateSnapshotRequest.deserializeBinaryFromReader(msg, reader);
    }, proto.flow.access.GetLatestProtocolStateSnapshotRequest.deserializeBinaryFromReader = function(msg, reader) {
      for (;reader.nextField() && !reader.isEndGroup(); ) {
        reader.getFieldNumber();
        reader.skipField();
      }
      return msg;
    }, proto.flow.access.GetLatestProtocolStateSnapshotRequest.prototype.serializeBinary = function() {
      var writer = new jspb.BinaryWriter;
      return proto.flow.access.GetLatestProtocolStateSnapshotRequest.serializeBinaryToWriter(this, writer), 
      writer.getResultBuffer();
    }, proto.flow.access.GetLatestProtocolStateSnapshotRequest.serializeBinaryToWriter = function(message, writer) {}, 
    jspb.Message.GENERATE_TO_OBJECT && (proto.flow.access.GetProtocolStateSnapshotByBlockIDRequest.prototype.toObject = function(opt_includeInstance) {
      return proto.flow.access.GetProtocolStateSnapshotByBlockIDRequest.toObject(opt_includeInstance, this);
    }, proto.flow.access.GetProtocolStateSnapshotByBlockIDRequest.toObject = function(includeInstance, msg) {
      var obj = {
        blockId: msg.getBlockId_asB64()
      };
      return includeInstance && (obj.$jspbMessageInstance = msg), obj;
    }), proto.flow.access.GetProtocolStateSnapshotByBlockIDRequest.deserializeBinary = function(bytes) {
      var reader = new jspb.BinaryReader(bytes), msg = new proto.flow.access.GetProtocolStateSnapshotByBlockIDRequest;
      return proto.flow.access.GetProtocolStateSnapshotByBlockIDRequest.deserializeBinaryFromReader(msg, reader);
    }, proto.flow.access.GetProtocolStateSnapshotByBlockIDRequest.deserializeBinaryFromReader = function(msg, reader) {
      for (var value; reader.nextField() && !reader.isEndGroup(); ) if (1 === reader.getFieldNumber()) value = reader.readBytes(), 
      msg.setBlockId(value); else reader.skipField();
      return msg;
    }, proto.flow.access.GetProtocolStateSnapshotByBlockIDRequest.prototype.serializeBinary = function() {
      var writer = new jspb.BinaryWriter;
      return proto.flow.access.GetProtocolStateSnapshotByBlockIDRequest.serializeBinaryToWriter(this, writer), 
      writer.getResultBuffer();
    }, proto.flow.access.GetProtocolStateSnapshotByBlockIDRequest.serializeBinaryToWriter = function(message, writer) {
      var f;
      (f = message.getBlockId_asU8()).length > 0 && writer.writeBytes(1, f);
    }, proto.flow.access.GetProtocolStateSnapshotByBlockIDRequest.prototype.getBlockId = function() {
      return jspb.Message.getFieldWithDefault(this, 1, "");
    }, proto.flow.access.GetProtocolStateSnapshotByBlockIDRequest.prototype.getBlockId_asB64 = function() {
      return jspb.Message.bytesAsB64(this.getBlockId());
    }, proto.flow.access.GetProtocolStateSnapshotByBlockIDRequest.prototype.getBlockId_asU8 = function() {
      return jspb.Message.bytesAsU8(this.getBlockId());
    }, proto.flow.access.GetProtocolStateSnapshotByBlockIDRequest.prototype.setBlockId = function(value) {
      return jspb.Message.setProto3BytesField(this, 1, value);
    }, jspb.Message.GENERATE_TO_OBJECT && (proto.flow.access.GetProtocolStateSnapshotByHeightRequest.prototype.toObject = function(opt_includeInstance) {
      return proto.flow.access.GetProtocolStateSnapshotByHeightRequest.toObject(opt_includeInstance, this);
    }, proto.flow.access.GetProtocolStateSnapshotByHeightRequest.toObject = function(includeInstance, msg) {
      var obj = {
        blockHeight: jspb.Message.getFieldWithDefault(msg, 1, 0)
      };
      return includeInstance && (obj.$jspbMessageInstance = msg), obj;
    }), proto.flow.access.GetProtocolStateSnapshotByHeightRequest.deserializeBinary = function(bytes) {
      var reader = new jspb.BinaryReader(bytes), msg = new proto.flow.access.GetProtocolStateSnapshotByHeightRequest;
      return proto.flow.access.GetProtocolStateSnapshotByHeightRequest.deserializeBinaryFromReader(msg, reader);
    }, proto.flow.access.GetProtocolStateSnapshotByHeightRequest.deserializeBinaryFromReader = function(msg, reader) {
      for (var value; reader.nextField() && !reader.isEndGroup(); ) if (1 === reader.getFieldNumber()) value = reader.readUint64(), 
      msg.setBlockHeight(value); else reader.skipField();
      return msg;
    }, proto.flow.access.GetProtocolStateSnapshotByHeightRequest.prototype.serializeBinary = function() {
      var writer = new jspb.BinaryWriter;
      return proto.flow.access.GetProtocolStateSnapshotByHeightRequest.serializeBinaryToWriter(this, writer), 
      writer.getResultBuffer();
    }, proto.flow.access.GetProtocolStateSnapshotByHeightRequest.serializeBinaryToWriter = function(message, writer) {
      var f;
      0 !== (f = message.getBlockHeight()) && writer.writeUint64(1, f);
    }, proto.flow.access.GetProtocolStateSnapshotByHeightRequest.prototype.getBlockHeight = function() {
      return jspb.Message.getFieldWithDefault(this, 1, 0);
    }, proto.flow.access.GetProtocolStateSnapshotByHeightRequest.prototype.setBlockHeight = function(value) {
      return jspb.Message.setProto3IntField(this, 1, value);
    }, jspb.Message.GENERATE_TO_OBJECT && (proto.flow.access.ProtocolStateSnapshotResponse.prototype.toObject = function(opt_includeInstance) {
      return proto.flow.access.ProtocolStateSnapshotResponse.toObject(opt_includeInstance, this);
    }, proto.flow.access.ProtocolStateSnapshotResponse.toObject = function(includeInstance, msg) {
      var f, obj = {
        serializedsnapshot: msg.getSerializedsnapshot_asB64(),
        metadata: (f = msg.getMetadata()) && flow_entities_metadata_pb.Metadata.toObject(includeInstance, f)
      };
      return includeInstance && (obj.$jspbMessageInstance = msg), obj;
    }), proto.flow.access.ProtocolStateSnapshotResponse.deserializeBinary = function(bytes) {
      var reader = new jspb.BinaryReader(bytes), msg = new proto.flow.access.ProtocolStateSnapshotResponse;
      return proto.flow.access.ProtocolStateSnapshotResponse.deserializeBinaryFromReader(msg, reader);
    }, proto.flow.access.ProtocolStateSnapshotResponse.deserializeBinaryFromReader = function(msg, reader) {
      for (var value; reader.nextField() && !reader.isEndGroup(); ) switch (reader.getFieldNumber()) {
       case 1:
        value = reader.readBytes(), msg.setSerializedsnapshot(value);
        break;
       case 2:
        value = new flow_entities_metadata_pb.Metadata, reader.readMessage(value, flow_entities_metadata_pb.Metadata.deserializeBinaryFromReader), 
        msg.setMetadata(value);
        break;
       default:
        reader.skipField();
      }
      return msg;
    }, proto.flow.access.ProtocolStateSnapshotResponse.prototype.serializeBinary = function() {
      var writer = new jspb.BinaryWriter;
      return proto.flow.access.ProtocolStateSnapshotResponse.serializeBinaryToWriter(this, writer), 
      writer.getResultBuffer();
    }, proto.flow.access.ProtocolStateSnapshotResponse.serializeBinaryToWriter = function(message, writer) {
      var f = void 0;
      (f = message.getSerializedsnapshot_asU8()).length > 0 && writer.writeBytes(1, f), 
      null != (f = message.getMetadata()) && writer.writeMessage(2, f, flow_entities_metadata_pb.Metadata.serializeBinaryToWriter);
    }, proto.flow.access.ProtocolStateSnapshotResponse.prototype.getSerializedsnapshot = function() {
      return jspb.Message.getFieldWithDefault(this, 1, "");
    }, proto.flow.access.ProtocolStateSnapshotResponse.prototype.getSerializedsnapshot_asB64 = function() {
      return jspb.Message.bytesAsB64(this.getSerializedsnapshot());
    }, proto.flow.access.ProtocolStateSnapshotResponse.prototype.getSerializedsnapshot_asU8 = function() {
      return jspb.Message.bytesAsU8(this.getSerializedsnapshot());
    }, proto.flow.access.ProtocolStateSnapshotResponse.prototype.setSerializedsnapshot = function(value) {
      return jspb.Message.setProto3BytesField(this, 1, value);
    }, proto.flow.access.ProtocolStateSnapshotResponse.prototype.getMetadata = function() {
      return jspb.Message.getWrapperField(this, flow_entities_metadata_pb.Metadata, 2);
    }, proto.flow.access.ProtocolStateSnapshotResponse.prototype.setMetadata = function(value) {
      return jspb.Message.setWrapperField(this, 2, value);
    }, proto.flow.access.ProtocolStateSnapshotResponse.prototype.clearMetadata = function() {
      return this.setMetadata(void 0);
    }, proto.flow.access.ProtocolStateSnapshotResponse.prototype.hasMetadata = function() {
      return null != jspb.Message.getField(this, 2);
    }, jspb.Message.GENERATE_TO_OBJECT && (proto.flow.access.GetExecutionResultForBlockIDRequest.prototype.toObject = function(opt_includeInstance) {
      return proto.flow.access.GetExecutionResultForBlockIDRequest.toObject(opt_includeInstance, this);
    }, proto.flow.access.GetExecutionResultForBlockIDRequest.toObject = function(includeInstance, msg) {
      var obj = {
        blockId: msg.getBlockId_asB64()
      };
      return includeInstance && (obj.$jspbMessageInstance = msg), obj;
    }), proto.flow.access.GetExecutionResultForBlockIDRequest.deserializeBinary = function(bytes) {
      var reader = new jspb.BinaryReader(bytes), msg = new proto.flow.access.GetExecutionResultForBlockIDRequest;
      return proto.flow.access.GetExecutionResultForBlockIDRequest.deserializeBinaryFromReader(msg, reader);
    }, proto.flow.access.GetExecutionResultForBlockIDRequest.deserializeBinaryFromReader = function(msg, reader) {
      for (var value; reader.nextField() && !reader.isEndGroup(); ) if (1 === reader.getFieldNumber()) value = reader.readBytes(), 
      msg.setBlockId(value); else reader.skipField();
      return msg;
    }, proto.flow.access.GetExecutionResultForBlockIDRequest.prototype.serializeBinary = function() {
      var writer = new jspb.BinaryWriter;
      return proto.flow.access.GetExecutionResultForBlockIDRequest.serializeBinaryToWriter(this, writer), 
      writer.getResultBuffer();
    }, proto.flow.access.GetExecutionResultForBlockIDRequest.serializeBinaryToWriter = function(message, writer) {
      var f;
      (f = message.getBlockId_asU8()).length > 0 && writer.writeBytes(1, f);
    }, proto.flow.access.GetExecutionResultForBlockIDRequest.prototype.getBlockId = function() {
      return jspb.Message.getFieldWithDefault(this, 1, "");
    }, proto.flow.access.GetExecutionResultForBlockIDRequest.prototype.getBlockId_asB64 = function() {
      return jspb.Message.bytesAsB64(this.getBlockId());
    }, proto.flow.access.GetExecutionResultForBlockIDRequest.prototype.getBlockId_asU8 = function() {
      return jspb.Message.bytesAsU8(this.getBlockId());
    }, proto.flow.access.GetExecutionResultForBlockIDRequest.prototype.setBlockId = function(value) {
      return jspb.Message.setProto3BytesField(this, 1, value);
    }, jspb.Message.GENERATE_TO_OBJECT && (proto.flow.access.ExecutionResultForBlockIDResponse.prototype.toObject = function(opt_includeInstance) {
      return proto.flow.access.ExecutionResultForBlockIDResponse.toObject(opt_includeInstance, this);
    }, proto.flow.access.ExecutionResultForBlockIDResponse.toObject = function(includeInstance, msg) {
      var f, obj = {
        executionResult: (f = msg.getExecutionResult()) && flow_entities_execution_result_pb.ExecutionResult.toObject(includeInstance, f),
        metadata: (f = msg.getMetadata()) && flow_entities_metadata_pb.Metadata.toObject(includeInstance, f)
      };
      return includeInstance && (obj.$jspbMessageInstance = msg), obj;
    });
    proto.flow.access.ExecutionResultForBlockIDResponse.deserializeBinary = function(bytes) {
      var reader = new jspb.BinaryReader(bytes), msg = new proto.flow.access.ExecutionResultForBlockIDResponse;
      return proto.flow.access.ExecutionResultForBlockIDResponse.deserializeBinaryFromReader(msg, reader);
    }, proto.flow.access.ExecutionResultForBlockIDResponse.deserializeBinaryFromReader = function(msg, reader) {
      for (var value; reader.nextField() && !reader.isEndGroup(); ) switch (reader.getFieldNumber()) {
       case 1:
        value = new flow_entities_execution_result_pb.ExecutionResult, reader.readMessage(value, flow_entities_execution_result_pb.ExecutionResult.deserializeBinaryFromReader), 
        msg.setExecutionResult(value);
        break;
       case 2:
        value = new flow_entities_metadata_pb.Metadata, reader.readMessage(value, flow_entities_metadata_pb.Metadata.deserializeBinaryFromReader), 
        msg.setMetadata(value);
        break;
       default:
        reader.skipField();
      }
      return msg;
    }, proto.flow.access.ExecutionResultForBlockIDResponse.prototype.serializeBinary = function() {
      var writer = new jspb.BinaryWriter;
      return proto.flow.access.ExecutionResultForBlockIDResponse.serializeBinaryToWriter(this, writer), 
      writer.getResultBuffer();
    }, proto.flow.access.ExecutionResultForBlockIDResponse.serializeBinaryToWriter = function(message, writer) {
      var f = void 0;
      null != (f = message.getExecutionResult()) && writer.writeMessage(1, f, flow_entities_execution_result_pb.ExecutionResult.serializeBinaryToWriter), 
      null != (f = message.getMetadata()) && writer.writeMessage(2, f, flow_entities_metadata_pb.Metadata.serializeBinaryToWriter);
    }, proto.flow.access.ExecutionResultForBlockIDResponse.prototype.getExecutionResult = function() {
      return jspb.Message.getWrapperField(this, flow_entities_execution_result_pb.ExecutionResult, 1);
    }, proto.flow.access.ExecutionResultForBlockIDResponse.prototype.setExecutionResult = function(value) {
      return jspb.Message.setWrapperField(this, 1, value);
    }, proto.flow.access.ExecutionResultForBlockIDResponse.prototype.clearExecutionResult = function() {
      return this.setExecutionResult(void 0);
    }, proto.flow.access.ExecutionResultForBlockIDResponse.prototype.hasExecutionResult = function() {
      return null != jspb.Message.getField(this, 1);
    }, proto.flow.access.ExecutionResultForBlockIDResponse.prototype.getMetadata = function() {
      return jspb.Message.getWrapperField(this, flow_entities_metadata_pb.Metadata, 2);
    }, proto.flow.access.ExecutionResultForBlockIDResponse.prototype.setMetadata = function(value) {
      return jspb.Message.setWrapperField(this, 2, value);
    }, proto.flow.access.ExecutionResultForBlockIDResponse.prototype.clearMetadata = function() {
      return this.setMetadata(void 0);
    }, proto.flow.access.ExecutionResultForBlockIDResponse.prototype.hasMetadata = function() {
      return null != jspb.Message.getField(this, 2);
    }, jspb.Message.GENERATE_TO_OBJECT && (proto.flow.access.GetExecutionResultByIDRequest.prototype.toObject = function(opt_includeInstance) {
      return proto.flow.access.GetExecutionResultByIDRequest.toObject(opt_includeInstance, this);
    }, proto.flow.access.GetExecutionResultByIDRequest.toObject = function(includeInstance, msg) {
      var obj = {
        id: msg.getId_asB64()
      };
      return includeInstance && (obj.$jspbMessageInstance = msg), obj;
    }), proto.flow.access.GetExecutionResultByIDRequest.deserializeBinary = function(bytes) {
      var reader = new jspb.BinaryReader(bytes), msg = new proto.flow.access.GetExecutionResultByIDRequest;
      return proto.flow.access.GetExecutionResultByIDRequest.deserializeBinaryFromReader(msg, reader);
    }, proto.flow.access.GetExecutionResultByIDRequest.deserializeBinaryFromReader = function(msg, reader) {
      for (var value; reader.nextField() && !reader.isEndGroup(); ) if (1 === reader.getFieldNumber()) value = reader.readBytes(), 
      msg.setId(value); else reader.skipField();
      return msg;
    }, proto.flow.access.GetExecutionResultByIDRequest.prototype.serializeBinary = function() {
      var writer = new jspb.BinaryWriter;
      return proto.flow.access.GetExecutionResultByIDRequest.serializeBinaryToWriter(this, writer), 
      writer.getResultBuffer();
    }, proto.flow.access.GetExecutionResultByIDRequest.serializeBinaryToWriter = function(message, writer) {
      var f;
      (f = message.getId_asU8()).length > 0 && writer.writeBytes(1, f);
    }, proto.flow.access.GetExecutionResultByIDRequest.prototype.getId = function() {
      return jspb.Message.getFieldWithDefault(this, 1, "");
    }, proto.flow.access.GetExecutionResultByIDRequest.prototype.getId_asB64 = function() {
      return jspb.Message.bytesAsB64(this.getId());
    }, proto.flow.access.GetExecutionResultByIDRequest.prototype.getId_asU8 = function() {
      return jspb.Message.bytesAsU8(this.getId());
    }, proto.flow.access.GetExecutionResultByIDRequest.prototype.setId = function(value) {
      return jspb.Message.setProto3BytesField(this, 1, value);
    }, jspb.Message.GENERATE_TO_OBJECT && (proto.flow.access.ExecutionResultByIDResponse.prototype.toObject = function(opt_includeInstance) {
      return proto.flow.access.ExecutionResultByIDResponse.toObject(opt_includeInstance, this);
    }, proto.flow.access.ExecutionResultByIDResponse.toObject = function(includeInstance, msg) {
      var f, obj = {
        executionResult: (f = msg.getExecutionResult()) && flow_entities_execution_result_pb.ExecutionResult.toObject(includeInstance, f),
        metadata: (f = msg.getMetadata()) && flow_entities_metadata_pb.Metadata.toObject(includeInstance, f)
      };
      return includeInstance && (obj.$jspbMessageInstance = msg), obj;
    }), proto.flow.access.ExecutionResultByIDResponse.deserializeBinary = function(bytes) {
      var reader = new jspb.BinaryReader(bytes), msg = new proto.flow.access.ExecutionResultByIDResponse;
      return proto.flow.access.ExecutionResultByIDResponse.deserializeBinaryFromReader(msg, reader);
    }, proto.flow.access.ExecutionResultByIDResponse.deserializeBinaryFromReader = function(msg, reader) {
      for (var value; reader.nextField() && !reader.isEndGroup(); ) switch (reader.getFieldNumber()) {
       case 1:
        value = new flow_entities_execution_result_pb.ExecutionResult, reader.readMessage(value, flow_entities_execution_result_pb.ExecutionResult.deserializeBinaryFromReader), 
        msg.setExecutionResult(value);
        break;
       case 2:
        value = new flow_entities_metadata_pb.Metadata, reader.readMessage(value, flow_entities_metadata_pb.Metadata.deserializeBinaryFromReader), 
        msg.setMetadata(value);
        break;
       default:
        reader.skipField();
      }
      return msg;
    }, proto.flow.access.ExecutionResultByIDResponse.prototype.serializeBinary = function() {
      var writer = new jspb.BinaryWriter;
      return proto.flow.access.ExecutionResultByIDResponse.serializeBinaryToWriter(this, writer), 
      writer.getResultBuffer();
    }, proto.flow.access.ExecutionResultByIDResponse.serializeBinaryToWriter = function(message, writer) {
      var f = void 0;
      null != (f = message.getExecutionResult()) && writer.writeMessage(1, f, flow_entities_execution_result_pb.ExecutionResult.serializeBinaryToWriter), 
      null != (f = message.getMetadata()) && writer.writeMessage(2, f, flow_entities_metadata_pb.Metadata.serializeBinaryToWriter);
    }, proto.flow.access.ExecutionResultByIDResponse.prototype.getExecutionResult = function() {
      return jspb.Message.getWrapperField(this, flow_entities_execution_result_pb.ExecutionResult, 1);
    }, proto.flow.access.ExecutionResultByIDResponse.prototype.setExecutionResult = function(value) {
      return jspb.Message.setWrapperField(this, 1, value);
    }, proto.flow.access.ExecutionResultByIDResponse.prototype.clearExecutionResult = function() {
      return this.setExecutionResult(void 0);
    }, proto.flow.access.ExecutionResultByIDResponse.prototype.hasExecutionResult = function() {
      return null != jspb.Message.getField(this, 1);
    }, proto.flow.access.ExecutionResultByIDResponse.prototype.getMetadata = function() {
      return jspb.Message.getWrapperField(this, flow_entities_metadata_pb.Metadata, 2);
    }, proto.flow.access.ExecutionResultByIDResponse.prototype.setMetadata = function(value) {
      return jspb.Message.setWrapperField(this, 2, value);
    }, proto.flow.access.ExecutionResultByIDResponse.prototype.clearMetadata = function() {
      return this.setMetadata(void 0);
    }, proto.flow.access.ExecutionResultByIDResponse.prototype.hasMetadata = function() {
      return null != jspb.Message.getField(this, 2);
    }, jspb.Message.GENERATE_TO_OBJECT && (proto.flow.access.SubscribeBlocksFromStartBlockIDRequest.prototype.toObject = function(opt_includeInstance) {
      return proto.flow.access.SubscribeBlocksFromStartBlockIDRequest.toObject(opt_includeInstance, this);
    }, proto.flow.access.SubscribeBlocksFromStartBlockIDRequest.toObject = function(includeInstance, msg) {
      var obj = {
        startBlockId: msg.getStartBlockId_asB64(),
        blockStatus: jspb.Message.getFieldWithDefault(msg, 2, 0),
        fullBlockResponse: jspb.Message.getBooleanFieldWithDefault(msg, 3, !1)
      };
      return includeInstance && (obj.$jspbMessageInstance = msg), obj;
    }), proto.flow.access.SubscribeBlocksFromStartBlockIDRequest.deserializeBinary = function(bytes) {
      var reader = new jspb.BinaryReader(bytes), msg = new proto.flow.access.SubscribeBlocksFromStartBlockIDRequest;
      return proto.flow.access.SubscribeBlocksFromStartBlockIDRequest.deserializeBinaryFromReader(msg, reader);
    }, proto.flow.access.SubscribeBlocksFromStartBlockIDRequest.deserializeBinaryFromReader = function(msg, reader) {
      for (var value; reader.nextField() && !reader.isEndGroup(); ) switch (reader.getFieldNumber()) {
       case 1:
        value = reader.readBytes(), msg.setStartBlockId(value);
        break;
       case 2:
        value = reader.readEnum(), msg.setBlockStatus(value);
        break;
       case 3:
        value = reader.readBool(), msg.setFullBlockResponse(value);
        break;
       default:
        reader.skipField();
      }
      return msg;
    }, proto.flow.access.SubscribeBlocksFromStartBlockIDRequest.prototype.serializeBinary = function() {
      var writer = new jspb.BinaryWriter;
      return proto.flow.access.SubscribeBlocksFromStartBlockIDRequest.serializeBinaryToWriter(this, writer), 
      writer.getResultBuffer();
    }, proto.flow.access.SubscribeBlocksFromStartBlockIDRequest.serializeBinaryToWriter = function(message, writer) {
      var f = void 0;
      (f = message.getStartBlockId_asU8()).length > 0 && writer.writeBytes(1, f), 0 !== (f = message.getBlockStatus()) && writer.writeEnum(2, f), 
      (f = message.getFullBlockResponse()) && writer.writeBool(3, f);
    }, proto.flow.access.SubscribeBlocksFromStartBlockIDRequest.prototype.getStartBlockId = function() {
      return jspb.Message.getFieldWithDefault(this, 1, "");
    }, proto.flow.access.SubscribeBlocksFromStartBlockIDRequest.prototype.getStartBlockId_asB64 = function() {
      return jspb.Message.bytesAsB64(this.getStartBlockId());
    }, proto.flow.access.SubscribeBlocksFromStartBlockIDRequest.prototype.getStartBlockId_asU8 = function() {
      return jspb.Message.bytesAsU8(this.getStartBlockId());
    }, proto.flow.access.SubscribeBlocksFromStartBlockIDRequest.prototype.setStartBlockId = function(value) {
      return jspb.Message.setProto3BytesField(this, 1, value);
    }, proto.flow.access.SubscribeBlocksFromStartBlockIDRequest.prototype.getBlockStatus = function() {
      return jspb.Message.getFieldWithDefault(this, 2, 0);
    }, proto.flow.access.SubscribeBlocksFromStartBlockIDRequest.prototype.setBlockStatus = function(value) {
      return jspb.Message.setProto3EnumField(this, 2, value);
    }, proto.flow.access.SubscribeBlocksFromStartBlockIDRequest.prototype.getFullBlockResponse = function() {
      return jspb.Message.getBooleanFieldWithDefault(this, 3, !1);
    }, proto.flow.access.SubscribeBlocksFromStartBlockIDRequest.prototype.setFullBlockResponse = function(value) {
      return jspb.Message.setProto3BooleanField(this, 3, value);
    }, jspb.Message.GENERATE_TO_OBJECT && (proto.flow.access.SubscribeBlocksFromStartHeightRequest.prototype.toObject = function(opt_includeInstance) {
      return proto.flow.access.SubscribeBlocksFromStartHeightRequest.toObject(opt_includeInstance, this);
    }, proto.flow.access.SubscribeBlocksFromStartHeightRequest.toObject = function(includeInstance, msg) {
      var obj = {
        startBlockHeight: jspb.Message.getFieldWithDefault(msg, 1, 0),
        blockStatus: jspb.Message.getFieldWithDefault(msg, 2, 0),
        fullBlockResponse: jspb.Message.getBooleanFieldWithDefault(msg, 3, !1)
      };
      return includeInstance && (obj.$jspbMessageInstance = msg), obj;
    }), proto.flow.access.SubscribeBlocksFromStartHeightRequest.deserializeBinary = function(bytes) {
      var reader = new jspb.BinaryReader(bytes), msg = new proto.flow.access.SubscribeBlocksFromStartHeightRequest;
      return proto.flow.access.SubscribeBlocksFromStartHeightRequest.deserializeBinaryFromReader(msg, reader);
    }, proto.flow.access.SubscribeBlocksFromStartHeightRequest.deserializeBinaryFromReader = function(msg, reader) {
      for (var value; reader.nextField() && !reader.isEndGroup(); ) switch (reader.getFieldNumber()) {
       case 1:
        value = reader.readUint64(), msg.setStartBlockHeight(value);
        break;
       case 2:
        value = reader.readEnum(), msg.setBlockStatus(value);
        break;
       case 3:
        value = reader.readBool(), msg.setFullBlockResponse(value);
        break;
       default:
        reader.skipField();
      }
      return msg;
    }, proto.flow.access.SubscribeBlocksFromStartHeightRequest.prototype.serializeBinary = function() {
      var writer = new jspb.BinaryWriter;
      return proto.flow.access.SubscribeBlocksFromStartHeightRequest.serializeBinaryToWriter(this, writer), 
      writer.getResultBuffer();
    }, proto.flow.access.SubscribeBlocksFromStartHeightRequest.serializeBinaryToWriter = function(message, writer) {
      var f = void 0;
      0 !== (f = message.getStartBlockHeight()) && writer.writeUint64(1, f), 0 !== (f = message.getBlockStatus()) && writer.writeEnum(2, f), 
      (f = message.getFullBlockResponse()) && writer.writeBool(3, f);
    }, proto.flow.access.SubscribeBlocksFromStartHeightRequest.prototype.getStartBlockHeight = function() {
      return jspb.Message.getFieldWithDefault(this, 1, 0);
    }, proto.flow.access.SubscribeBlocksFromStartHeightRequest.prototype.setStartBlockHeight = function(value) {
      return jspb.Message.setProto3IntField(this, 1, value);
    }, proto.flow.access.SubscribeBlocksFromStartHeightRequest.prototype.getBlockStatus = function() {
      return jspb.Message.getFieldWithDefault(this, 2, 0);
    }, proto.flow.access.SubscribeBlocksFromStartHeightRequest.prototype.setBlockStatus = function(value) {
      return jspb.Message.setProto3EnumField(this, 2, value);
    }, proto.flow.access.SubscribeBlocksFromStartHeightRequest.prototype.getFullBlockResponse = function() {
      return jspb.Message.getBooleanFieldWithDefault(this, 3, !1);
    }, proto.flow.access.SubscribeBlocksFromStartHeightRequest.prototype.setFullBlockResponse = function(value) {
      return jspb.Message.setProto3BooleanField(this, 3, value);
    }, jspb.Message.GENERATE_TO_OBJECT && (proto.flow.access.SubscribeBlocksFromLatestRequest.prototype.toObject = function(opt_includeInstance) {
      return proto.flow.access.SubscribeBlocksFromLatestRequest.toObject(opt_includeInstance, this);
    }, proto.flow.access.SubscribeBlocksFromLatestRequest.toObject = function(includeInstance, msg) {
      var obj = {
        blockStatus: jspb.Message.getFieldWithDefault(msg, 1, 0),
        fullBlockResponse: jspb.Message.getBooleanFieldWithDefault(msg, 2, !1)
      };
      return includeInstance && (obj.$jspbMessageInstance = msg), obj;
    }), proto.flow.access.SubscribeBlocksFromLatestRequest.deserializeBinary = function(bytes) {
      var reader = new jspb.BinaryReader(bytes), msg = new proto.flow.access.SubscribeBlocksFromLatestRequest;
      return proto.flow.access.SubscribeBlocksFromLatestRequest.deserializeBinaryFromReader(msg, reader);
    }, proto.flow.access.SubscribeBlocksFromLatestRequest.deserializeBinaryFromReader = function(msg, reader) {
      for (var value; reader.nextField() && !reader.isEndGroup(); ) switch (reader.getFieldNumber()) {
       case 1:
        value = reader.readEnum(), msg.setBlockStatus(value);
        break;
       case 2:
        value = reader.readBool(), msg.setFullBlockResponse(value);
        break;
       default:
        reader.skipField();
      }
      return msg;
    }, proto.flow.access.SubscribeBlocksFromLatestRequest.prototype.serializeBinary = function() {
      var writer = new jspb.BinaryWriter;
      return proto.flow.access.SubscribeBlocksFromLatestRequest.serializeBinaryToWriter(this, writer), 
      writer.getResultBuffer();
    }, proto.flow.access.SubscribeBlocksFromLatestRequest.serializeBinaryToWriter = function(message, writer) {
      var f = void 0;
      0 !== (f = message.getBlockStatus()) && writer.writeEnum(1, f), (f = message.getFullBlockResponse()) && writer.writeBool(2, f);
    }, proto.flow.access.SubscribeBlocksFromLatestRequest.prototype.getBlockStatus = function() {
      return jspb.Message.getFieldWithDefault(this, 1, 0);
    }, proto.flow.access.SubscribeBlocksFromLatestRequest.prototype.setBlockStatus = function(value) {
      return jspb.Message.setProto3EnumField(this, 1, value);
    }, proto.flow.access.SubscribeBlocksFromLatestRequest.prototype.getFullBlockResponse = function() {
      return jspb.Message.getBooleanFieldWithDefault(this, 2, !1);
    }, proto.flow.access.SubscribeBlocksFromLatestRequest.prototype.setFullBlockResponse = function(value) {
      return jspb.Message.setProto3BooleanField(this, 2, value);
    }, jspb.Message.GENERATE_TO_OBJECT && (proto.flow.access.SubscribeBlocksResponse.prototype.toObject = function(opt_includeInstance) {
      return proto.flow.access.SubscribeBlocksResponse.toObject(opt_includeInstance, this);
    }, proto.flow.access.SubscribeBlocksResponse.toObject = function(includeInstance, msg) {
      var f, obj = {
        block: (f = msg.getBlock()) && flow_entities_block_pb.Block.toObject(includeInstance, f)
      };
      return includeInstance && (obj.$jspbMessageInstance = msg), obj;
    }), proto.flow.access.SubscribeBlocksResponse.deserializeBinary = function(bytes) {
      var reader = new jspb.BinaryReader(bytes), msg = new proto.flow.access.SubscribeBlocksResponse;
      return proto.flow.access.SubscribeBlocksResponse.deserializeBinaryFromReader(msg, reader);
    }, proto.flow.access.SubscribeBlocksResponse.deserializeBinaryFromReader = function(msg, reader) {
      for (var value; reader.nextField() && !reader.isEndGroup(); ) if (1 === reader.getFieldNumber()) value = new flow_entities_block_pb.Block, 
      reader.readMessage(value, flow_entities_block_pb.Block.deserializeBinaryFromReader), 
      msg.setBlock(value); else reader.skipField();
      return msg;
    }, proto.flow.access.SubscribeBlocksResponse.prototype.serializeBinary = function() {
      var writer = new jspb.BinaryWriter;
      return proto.flow.access.SubscribeBlocksResponse.serializeBinaryToWriter(this, writer), 
      writer.getResultBuffer();
    }, proto.flow.access.SubscribeBlocksResponse.serializeBinaryToWriter = function(message, writer) {
      var f;
      null != (f = message.getBlock()) && writer.writeMessage(1, f, flow_entities_block_pb.Block.serializeBinaryToWriter);
    }, proto.flow.access.SubscribeBlocksResponse.prototype.getBlock = function() {
      return jspb.Message.getWrapperField(this, flow_entities_block_pb.Block, 1);
    }, proto.flow.access.SubscribeBlocksResponse.prototype.setBlock = function(value) {
      return jspb.Message.setWrapperField(this, 1, value);
    }, proto.flow.access.SubscribeBlocksResponse.prototype.clearBlock = function() {
      return this.setBlock(void 0);
    }, proto.flow.access.SubscribeBlocksResponse.prototype.hasBlock = function() {
      return null != jspb.Message.getField(this, 1);
    }, jspb.Message.GENERATE_TO_OBJECT && (proto.flow.access.SubscribeBlockHeadersFromStartBlockIDRequest.prototype.toObject = function(opt_includeInstance) {
      return proto.flow.access.SubscribeBlockHeadersFromStartBlockIDRequest.toObject(opt_includeInstance, this);
    }, proto.flow.access.SubscribeBlockHeadersFromStartBlockIDRequest.toObject = function(includeInstance, msg) {
      var obj = {
        startBlockId: msg.getStartBlockId_asB64(),
        blockStatus: jspb.Message.getFieldWithDefault(msg, 2, 0)
      };
      return includeInstance && (obj.$jspbMessageInstance = msg), obj;
    }), proto.flow.access.SubscribeBlockHeadersFromStartBlockIDRequest.deserializeBinary = function(bytes) {
      var reader = new jspb.BinaryReader(bytes), msg = new proto.flow.access.SubscribeBlockHeadersFromStartBlockIDRequest;
      return proto.flow.access.SubscribeBlockHeadersFromStartBlockIDRequest.deserializeBinaryFromReader(msg, reader);
    }, proto.flow.access.SubscribeBlockHeadersFromStartBlockIDRequest.deserializeBinaryFromReader = function(msg, reader) {
      for (var value; reader.nextField() && !reader.isEndGroup(); ) switch (reader.getFieldNumber()) {
       case 1:
        value = reader.readBytes(), msg.setStartBlockId(value);
        break;
       case 2:
        value = reader.readEnum(), msg.setBlockStatus(value);
        break;
       default:
        reader.skipField();
      }
      return msg;
    }, proto.flow.access.SubscribeBlockHeadersFromStartBlockIDRequest.prototype.serializeBinary = function() {
      var writer = new jspb.BinaryWriter;
      return proto.flow.access.SubscribeBlockHeadersFromStartBlockIDRequest.serializeBinaryToWriter(this, writer), 
      writer.getResultBuffer();
    }, proto.flow.access.SubscribeBlockHeadersFromStartBlockIDRequest.serializeBinaryToWriter = function(message, writer) {
      var f = void 0;
      (f = message.getStartBlockId_asU8()).length > 0 && writer.writeBytes(1, f), 0 !== (f = message.getBlockStatus()) && writer.writeEnum(2, f);
    }, proto.flow.access.SubscribeBlockHeadersFromStartBlockIDRequest.prototype.getStartBlockId = function() {
      return jspb.Message.getFieldWithDefault(this, 1, "");
    }, proto.flow.access.SubscribeBlockHeadersFromStartBlockIDRequest.prototype.getStartBlockId_asB64 = function() {
      return jspb.Message.bytesAsB64(this.getStartBlockId());
    }, proto.flow.access.SubscribeBlockHeadersFromStartBlockIDRequest.prototype.getStartBlockId_asU8 = function() {
      return jspb.Message.bytesAsU8(this.getStartBlockId());
    }, proto.flow.access.SubscribeBlockHeadersFromStartBlockIDRequest.prototype.setStartBlockId = function(value) {
      return jspb.Message.setProto3BytesField(this, 1, value);
    }, proto.flow.access.SubscribeBlockHeadersFromStartBlockIDRequest.prototype.getBlockStatus = function() {
      return jspb.Message.getFieldWithDefault(this, 2, 0);
    }, proto.flow.access.SubscribeBlockHeadersFromStartBlockIDRequest.prototype.setBlockStatus = function(value) {
      return jspb.Message.setProto3EnumField(this, 2, value);
    }, jspb.Message.GENERATE_TO_OBJECT && (proto.flow.access.SubscribeBlockHeadersFromStartHeightRequest.prototype.toObject = function(opt_includeInstance) {
      return proto.flow.access.SubscribeBlockHeadersFromStartHeightRequest.toObject(opt_includeInstance, this);
    }, proto.flow.access.SubscribeBlockHeadersFromStartHeightRequest.toObject = function(includeInstance, msg) {
      var obj = {
        startBlockHeight: jspb.Message.getFieldWithDefault(msg, 1, 0),
        blockStatus: jspb.Message.getFieldWithDefault(msg, 2, 0)
      };
      return includeInstance && (obj.$jspbMessageInstance = msg), obj;
    }), proto.flow.access.SubscribeBlockHeadersFromStartHeightRequest.deserializeBinary = function(bytes) {
      var reader = new jspb.BinaryReader(bytes), msg = new proto.flow.access.SubscribeBlockHeadersFromStartHeightRequest;
      return proto.flow.access.SubscribeBlockHeadersFromStartHeightRequest.deserializeBinaryFromReader(msg, reader);
    }, proto.flow.access.SubscribeBlockHeadersFromStartHeightRequest.deserializeBinaryFromReader = function(msg, reader) {
      for (var value; reader.nextField() && !reader.isEndGroup(); ) switch (reader.getFieldNumber()) {
       case 1:
        value = reader.readUint64(), msg.setStartBlockHeight(value);
        break;
       case 2:
        value = reader.readEnum(), msg.setBlockStatus(value);
        break;
       default:
        reader.skipField();
      }
      return msg;
    }, proto.flow.access.SubscribeBlockHeadersFromStartHeightRequest.prototype.serializeBinary = function() {
      var writer = new jspb.BinaryWriter;
      return proto.flow.access.SubscribeBlockHeadersFromStartHeightRequest.serializeBinaryToWriter(this, writer), 
      writer.getResultBuffer();
    }, proto.flow.access.SubscribeBlockHeadersFromStartHeightRequest.serializeBinaryToWriter = function(message, writer) {
      var f = void 0;
      0 !== (f = message.getStartBlockHeight()) && writer.writeUint64(1, f), 0 !== (f = message.getBlockStatus()) && writer.writeEnum(2, f);
    }, proto.flow.access.SubscribeBlockHeadersFromStartHeightRequest.prototype.getStartBlockHeight = function() {
      return jspb.Message.getFieldWithDefault(this, 1, 0);
    }, proto.flow.access.SubscribeBlockHeadersFromStartHeightRequest.prototype.setStartBlockHeight = function(value) {
      return jspb.Message.setProto3IntField(this, 1, value);
    }, proto.flow.access.SubscribeBlockHeadersFromStartHeightRequest.prototype.getBlockStatus = function() {
      return jspb.Message.getFieldWithDefault(this, 2, 0);
    }, proto.flow.access.SubscribeBlockHeadersFromStartHeightRequest.prototype.setBlockStatus = function(value) {
      return jspb.Message.setProto3EnumField(this, 2, value);
    }, jspb.Message.GENERATE_TO_OBJECT && (proto.flow.access.SubscribeBlockHeadersFromLatestRequest.prototype.toObject = function(opt_includeInstance) {
      return proto.flow.access.SubscribeBlockHeadersFromLatestRequest.toObject(opt_includeInstance, this);
    }, proto.flow.access.SubscribeBlockHeadersFromLatestRequest.toObject = function(includeInstance, msg) {
      var obj = {
        blockStatus: jspb.Message.getFieldWithDefault(msg, 1, 0)
      };
      return includeInstance && (obj.$jspbMessageInstance = msg), obj;
    }), proto.flow.access.SubscribeBlockHeadersFromLatestRequest.deserializeBinary = function(bytes) {
      var reader = new jspb.BinaryReader(bytes), msg = new proto.flow.access.SubscribeBlockHeadersFromLatestRequest;
      return proto.flow.access.SubscribeBlockHeadersFromLatestRequest.deserializeBinaryFromReader(msg, reader);
    }, proto.flow.access.SubscribeBlockHeadersFromLatestRequest.deserializeBinaryFromReader = function(msg, reader) {
      for (var value; reader.nextField() && !reader.isEndGroup(); ) if (1 === reader.getFieldNumber()) value = reader.readEnum(), 
      msg.setBlockStatus(value); else reader.skipField();
      return msg;
    }, proto.flow.access.SubscribeBlockHeadersFromLatestRequest.prototype.serializeBinary = function() {
      var writer = new jspb.BinaryWriter;
      return proto.flow.access.SubscribeBlockHeadersFromLatestRequest.serializeBinaryToWriter(this, writer), 
      writer.getResultBuffer();
    }, proto.flow.access.SubscribeBlockHeadersFromLatestRequest.serializeBinaryToWriter = function(message, writer) {
      var f;
      0 !== (f = message.getBlockStatus()) && writer.writeEnum(1, f);
    }, proto.flow.access.SubscribeBlockHeadersFromLatestRequest.prototype.getBlockStatus = function() {
      return jspb.Message.getFieldWithDefault(this, 1, 0);
    }, proto.flow.access.SubscribeBlockHeadersFromLatestRequest.prototype.setBlockStatus = function(value) {
      return jspb.Message.setProto3EnumField(this, 1, value);
    }, jspb.Message.GENERATE_TO_OBJECT && (proto.flow.access.SubscribeBlockHeadersResponse.prototype.toObject = function(opt_includeInstance) {
      return proto.flow.access.SubscribeBlockHeadersResponse.toObject(opt_includeInstance, this);
    }, proto.flow.access.SubscribeBlockHeadersResponse.toObject = function(includeInstance, msg) {
      var f, obj = {
        header: (f = msg.getHeader()) && flow_entities_block_header_pb.BlockHeader.toObject(includeInstance, f)
      };
      return includeInstance && (obj.$jspbMessageInstance = msg), obj;
    }), proto.flow.access.SubscribeBlockHeadersResponse.deserializeBinary = function(bytes) {
      var reader = new jspb.BinaryReader(bytes), msg = new proto.flow.access.SubscribeBlockHeadersResponse;
      return proto.flow.access.SubscribeBlockHeadersResponse.deserializeBinaryFromReader(msg, reader);
    }, proto.flow.access.SubscribeBlockHeadersResponse.deserializeBinaryFromReader = function(msg, reader) {
      for (var value; reader.nextField() && !reader.isEndGroup(); ) if (1 === reader.getFieldNumber()) value = new flow_entities_block_header_pb.BlockHeader, 
      reader.readMessage(value, flow_entities_block_header_pb.BlockHeader.deserializeBinaryFromReader), 
      msg.setHeader(value); else reader.skipField();
      return msg;
    }, proto.flow.access.SubscribeBlockHeadersResponse.prototype.serializeBinary = function() {
      var writer = new jspb.BinaryWriter;
      return proto.flow.access.SubscribeBlockHeadersResponse.serializeBinaryToWriter(this, writer), 
      writer.getResultBuffer();
    }, proto.flow.access.SubscribeBlockHeadersResponse.serializeBinaryToWriter = function(message, writer) {
      var f;
      null != (f = message.getHeader()) && writer.writeMessage(1, f, flow_entities_block_header_pb.BlockHeader.serializeBinaryToWriter);
    }, proto.flow.access.SubscribeBlockHeadersResponse.prototype.getHeader = function() {
      return jspb.Message.getWrapperField(this, flow_entities_block_header_pb.BlockHeader, 1);
    }, proto.flow.access.SubscribeBlockHeadersResponse.prototype.setHeader = function(value) {
      return jspb.Message.setWrapperField(this, 1, value);
    }, proto.flow.access.SubscribeBlockHeadersResponse.prototype.clearHeader = function() {
      return this.setHeader(void 0);
    }, proto.flow.access.SubscribeBlockHeadersResponse.prototype.hasHeader = function() {
      return null != jspb.Message.getField(this, 1);
    }, jspb.Message.GENERATE_TO_OBJECT && (proto.flow.access.SubscribeBlockDigestsFromStartBlockIDRequest.prototype.toObject = function(opt_includeInstance) {
      return proto.flow.access.SubscribeBlockDigestsFromStartBlockIDRequest.toObject(opt_includeInstance, this);
    }, proto.flow.access.SubscribeBlockDigestsFromStartBlockIDRequest.toObject = function(includeInstance, msg) {
      var obj = {
        startBlockId: msg.getStartBlockId_asB64(),
        blockStatus: jspb.Message.getFieldWithDefault(msg, 2, 0)
      };
      return includeInstance && (obj.$jspbMessageInstance = msg), obj;
    }), proto.flow.access.SubscribeBlockDigestsFromStartBlockIDRequest.deserializeBinary = function(bytes) {
      var reader = new jspb.BinaryReader(bytes), msg = new proto.flow.access.SubscribeBlockDigestsFromStartBlockIDRequest;
      return proto.flow.access.SubscribeBlockDigestsFromStartBlockIDRequest.deserializeBinaryFromReader(msg, reader);
    }, proto.flow.access.SubscribeBlockDigestsFromStartBlockIDRequest.deserializeBinaryFromReader = function(msg, reader) {
      for (var value; reader.nextField() && !reader.isEndGroup(); ) switch (reader.getFieldNumber()) {
       case 1:
        value = reader.readBytes(), msg.setStartBlockId(value);
        break;
       case 2:
        value = reader.readEnum(), msg.setBlockStatus(value);
        break;
       default:
        reader.skipField();
      }
      return msg;
    }, proto.flow.access.SubscribeBlockDigestsFromStartBlockIDRequest.prototype.serializeBinary = function() {
      var writer = new jspb.BinaryWriter;
      return proto.flow.access.SubscribeBlockDigestsFromStartBlockIDRequest.serializeBinaryToWriter(this, writer), 
      writer.getResultBuffer();
    }, proto.flow.access.SubscribeBlockDigestsFromStartBlockIDRequest.serializeBinaryToWriter = function(message, writer) {
      var f = void 0;
      (f = message.getStartBlockId_asU8()).length > 0 && writer.writeBytes(1, f), 0 !== (f = message.getBlockStatus()) && writer.writeEnum(2, f);
    }, proto.flow.access.SubscribeBlockDigestsFromStartBlockIDRequest.prototype.getStartBlockId = function() {
      return jspb.Message.getFieldWithDefault(this, 1, "");
    }, proto.flow.access.SubscribeBlockDigestsFromStartBlockIDRequest.prototype.getStartBlockId_asB64 = function() {
      return jspb.Message.bytesAsB64(this.getStartBlockId());
    }, proto.flow.access.SubscribeBlockDigestsFromStartBlockIDRequest.prototype.getStartBlockId_asU8 = function() {
      return jspb.Message.bytesAsU8(this.getStartBlockId());
    }, proto.flow.access.SubscribeBlockDigestsFromStartBlockIDRequest.prototype.setStartBlockId = function(value) {
      return jspb.Message.setProto3BytesField(this, 1, value);
    }, proto.flow.access.SubscribeBlockDigestsFromStartBlockIDRequest.prototype.getBlockStatus = function() {
      return jspb.Message.getFieldWithDefault(this, 2, 0);
    }, proto.flow.access.SubscribeBlockDigestsFromStartBlockIDRequest.prototype.setBlockStatus = function(value) {
      return jspb.Message.setProto3EnumField(this, 2, value);
    }, jspb.Message.GENERATE_TO_OBJECT && (proto.flow.access.SubscribeBlockDigestsFromStartHeightRequest.prototype.toObject = function(opt_includeInstance) {
      return proto.flow.access.SubscribeBlockDigestsFromStartHeightRequest.toObject(opt_includeInstance, this);
    }, proto.flow.access.SubscribeBlockDigestsFromStartHeightRequest.toObject = function(includeInstance, msg) {
      var obj = {
        startBlockHeight: jspb.Message.getFieldWithDefault(msg, 1, 0),
        blockStatus: jspb.Message.getFieldWithDefault(msg, 2, 0)
      };
      return includeInstance && (obj.$jspbMessageInstance = msg), obj;
    }), proto.flow.access.SubscribeBlockDigestsFromStartHeightRequest.deserializeBinary = function(bytes) {
      var reader = new jspb.BinaryReader(bytes), msg = new proto.flow.access.SubscribeBlockDigestsFromStartHeightRequest;
      return proto.flow.access.SubscribeBlockDigestsFromStartHeightRequest.deserializeBinaryFromReader(msg, reader);
    }, proto.flow.access.SubscribeBlockDigestsFromStartHeightRequest.deserializeBinaryFromReader = function(msg, reader) {
      for (var value; reader.nextField() && !reader.isEndGroup(); ) switch (reader.getFieldNumber()) {
       case 1:
        value = reader.readUint64(), msg.setStartBlockHeight(value);
        break;
       case 2:
        value = reader.readEnum(), msg.setBlockStatus(value);
        break;
       default:
        reader.skipField();
      }
      return msg;
    }, proto.flow.access.SubscribeBlockDigestsFromStartHeightRequest.prototype.serializeBinary = function() {
      var writer = new jspb.BinaryWriter;
      return proto.flow.access.SubscribeBlockDigestsFromStartHeightRequest.serializeBinaryToWriter(this, writer), 
      writer.getResultBuffer();
    }, proto.flow.access.SubscribeBlockDigestsFromStartHeightRequest.serializeBinaryToWriter = function(message, writer) {
      var f = void 0;
      0 !== (f = message.getStartBlockHeight()) && writer.writeUint64(1, f), 0 !== (f = message.getBlockStatus()) && writer.writeEnum(2, f);
    }, proto.flow.access.SubscribeBlockDigestsFromStartHeightRequest.prototype.getStartBlockHeight = function() {
      return jspb.Message.getFieldWithDefault(this, 1, 0);
    }, proto.flow.access.SubscribeBlockDigestsFromStartHeightRequest.prototype.setStartBlockHeight = function(value) {
      return jspb.Message.setProto3IntField(this, 1, value);
    }, proto.flow.access.SubscribeBlockDigestsFromStartHeightRequest.prototype.getBlockStatus = function() {
      return jspb.Message.getFieldWithDefault(this, 2, 0);
    }, proto.flow.access.SubscribeBlockDigestsFromStartHeightRequest.prototype.setBlockStatus = function(value) {
      return jspb.Message.setProto3EnumField(this, 2, value);
    }, jspb.Message.GENERATE_TO_OBJECT && (proto.flow.access.SubscribeBlockDigestsFromLatestRequest.prototype.toObject = function(opt_includeInstance) {
      return proto.flow.access.SubscribeBlockDigestsFromLatestRequest.toObject(opt_includeInstance, this);
    }, proto.flow.access.SubscribeBlockDigestsFromLatestRequest.toObject = function(includeInstance, msg) {
      var obj = {
        blockStatus: jspb.Message.getFieldWithDefault(msg, 1, 0)
      };
      return includeInstance && (obj.$jspbMessageInstance = msg), obj;
    }), proto.flow.access.SubscribeBlockDigestsFromLatestRequest.deserializeBinary = function(bytes) {
      var reader = new jspb.BinaryReader(bytes), msg = new proto.flow.access.SubscribeBlockDigestsFromLatestRequest;
      return proto.flow.access.SubscribeBlockDigestsFromLatestRequest.deserializeBinaryFromReader(msg, reader);
    }, proto.flow.access.SubscribeBlockDigestsFromLatestRequest.deserializeBinaryFromReader = function(msg, reader) {
      for (var value; reader.nextField() && !reader.isEndGroup(); ) if (1 === reader.getFieldNumber()) value = reader.readEnum(), 
      msg.setBlockStatus(value); else reader.skipField();
      return msg;
    }, proto.flow.access.SubscribeBlockDigestsFromLatestRequest.prototype.serializeBinary = function() {
      var writer = new jspb.BinaryWriter;
      return proto.flow.access.SubscribeBlockDigestsFromLatestRequest.serializeBinaryToWriter(this, writer), 
      writer.getResultBuffer();
    }, proto.flow.access.SubscribeBlockDigestsFromLatestRequest.serializeBinaryToWriter = function(message, writer) {
      var f;
      0 !== (f = message.getBlockStatus()) && writer.writeEnum(1, f);
    }, proto.flow.access.SubscribeBlockDigestsFromLatestRequest.prototype.getBlockStatus = function() {
      return jspb.Message.getFieldWithDefault(this, 1, 0);
    }, proto.flow.access.SubscribeBlockDigestsFromLatestRequest.prototype.setBlockStatus = function(value) {
      return jspb.Message.setProto3EnumField(this, 1, value);
    }, jspb.Message.GENERATE_TO_OBJECT && (proto.flow.access.SubscribeBlockDigestsResponse.prototype.toObject = function(opt_includeInstance) {
      return proto.flow.access.SubscribeBlockDigestsResponse.toObject(opt_includeInstance, this);
    }, proto.flow.access.SubscribeBlockDigestsResponse.toObject = function(includeInstance, msg) {
      var f, obj = {
        blockId: msg.getBlockId_asB64(),
        blockHeight: jspb.Message.getFieldWithDefault(msg, 2, 0),
        blockTimestamp: (f = msg.getBlockTimestamp()) && google_protobuf_timestamp_pb.Timestamp.toObject(includeInstance, f)
      };
      return includeInstance && (obj.$jspbMessageInstance = msg), obj;
    }), proto.flow.access.SubscribeBlockDigestsResponse.deserializeBinary = function(bytes) {
      var reader = new jspb.BinaryReader(bytes), msg = new proto.flow.access.SubscribeBlockDigestsResponse;
      return proto.flow.access.SubscribeBlockDigestsResponse.deserializeBinaryFromReader(msg, reader);
    }, proto.flow.access.SubscribeBlockDigestsResponse.deserializeBinaryFromReader = function(msg, reader) {
      for (var value; reader.nextField() && !reader.isEndGroup(); ) switch (reader.getFieldNumber()) {
       case 1:
        value = reader.readBytes(), msg.setBlockId(value);
        break;
       case 2:
        value = reader.readUint64(), msg.setBlockHeight(value);
        break;
       case 3:
        value = new google_protobuf_timestamp_pb.Timestamp, reader.readMessage(value, google_protobuf_timestamp_pb.Timestamp.deserializeBinaryFromReader), 
        msg.setBlockTimestamp(value);
        break;
       default:
        reader.skipField();
      }
      return msg;
    }, proto.flow.access.SubscribeBlockDigestsResponse.prototype.serializeBinary = function() {
      var writer = new jspb.BinaryWriter;
      return proto.flow.access.SubscribeBlockDigestsResponse.serializeBinaryToWriter(this, writer), 
      writer.getResultBuffer();
    }, proto.flow.access.SubscribeBlockDigestsResponse.serializeBinaryToWriter = function(message, writer) {
      var f = void 0;
      (f = message.getBlockId_asU8()).length > 0 && writer.writeBytes(1, f), 0 !== (f = message.getBlockHeight()) && writer.writeUint64(2, f), 
      null != (f = message.getBlockTimestamp()) && writer.writeMessage(3, f, google_protobuf_timestamp_pb.Timestamp.serializeBinaryToWriter);
    }, proto.flow.access.SubscribeBlockDigestsResponse.prototype.getBlockId = function() {
      return jspb.Message.getFieldWithDefault(this, 1, "");
    }, proto.flow.access.SubscribeBlockDigestsResponse.prototype.getBlockId_asB64 = function() {
      return jspb.Message.bytesAsB64(this.getBlockId());
    }, proto.flow.access.SubscribeBlockDigestsResponse.prototype.getBlockId_asU8 = function() {
      return jspb.Message.bytesAsU8(this.getBlockId());
    }, proto.flow.access.SubscribeBlockDigestsResponse.prototype.setBlockId = function(value) {
      return jspb.Message.setProto3BytesField(this, 1, value);
    }, proto.flow.access.SubscribeBlockDigestsResponse.prototype.getBlockHeight = function() {
      return jspb.Message.getFieldWithDefault(this, 2, 0);
    }, proto.flow.access.SubscribeBlockDigestsResponse.prototype.setBlockHeight = function(value) {
      return jspb.Message.setProto3IntField(this, 2, value);
    }, proto.flow.access.SubscribeBlockDigestsResponse.prototype.getBlockTimestamp = function() {
      return jspb.Message.getWrapperField(this, google_protobuf_timestamp_pb.Timestamp, 3);
    }, proto.flow.access.SubscribeBlockDigestsResponse.prototype.setBlockTimestamp = function(value) {
      return jspb.Message.setWrapperField(this, 3, value);
    }, proto.flow.access.SubscribeBlockDigestsResponse.prototype.clearBlockTimestamp = function() {
      return this.setBlockTimestamp(void 0);
    }, proto.flow.access.SubscribeBlockDigestsResponse.prototype.hasBlockTimestamp = function() {
      return null != jspb.Message.getField(this, 3);
    }, jspb.Message.GENERATE_TO_OBJECT && (proto.flow.access.SendAndSubscribeTransactionStatusesRequest.prototype.toObject = function(opt_includeInstance) {
      return proto.flow.access.SendAndSubscribeTransactionStatusesRequest.toObject(opt_includeInstance, this);
    }, proto.flow.access.SendAndSubscribeTransactionStatusesRequest.toObject = function(includeInstance, msg) {
      var f, obj = {
        transaction: (f = msg.getTransaction()) && flow_entities_transaction_pb.Transaction.toObject(includeInstance, f)
      };
      return includeInstance && (obj.$jspbMessageInstance = msg), obj;
    }), proto.flow.access.SendAndSubscribeTransactionStatusesRequest.deserializeBinary = function(bytes) {
      var reader = new jspb.BinaryReader(bytes), msg = new proto.flow.access.SendAndSubscribeTransactionStatusesRequest;
      return proto.flow.access.SendAndSubscribeTransactionStatusesRequest.deserializeBinaryFromReader(msg, reader);
    }, proto.flow.access.SendAndSubscribeTransactionStatusesRequest.deserializeBinaryFromReader = function(msg, reader) {
      for (var value; reader.nextField() && !reader.isEndGroup(); ) if (1 === reader.getFieldNumber()) value = new flow_entities_transaction_pb.Transaction, 
      reader.readMessage(value, flow_entities_transaction_pb.Transaction.deserializeBinaryFromReader), 
      msg.setTransaction(value); else reader.skipField();
      return msg;
    }, proto.flow.access.SendAndSubscribeTransactionStatusesRequest.prototype.serializeBinary = function() {
      var writer = new jspb.BinaryWriter;
      return proto.flow.access.SendAndSubscribeTransactionStatusesRequest.serializeBinaryToWriter(this, writer), 
      writer.getResultBuffer();
    }, proto.flow.access.SendAndSubscribeTransactionStatusesRequest.serializeBinaryToWriter = function(message, writer) {
      var f;
      null != (f = message.getTransaction()) && writer.writeMessage(1, f, flow_entities_transaction_pb.Transaction.serializeBinaryToWriter);
    }, proto.flow.access.SendAndSubscribeTransactionStatusesRequest.prototype.getTransaction = function() {
      return jspb.Message.getWrapperField(this, flow_entities_transaction_pb.Transaction, 1);
    }, proto.flow.access.SendAndSubscribeTransactionStatusesRequest.prototype.setTransaction = function(value) {
      return jspb.Message.setWrapperField(this, 1, value);
    }, proto.flow.access.SendAndSubscribeTransactionStatusesRequest.prototype.clearTransaction = function() {
      return this.setTransaction(void 0);
    }, proto.flow.access.SendAndSubscribeTransactionStatusesRequest.prototype.hasTransaction = function() {
      return null != jspb.Message.getField(this, 1);
    }, jspb.Message.GENERATE_TO_OBJECT && (proto.flow.access.SendAndSubscribeTransactionStatusesResponse.prototype.toObject = function(opt_includeInstance) {
      return proto.flow.access.SendAndSubscribeTransactionStatusesResponse.toObject(opt_includeInstance, this);
    }, proto.flow.access.SendAndSubscribeTransactionStatusesResponse.toObject = function(includeInstance, msg) {
      var obj = {
        id: msg.getId_asB64(),
        status: jspb.Message.getFieldWithDefault(msg, 2, 0),
        messageIndex: jspb.Message.getFieldWithDefault(msg, 3, 0)
      };
      return includeInstance && (obj.$jspbMessageInstance = msg), obj;
    }), proto.flow.access.SendAndSubscribeTransactionStatusesResponse.deserializeBinary = function(bytes) {
      var reader = new jspb.BinaryReader(bytes), msg = new proto.flow.access.SendAndSubscribeTransactionStatusesResponse;
      return proto.flow.access.SendAndSubscribeTransactionStatusesResponse.deserializeBinaryFromReader(msg, reader);
    }, proto.flow.access.SendAndSubscribeTransactionStatusesResponse.deserializeBinaryFromReader = function(msg, reader) {
      for (var value; reader.nextField() && !reader.isEndGroup(); ) switch (reader.getFieldNumber()) {
       case 1:
        value = reader.readBytes(), msg.setId(value);
        break;
       case 2:
        value = reader.readEnum(), msg.setStatus(value);
        break;
       case 3:
        value = reader.readUint64(), msg.setMessageIndex(value);
        break;
       default:
        reader.skipField();
      }
      return msg;
    }, proto.flow.access.SendAndSubscribeTransactionStatusesResponse.prototype.serializeBinary = function() {
      var writer = new jspb.BinaryWriter;
      return proto.flow.access.SendAndSubscribeTransactionStatusesResponse.serializeBinaryToWriter(this, writer), 
      writer.getResultBuffer();
    }, proto.flow.access.SendAndSubscribeTransactionStatusesResponse.serializeBinaryToWriter = function(message, writer) {
      var f = void 0;
      (f = message.getId_asU8()).length > 0 && writer.writeBytes(1, f), 0 !== (f = message.getStatus()) && writer.writeEnum(2, f), 
      0 !== (f = message.getMessageIndex()) && writer.writeUint64(3, f);
    }, proto.flow.access.SendAndSubscribeTransactionStatusesResponse.prototype.getId = function() {
      return jspb.Message.getFieldWithDefault(this, 1, "");
    }, proto.flow.access.SendAndSubscribeTransactionStatusesResponse.prototype.getId_asB64 = function() {
      return jspb.Message.bytesAsB64(this.getId());
    }, proto.flow.access.SendAndSubscribeTransactionStatusesResponse.prototype.getId_asU8 = function() {
      return jspb.Message.bytesAsU8(this.getId());
    }, proto.flow.access.SendAndSubscribeTransactionStatusesResponse.prototype.setId = function(value) {
      return jspb.Message.setProto3BytesField(this, 1, value);
    }, proto.flow.access.SendAndSubscribeTransactionStatusesResponse.prototype.getStatus = function() {
      return jspb.Message.getFieldWithDefault(this, 2, 0);
    }, proto.flow.access.SendAndSubscribeTransactionStatusesResponse.prototype.setStatus = function(value) {
      return jspb.Message.setProto3EnumField(this, 2, value);
    }, proto.flow.access.SendAndSubscribeTransactionStatusesResponse.prototype.getMessageIndex = function() {
      return jspb.Message.getFieldWithDefault(this, 3, 0);
    }, proto.flow.access.SendAndSubscribeTransactionStatusesResponse.prototype.setMessageIndex = function(value) {
      return jspb.Message.setProto3IntField(this, 3, value);
    }, goog.object.extend(exports, proto.flow.access);
  }(access_pb)), access_pb;
}
transaction_pb = {}, require$$0 = getDefaultExportFromNamespaceIfPresent(googleProtobuf), 
event_pb = {}, transaction_pbExports = requireTransaction_pb(), access_pb = {}, 
account_pb = {}, block_header_pb = {}, require$$10 = getDefaultExportFromNamespaceIfPresent(timestamp_pb), 
block_pb = {}, collection_pb = {}, block_seal_pb = {}, execution_result_pb = {}, 
metadata_pb = {}, node_version_info_pb = {}, access_pbExports = requireAccess_pb();
const Transaction = transaction_pbExports.Transaction, SendTransactionRequest = access_pbExports.SendTransactionRequest;
export { SendTransactionRequest, Transaction, build, config, decode, getAccount, getBlock, getTransactionStatus, limit, resolve, sansPrefix, send, withPrefix$1 as withPrefix };
//# sourceMappingURL=bundle.js.map
