{"type": "module", "scripts": {"build:esm": "rm -rf ./es && tsc -p ./tsconfig.json && ts-add-js-extension add --dir=es", "build:treeshaking": "rm -rf ./bundle.js && rollup -c"}, "devDependencies": {"rollup": "4.41.1", "@rollup/plugin-json": "6.1.0", "@rollup/plugin-swc": "0.4.0", "@rollup/plugin-terser": "0.4.4", "rollup-plugin-cleanup": "3.2.1", "@rollup/plugin-replace": "6.0.2", "rollup-plugin-visualizer": "6.0.1", "@rollup/plugin-commonjs": "28.0.3", "rollup-plugin-node-externals": "8.0.0", "@rollup/plugin-node-resolve": "16.0.1", "rollup-plugin-tslib-resolve-id": "0.0.0", "@improbable-eng/grpc-web": "^0.12.0", "elliptic": "^6.5.4", "google-protobuf": "3.11.4", "ts-add-js-extension": "1.6.6"}}