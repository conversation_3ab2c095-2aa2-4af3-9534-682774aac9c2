#!/usr/bin/env node

import fs from 'fs';
import path from 'path';
import { fileURLToPath } from 'url';

const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

// 递归遍历目录
function walkDir(dir, callback) {
  const files = fs.readdirSync(dir);

  files.forEach(file => {
    const filePath = path.join(dir, file);
    const stat = fs.statSync(filePath);

    if (stat.isDirectory()) {
      walkDir(filePath, callback);
    } else if (stat.isFile() && file.endsWith('.js')) {
      callback(filePath);
    }
  });
}

// 转换 CommonJS 到 ESM
function convertToESM(filePath) {
  let content = fs.readFileSync(filePath, 'utf8');

  // 检查是否是 protobuf 生成的文件
  if (!content.includes('goog.object.extend(exports,') && !content.includes('require(')) {
    return; // 跳过已经是 ESM 格式的文件
  }

  console.log(`Converting ${filePath} to ESM...`);

  // 替换 "use strict" 为空（ESM 默认是严格模式）
  content = content.replace(/"use strict";\s*\n/g, '');

  // 替换 require 语句为 import 语句
  content = content.replace(
    /var\s+(\w+)\s+=\s+require\(['"]([^'"]+)['"]\);/g,
    'import $1 from "$2";'
  );

  // 处理 google-protobuf 的特殊导入
  content = content.replace(
    /var jspb = require\(['"]google-protobuf['"]\);/g,
    'import jspb from "google-protobuf";'
  );

  // 处理 goog 变量赋值
  content = content.replace(/var goog = jspb;\s*\n/g, 'const goog = jspb;\n');

  // 处理全局变量定义 - 更精确的匹配
  content = content.replace(
    /var global = \(typeof globalThis[^}]+\}\);/g,
    'const global = globalThis || window || self || {};'
  );

  // 收集所有导出的符号
  const exportMatches = content.match(/goog\.exportSymbol\(['"]([^'"]+)['"][^)]*\)/g) || [];
  const exportedSymbols = exportMatches.map(match => {
    const symbolMatch = match.match(/goog\.exportSymbol\(['"]([^'"]+)['"]/);
    return symbolMatch ? symbolMatch[1] : null;
  }).filter(Boolean);

  // 移除 goog.exportSymbol 调用
  content = content.replace(/goog\.exportSymbol\([^)]+\);\s*\n/g, '');

  // 移除 goog.inherits 调用
  content = content.replace(/goog\.inherits\([^)]+\);\s*\n/g, '');

  // 移除 goog.object.extend 调用
  content = content.replace(/goog\.object\.extend\([^)]+\);\s*\n/g, '');

  // 在文件末尾添加 ESM 导出
  if (exportedSymbols.length > 0) {
    const exports = exportedSymbols.map(symbol => {
      const parts = symbol.split('.');
      const exportName = parts[parts.length - 1];
      return `export const ${exportName} = ${symbol};`;
    }).join('\n');

    content += '\n' + exports + '\n';
  }

  // 写回文件
  fs.writeFileSync(filePath, content, 'utf8');
}

// 主函数
function main() {
  const esDir = path.join(__dirname, 'es');

  if (!fs.existsSync(esDir)) {
    console.error('es directory not found. Please run TypeScript compilation first.');
    process.exit(1);
  }

  console.log('Converting CommonJS protobuf files to ESM...');
  walkDir(esDir, convertToESM);
  console.log('Conversion completed!');
}

main();
