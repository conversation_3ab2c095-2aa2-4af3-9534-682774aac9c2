// source: flow/execution/execution.proto
/**
 * @fileoverview
 * @enhanceable
 * @suppress {missingRequire} reports error on implicit type usages.
 * @suppress {messageConventions} JS Compiler reports an error if a variable or
 *     field starts with 'MSG_' and isn't a translatable message.
 * @public
 */
// GENERATED CODE -- DO NOT EDIT!
/* eslint-disable */
// @ts-nocheck
import jspb from "google-protobuf";
const goog = jspb;
var global = (typeof globalThis !== 'undefined' && globalThis) ||
    (typeof window !== 'undefined' && window) ||
    (typeof global !== 'undefined' && global) ||
    (typeof self !== 'undefined' && self) ||
    (function () { return this; }).call(null) ||
    Function('return this')();
import flow_entities_account_pb from "../../flow/entities/account_pb.js";
import flow_entities_block_header_pb from "../../flow/entities/block_header_pb.js";
import flow_entities_event_pb from "../../flow/entities/event_pb.js";
import flow_entities_transaction_pb from "../../flow/entities/transaction_pb.js";
/**
 * Generated by JsPbCodeGenerator.
 * @param {Array=} opt_data Optional initial data array, typically from a
 * server response, or constructed directly in Javascript. The array is used
 * in place and becomes part of the constructed object. It is not cloned.
 * If no data is provided, the constructed object will be empty, but still
 * valid.
 * @extends {jspb.Message}
 * @constructor
 */
proto.flow.execution.PingRequest = function (opt_data) {
    jspb.Message.initialize(this, opt_data, 0, -1, null, null);
};
if (goog.DEBUG && !COMPILED) {
    /**
     * @public
     * @override
     */
    proto.flow.execution.PingRequest.displayName = 'proto.flow.execution.PingRequest';
}
/**
 * Generated by JsPbCodeGenerator.
 * @param {Array=} opt_data Optional initial data array, typically from a
 * server response, or constructed directly in Javascript. The array is used
 * in place and becomes part of the constructed object. It is not cloned.
 * If no data is provided, the constructed object will be empty, but still
 * valid.
 * @extends {jspb.Message}
 * @constructor
 */
proto.flow.execution.PingResponse = function (opt_data) {
    jspb.Message.initialize(this, opt_data, 0, -1, null, null);
};
if (goog.DEBUG && !COMPILED) {
    /**
     * @public
     * @override
     */
    proto.flow.execution.PingResponse.displayName = 'proto.flow.execution.PingResponse';
}
/**
 * Generated by JsPbCodeGenerator.
 * @param {Array=} opt_data Optional initial data array, typically from a
 * server response, or constructed directly in Javascript. The array is used
 * in place and becomes part of the constructed object. It is not cloned.
 * If no data is provided, the constructed object will be empty, but still
 * valid.
 * @extends {jspb.Message}
 * @constructor
 */
proto.flow.execution.GetAccountAtBlockIDRequest = function (opt_data) {
    jspb.Message.initialize(this, opt_data, 0, -1, null, null);
};
if (goog.DEBUG && !COMPILED) {
    /**
     * @public
     * @override
     */
    proto.flow.execution.GetAccountAtBlockIDRequest.displayName = 'proto.flow.execution.GetAccountAtBlockIDRequest';
}
/**
 * Generated by JsPbCodeGenerator.
 * @param {Array=} opt_data Optional initial data array, typically from a
 * server response, or constructed directly in Javascript. The array is used
 * in place and becomes part of the constructed object. It is not cloned.
 * If no data is provided, the constructed object will be empty, but still
 * valid.
 * @extends {jspb.Message}
 * @constructor
 */
proto.flow.execution.GetAccountAtBlockIDResponse = function (opt_data) {
    jspb.Message.initialize(this, opt_data, 0, -1, null, null);
};
if (goog.DEBUG && !COMPILED) {
    /**
     * @public
     * @override
     */
    proto.flow.execution.GetAccountAtBlockIDResponse.displayName = 'proto.flow.execution.GetAccountAtBlockIDResponse';
}
/**
 * Generated by JsPbCodeGenerator.
 * @param {Array=} opt_data Optional initial data array, typically from a
 * server response, or constructed directly in Javascript. The array is used
 * in place and becomes part of the constructed object. It is not cloned.
 * If no data is provided, the constructed object will be empty, but still
 * valid.
 * @extends {jspb.Message}
 * @constructor
 */
proto.flow.execution.ExecuteScriptAtBlockIDRequest = function (opt_data) {
    jspb.Message.initialize(this, opt_data, 0, -1, proto.flow.execution.ExecuteScriptAtBlockIDRequest.repeatedFields_, null);
};
if (goog.DEBUG && !COMPILED) {
    /**
     * @public
     * @override
     */
    proto.flow.execution.ExecuteScriptAtBlockIDRequest.displayName = 'proto.flow.execution.ExecuteScriptAtBlockIDRequest';
}
/**
 * Generated by JsPbCodeGenerator.
 * @param {Array=} opt_data Optional initial data array, typically from a
 * server response, or constructed directly in Javascript. The array is used
 * in place and becomes part of the constructed object. It is not cloned.
 * If no data is provided, the constructed object will be empty, but still
 * valid.
 * @extends {jspb.Message}
 * @constructor
 */
proto.flow.execution.ExecuteScriptAtBlockIDResponse = function (opt_data) {
    jspb.Message.initialize(this, opt_data, 0, -1, null, null);
};
if (goog.DEBUG && !COMPILED) {
    /**
     * @public
     * @override
     */
    proto.flow.execution.ExecuteScriptAtBlockIDResponse.displayName = 'proto.flow.execution.ExecuteScriptAtBlockIDResponse';
}
/**
 * Generated by JsPbCodeGenerator.
 * @param {Array=} opt_data Optional initial data array, typically from a
 * server response, or constructed directly in Javascript. The array is used
 * in place and becomes part of the constructed object. It is not cloned.
 * If no data is provided, the constructed object will be empty, but still
 * valid.
 * @extends {jspb.Message}
 * @constructor
 */
proto.flow.execution.GetEventsForBlockIDsResponse = function (opt_data) {
    jspb.Message.initialize(this, opt_data, 0, -1, proto.flow.execution.GetEventsForBlockIDsResponse.repeatedFields_, null);
};
if (goog.DEBUG && !COMPILED) {
    /**
     * @public
     * @override
     */
    proto.flow.execution.GetEventsForBlockIDsResponse.displayName = 'proto.flow.execution.GetEventsForBlockIDsResponse';
}
/**
 * Generated by JsPbCodeGenerator.
 * @param {Array=} opt_data Optional initial data array, typically from a
 * server response, or constructed directly in Javascript. The array is used
 * in place and becomes part of the constructed object. It is not cloned.
 * If no data is provided, the constructed object will be empty, but still
 * valid.
 * @extends {jspb.Message}
 * @constructor
 */
proto.flow.execution.GetEventsForBlockIDsResponse.Result = function (opt_data) {
    jspb.Message.initialize(this, opt_data, 0, -1, proto.flow.execution.GetEventsForBlockIDsResponse.Result.repeatedFields_, null);
};
if (goog.DEBUG && !COMPILED) {
    /**
     * @public
     * @override
     */
    proto.flow.execution.GetEventsForBlockIDsResponse.Result.displayName = 'proto.flow.execution.GetEventsForBlockIDsResponse.Result';
}
/**
 * Generated by JsPbCodeGenerator.
 * @param {Array=} opt_data Optional initial data array, typically from a
 * server response, or constructed directly in Javascript. The array is used
 * in place and becomes part of the constructed object. It is not cloned.
 * If no data is provided, the constructed object will be empty, but still
 * valid.
 * @extends {jspb.Message}
 * @constructor
 */
proto.flow.execution.GetEventsForBlockIDsRequest = function (opt_data) {
    jspb.Message.initialize(this, opt_data, 0, -1, proto.flow.execution.GetEventsForBlockIDsRequest.repeatedFields_, null);
};
if (goog.DEBUG && !COMPILED) {
    /**
     * @public
     * @override
     */
    proto.flow.execution.GetEventsForBlockIDsRequest.displayName = 'proto.flow.execution.GetEventsForBlockIDsRequest';
}
/**
 * Generated by JsPbCodeGenerator.
 * @param {Array=} opt_data Optional initial data array, typically from a
 * server response, or constructed directly in Javascript. The array is used
 * in place and becomes part of the constructed object. It is not cloned.
 * If no data is provided, the constructed object will be empty, but still
 * valid.
 * @extends {jspb.Message}
 * @constructor
 */
proto.flow.execution.GetTransactionResultRequest = function (opt_data) {
    jspb.Message.initialize(this, opt_data, 0, -1, null, null);
};
if (goog.DEBUG && !COMPILED) {
    /**
     * @public
     * @override
     */
    proto.flow.execution.GetTransactionResultRequest.displayName = 'proto.flow.execution.GetTransactionResultRequest';
}
/**
 * Generated by JsPbCodeGenerator.
 * @param {Array=} opt_data Optional initial data array, typically from a
 * server response, or constructed directly in Javascript. The array is used
 * in place and becomes part of the constructed object. It is not cloned.
 * If no data is provided, the constructed object will be empty, but still
 * valid.
 * @extends {jspb.Message}
 * @constructor
 */
proto.flow.execution.GetTransactionByIndexRequest = function (opt_data) {
    jspb.Message.initialize(this, opt_data, 0, -1, null, null);
};
if (goog.DEBUG && !COMPILED) {
    /**
     * @public
     * @override
     */
    proto.flow.execution.GetTransactionByIndexRequest.displayName = 'proto.flow.execution.GetTransactionByIndexRequest';
}
/**
 * Generated by JsPbCodeGenerator.
 * @param {Array=} opt_data Optional initial data array, typically from a
 * server response, or constructed directly in Javascript. The array is used
 * in place and becomes part of the constructed object. It is not cloned.
 * If no data is provided, the constructed object will be empty, but still
 * valid.
 * @extends {jspb.Message}
 * @constructor
 */
proto.flow.execution.GetTransactionResultResponse = function (opt_data) {
    jspb.Message.initialize(this, opt_data, 0, -1, proto.flow.execution.GetTransactionResultResponse.repeatedFields_, null);
};
if (goog.DEBUG && !COMPILED) {
    /**
     * @public
     * @override
     */
    proto.flow.execution.GetTransactionResultResponse.displayName = 'proto.flow.execution.GetTransactionResultResponse';
}
/**
 * Generated by JsPbCodeGenerator.
 * @param {Array=} opt_data Optional initial data array, typically from a
 * server response, or constructed directly in Javascript. The array is used
 * in place and becomes part of the constructed object. It is not cloned.
 * If no data is provided, the constructed object will be empty, but still
 * valid.
 * @extends {jspb.Message}
 * @constructor
 */
proto.flow.execution.GetTransactionsByBlockIDRequest = function (opt_data) {
    jspb.Message.initialize(this, opt_data, 0, -1, null, null);
};
if (goog.DEBUG && !COMPILED) {
    /**
     * @public
     * @override
     */
    proto.flow.execution.GetTransactionsByBlockIDRequest.displayName = 'proto.flow.execution.GetTransactionsByBlockIDRequest';
}
/**
 * Generated by JsPbCodeGenerator.
 * @param {Array=} opt_data Optional initial data array, typically from a
 * server response, or constructed directly in Javascript. The array is used
 * in place and becomes part of the constructed object. It is not cloned.
 * If no data is provided, the constructed object will be empty, but still
 * valid.
 * @extends {jspb.Message}
 * @constructor
 */
proto.flow.execution.GetTransactionResultsResponse = function (opt_data) {
    jspb.Message.initialize(this, opt_data, 0, -1, proto.flow.execution.GetTransactionResultsResponse.repeatedFields_, null);
};
if (goog.DEBUG && !COMPILED) {
    /**
     * @public
     * @override
     */
    proto.flow.execution.GetTransactionResultsResponse.displayName = 'proto.flow.execution.GetTransactionResultsResponse';
}
/**
 * Generated by JsPbCodeGenerator.
 * @param {Array=} opt_data Optional initial data array, typically from a
 * server response, or constructed directly in Javascript. The array is used
 * in place and becomes part of the constructed object. It is not cloned.
 * If no data is provided, the constructed object will be empty, but still
 * valid.
 * @extends {jspb.Message}
 * @constructor
 */
proto.flow.execution.GetTransactionErrorMessageRequest = function (opt_data) {
    jspb.Message.initialize(this, opt_data, 0, -1, null, null);
};
if (goog.DEBUG && !COMPILED) {
    /**
     * @public
     * @override
     */
    proto.flow.execution.GetTransactionErrorMessageRequest.displayName = 'proto.flow.execution.GetTransactionErrorMessageRequest';
}
/**
 * Generated by JsPbCodeGenerator.
 * @param {Array=} opt_data Optional initial data array, typically from a
 * server response, or constructed directly in Javascript. The array is used
 * in place and becomes part of the constructed object. It is not cloned.
 * If no data is provided, the constructed object will be empty, but still
 * valid.
 * @extends {jspb.Message}
 * @constructor
 */
proto.flow.execution.GetTransactionErrorMessageByIndexRequest = function (opt_data) {
    jspb.Message.initialize(this, opt_data, 0, -1, null, null);
};
if (goog.DEBUG && !COMPILED) {
    /**
     * @public
     * @override
     */
    proto.flow.execution.GetTransactionErrorMessageByIndexRequest.displayName = 'proto.flow.execution.GetTransactionErrorMessageByIndexRequest';
}
/**
 * Generated by JsPbCodeGenerator.
 * @param {Array=} opt_data Optional initial data array, typically from a
 * server response, or constructed directly in Javascript. The array is used
 * in place and becomes part of the constructed object. It is not cloned.
 * If no data is provided, the constructed object will be empty, but still
 * valid.
 * @extends {jspb.Message}
 * @constructor
 */
proto.flow.execution.GetTransactionErrorMessageResponse = function (opt_data) {
    jspb.Message.initialize(this, opt_data, 0, -1, null, null);
};
if (goog.DEBUG && !COMPILED) {
    /**
     * @public
     * @override
     */
    proto.flow.execution.GetTransactionErrorMessageResponse.displayName = 'proto.flow.execution.GetTransactionErrorMessageResponse';
}
/**
 * Generated by JsPbCodeGenerator.
 * @param {Array=} opt_data Optional initial data array, typically from a
 * server response, or constructed directly in Javascript. The array is used
 * in place and becomes part of the constructed object. It is not cloned.
 * If no data is provided, the constructed object will be empty, but still
 * valid.
 * @extends {jspb.Message}
 * @constructor
 */
proto.flow.execution.GetTransactionErrorMessagesByBlockIDRequest = function (opt_data) {
    jspb.Message.initialize(this, opt_data, 0, -1, null, null);
};
if (goog.DEBUG && !COMPILED) {
    /**
     * @public
     * @override
     */
    proto.flow.execution.GetTransactionErrorMessagesByBlockIDRequest.displayName = 'proto.flow.execution.GetTransactionErrorMessagesByBlockIDRequest';
}
/**
 * Generated by JsPbCodeGenerator.
 * @param {Array=} opt_data Optional initial data array, typically from a
 * server response, or constructed directly in Javascript. The array is used
 * in place and becomes part of the constructed object. It is not cloned.
 * If no data is provided, the constructed object will be empty, but still
 * valid.
 * @extends {jspb.Message}
 * @constructor
 */
proto.flow.execution.GetTransactionErrorMessagesResponse = function (opt_data) {
    jspb.Message.initialize(this, opt_data, 0, -1, proto.flow.execution.GetTransactionErrorMessagesResponse.repeatedFields_, null);
};
if (goog.DEBUG && !COMPILED) {
    /**
     * @public
     * @override
     */
    proto.flow.execution.GetTransactionErrorMessagesResponse.displayName = 'proto.flow.execution.GetTransactionErrorMessagesResponse';
}
/**
 * Generated by JsPbCodeGenerator.
 * @param {Array=} opt_data Optional initial data array, typically from a
 * server response, or constructed directly in Javascript. The array is used
 * in place and becomes part of the constructed object. It is not cloned.
 * If no data is provided, the constructed object will be empty, but still
 * valid.
 * @extends {jspb.Message}
 * @constructor
 */
proto.flow.execution.GetTransactionErrorMessagesResponse.Result = function (opt_data) {
    jspb.Message.initialize(this, opt_data, 0, -1, null, null);
};
if (goog.DEBUG && !COMPILED) {
    /**
     * @public
     * @override
     */
    proto.flow.execution.GetTransactionErrorMessagesResponse.Result.displayName = 'proto.flow.execution.GetTransactionErrorMessagesResponse.Result';
}
/**
 * Generated by JsPbCodeGenerator.
 * @param {Array=} opt_data Optional initial data array, typically from a
 * server response, or constructed directly in Javascript. The array is used
 * in place and becomes part of the constructed object. It is not cloned.
 * If no data is provided, the constructed object will be empty, but still
 * valid.
 * @extends {jspb.Message}
 * @constructor
 */
proto.flow.execution.GetRegisterAtBlockIDRequest = function (opt_data) {
    jspb.Message.initialize(this, opt_data, 0, -1, null, null);
};
if (goog.DEBUG && !COMPILED) {
    /**
     * @public
     * @override
     */
    proto.flow.execution.GetRegisterAtBlockIDRequest.displayName = 'proto.flow.execution.GetRegisterAtBlockIDRequest';
}
/**
 * Generated by JsPbCodeGenerator.
 * @param {Array=} opt_data Optional initial data array, typically from a
 * server response, or constructed directly in Javascript. The array is used
 * in place and becomes part of the constructed object. It is not cloned.
 * If no data is provided, the constructed object will be empty, but still
 * valid.
 * @extends {jspb.Message}
 * @constructor
 */
proto.flow.execution.GetRegisterAtBlockIDResponse = function (opt_data) {
    jspb.Message.initialize(this, opt_data, 0, -1, null, null);
};
if (goog.DEBUG && !COMPILED) {
    /**
     * @public
     * @override
     */
    proto.flow.execution.GetRegisterAtBlockIDResponse.displayName = 'proto.flow.execution.GetRegisterAtBlockIDResponse';
}
/**
 * Generated by JsPbCodeGenerator.
 * @param {Array=} opt_data Optional initial data array, typically from a
 * server response, or constructed directly in Javascript. The array is used
 * in place and becomes part of the constructed object. It is not cloned.
 * If no data is provided, the constructed object will be empty, but still
 * valid.
 * @extends {jspb.Message}
 * @constructor
 */
proto.flow.execution.GetLatestBlockHeaderRequest = function (opt_data) {
    jspb.Message.initialize(this, opt_data, 0, -1, null, null);
};
if (goog.DEBUG && !COMPILED) {
    /**
     * @public
     * @override
     */
    proto.flow.execution.GetLatestBlockHeaderRequest.displayName = 'proto.flow.execution.GetLatestBlockHeaderRequest';
}
/**
 * Generated by JsPbCodeGenerator.
 * @param {Array=} opt_data Optional initial data array, typically from a
 * server response, or constructed directly in Javascript. The array is used
 * in place and becomes part of the constructed object. It is not cloned.
 * If no data is provided, the constructed object will be empty, but still
 * valid.
 * @extends {jspb.Message}
 * @constructor
 */
proto.flow.execution.GetBlockHeaderByIDRequest = function (opt_data) {
    jspb.Message.initialize(this, opt_data, 0, -1, null, null);
};
if (goog.DEBUG && !COMPILED) {
    /**
     * @public
     * @override
     */
    proto.flow.execution.GetBlockHeaderByIDRequest.displayName = 'proto.flow.execution.GetBlockHeaderByIDRequest';
}
/**
 * Generated by JsPbCodeGenerator.
 * @param {Array=} opt_data Optional initial data array, typically from a
 * server response, or constructed directly in Javascript. The array is used
 * in place and becomes part of the constructed object. It is not cloned.
 * If no data is provided, the constructed object will be empty, but still
 * valid.
 * @extends {jspb.Message}
 * @constructor
 */
proto.flow.execution.BlockHeaderResponse = function (opt_data) {
    jspb.Message.initialize(this, opt_data, 0, -1, null, null);
};
if (goog.DEBUG && !COMPILED) {
    /**
     * @public
     * @override
     */
    proto.flow.execution.BlockHeaderResponse.displayName = 'proto.flow.execution.BlockHeaderResponse';
}
if (jspb.Message.GENERATE_TO_OBJECT) {
    /**
     * Creates an object representation of this proto.
     * Field names that are reserved in JavaScript and will be renamed to pb_name.
     * Optional fields that are not set will be set to undefined.
     * To access a reserved field use, foo.pb_<name>, eg, foo.pb_default.
     * For the list of reserved names please see:
     *     net/proto2/compiler/js/internal/generator.cc#kKeyword.
     * @param {boolean=} opt_includeInstance Deprecated. whether to include the
     *     JSPB instance for transitional soy proto support:
     *     http://goto/soy-param-migration
     * @return {!Object}
     */
    proto.flow.execution.PingRequest.prototype.toObject = function (opt_includeInstance) {
        return proto.flow.execution.PingRequest.toObject(opt_includeInstance, this);
    };
    /**
     * Static version of the {@see toObject} method.
     * @param {boolean|undefined} includeInstance Deprecated. Whether to include
     *     the JSPB instance for transitional soy proto support:
     *     http://goto/soy-param-migration
     * @param {!proto.flow.execution.PingRequest} msg The msg instance to transform.
     * @return {!Object}
     * @suppress {unusedLocalVariables} f is only used for nested messages
     */
    proto.flow.execution.PingRequest.toObject = function (includeInstance, msg) {
        var f, obj = {};
        if (includeInstance) {
            obj.$jspbMessageInstance = msg;
        }
        return obj;
    };
}
/**
 * Deserializes binary data (in protobuf wire format).
 * @param {jspb.ByteSource} bytes The bytes to deserialize.
 * @return {!proto.flow.execution.PingRequest}
 */
proto.flow.execution.PingRequest.deserializeBinary = function (bytes) {
    var reader = new jspb.BinaryReader(bytes);
    var msg = new proto.flow.execution.PingRequest;
    return proto.flow.execution.PingRequest.deserializeBinaryFromReader(msg, reader);
};
/**
 * Deserializes binary data (in protobuf wire format) from the
 * given reader into the given message object.
 * @param {!proto.flow.execution.PingRequest} msg The message object to deserialize into.
 * @param {!jspb.BinaryReader} reader The BinaryReader to use.
 * @return {!proto.flow.execution.PingRequest}
 */
proto.flow.execution.PingRequest.deserializeBinaryFromReader = function (msg, reader) {
    while (reader.nextField()) {
        if (reader.isEndGroup()) {
            break;
        }
        var field = reader.getFieldNumber();
        switch (field) {
            default:
                reader.skipField();
                break;
        }
    }
    return msg;
};
/**
 * Serializes the message to binary data (in protobuf wire format).
 * @return {!Uint8Array}
 */
proto.flow.execution.PingRequest.prototype.serializeBinary = function () {
    var writer = new jspb.BinaryWriter();
    proto.flow.execution.PingRequest.serializeBinaryToWriter(this, writer);
    return writer.getResultBuffer();
};
/**
 * Serializes the given message to binary data (in protobuf wire
 * format), writing to the given BinaryWriter.
 * @param {!proto.flow.execution.PingRequest} message
 * @param {!jspb.BinaryWriter} writer
 * @suppress {unusedLocalVariables} f is only used for nested messages
 */
proto.flow.execution.PingRequest.serializeBinaryToWriter = function (message, writer) {
    var f = undefined;
};
if (jspb.Message.GENERATE_TO_OBJECT) {
    /**
     * Creates an object representation of this proto.
     * Field names that are reserved in JavaScript and will be renamed to pb_name.
     * Optional fields that are not set will be set to undefined.
     * To access a reserved field use, foo.pb_<name>, eg, foo.pb_default.
     * For the list of reserved names please see:
     *     net/proto2/compiler/js/internal/generator.cc#kKeyword.
     * @param {boolean=} opt_includeInstance Deprecated. whether to include the
     *     JSPB instance for transitional soy proto support:
     *     http://goto/soy-param-migration
     * @return {!Object}
     */
    proto.flow.execution.PingResponse.prototype.toObject = function (opt_includeInstance) {
        return proto.flow.execution.PingResponse.toObject(opt_includeInstance, this);
    };
    /**
     * Static version of the {@see toObject} method.
     * @param {boolean|undefined} includeInstance Deprecated. Whether to include
     *     the JSPB instance for transitional soy proto support:
     *     http://goto/soy-param-migration
     * @param {!proto.flow.execution.PingResponse} msg The msg instance to transform.
     * @return {!Object}
     * @suppress {unusedLocalVariables} f is only used for nested messages
     */
    proto.flow.execution.PingResponse.toObject = function (includeInstance, msg) {
        var f, obj = {};
        if (includeInstance) {
            obj.$jspbMessageInstance = msg;
        }
        return obj;
    };
}
/**
 * Deserializes binary data (in protobuf wire format).
 * @param {jspb.ByteSource} bytes The bytes to deserialize.
 * @return {!proto.flow.execution.PingResponse}
 */
proto.flow.execution.PingResponse.deserializeBinary = function (bytes) {
    var reader = new jspb.BinaryReader(bytes);
    var msg = new proto.flow.execution.PingResponse;
    return proto.flow.execution.PingResponse.deserializeBinaryFromReader(msg, reader);
};
/**
 * Deserializes binary data (in protobuf wire format) from the
 * given reader into the given message object.
 * @param {!proto.flow.execution.PingResponse} msg The message object to deserialize into.
 * @param {!jspb.BinaryReader} reader The BinaryReader to use.
 * @return {!proto.flow.execution.PingResponse}
 */
proto.flow.execution.PingResponse.deserializeBinaryFromReader = function (msg, reader) {
    while (reader.nextField()) {
        if (reader.isEndGroup()) {
            break;
        }
        var field = reader.getFieldNumber();
        switch (field) {
            default:
                reader.skipField();
                break;
        }
    }
    return msg;
};
/**
 * Serializes the message to binary data (in protobuf wire format).
 * @return {!Uint8Array}
 */
proto.flow.execution.PingResponse.prototype.serializeBinary = function () {
    var writer = new jspb.BinaryWriter();
    proto.flow.execution.PingResponse.serializeBinaryToWriter(this, writer);
    return writer.getResultBuffer();
};
/**
 * Serializes the given message to binary data (in protobuf wire
 * format), writing to the given BinaryWriter.
 * @param {!proto.flow.execution.PingResponse} message
 * @param {!jspb.BinaryWriter} writer
 * @suppress {unusedLocalVariables} f is only used for nested messages
 */
proto.flow.execution.PingResponse.serializeBinaryToWriter = function (message, writer) {
    var f = undefined;
};
if (jspb.Message.GENERATE_TO_OBJECT) {
    /**
     * Creates an object representation of this proto.
     * Field names that are reserved in JavaScript and will be renamed to pb_name.
     * Optional fields that are not set will be set to undefined.
     * To access a reserved field use, foo.pb_<name>, eg, foo.pb_default.
     * For the list of reserved names please see:
     *     net/proto2/compiler/js/internal/generator.cc#kKeyword.
     * @param {boolean=} opt_includeInstance Deprecated. whether to include the
     *     JSPB instance for transitional soy proto support:
     *     http://goto/soy-param-migration
     * @return {!Object}
     */
    proto.flow.execution.GetAccountAtBlockIDRequest.prototype.toObject = function (opt_includeInstance) {
        return proto.flow.execution.GetAccountAtBlockIDRequest.toObject(opt_includeInstance, this);
    };
    /**
     * Static version of the {@see toObject} method.
     * @param {boolean|undefined} includeInstance Deprecated. Whether to include
     *     the JSPB instance for transitional soy proto support:
     *     http://goto/soy-param-migration
     * @param {!proto.flow.execution.GetAccountAtBlockIDRequest} msg The msg instance to transform.
     * @return {!Object}
     * @suppress {unusedLocalVariables} f is only used for nested messages
     */
    proto.flow.execution.GetAccountAtBlockIDRequest.toObject = function (includeInstance, msg) {
        var f, obj = {
            blockId: msg.getBlockId_asB64(),
            address: msg.getAddress_asB64()
        };
        if (includeInstance) {
            obj.$jspbMessageInstance = msg;
        }
        return obj;
    };
}
/**
 * Deserializes binary data (in protobuf wire format).
 * @param {jspb.ByteSource} bytes The bytes to deserialize.
 * @return {!proto.flow.execution.GetAccountAtBlockIDRequest}
 */
proto.flow.execution.GetAccountAtBlockIDRequest.deserializeBinary = function (bytes) {
    var reader = new jspb.BinaryReader(bytes);
    var msg = new proto.flow.execution.GetAccountAtBlockIDRequest;
    return proto.flow.execution.GetAccountAtBlockIDRequest.deserializeBinaryFromReader(msg, reader);
};
/**
 * Deserializes binary data (in protobuf wire format) from the
 * given reader into the given message object.
 * @param {!proto.flow.execution.GetAccountAtBlockIDRequest} msg The message object to deserialize into.
 * @param {!jspb.BinaryReader} reader The BinaryReader to use.
 * @return {!proto.flow.execution.GetAccountAtBlockIDRequest}
 */
proto.flow.execution.GetAccountAtBlockIDRequest.deserializeBinaryFromReader = function (msg, reader) {
    while (reader.nextField()) {
        if (reader.isEndGroup()) {
            break;
        }
        var field = reader.getFieldNumber();
        switch (field) {
            case 1:
                var value = /** @type {!Uint8Array} */ (reader.readBytes());
                msg.setBlockId(value);
                break;
            case 2:
                var value = /** @type {!Uint8Array} */ (reader.readBytes());
                msg.setAddress(value);
                break;
            default:
                reader.skipField();
                break;
        }
    }
    return msg;
};
/**
 * Serializes the message to binary data (in protobuf wire format).
 * @return {!Uint8Array}
 */
proto.flow.execution.GetAccountAtBlockIDRequest.prototype.serializeBinary = function () {
    var writer = new jspb.BinaryWriter();
    proto.flow.execution.GetAccountAtBlockIDRequest.serializeBinaryToWriter(this, writer);
    return writer.getResultBuffer();
};
/**
 * Serializes the given message to binary data (in protobuf wire
 * format), writing to the given BinaryWriter.
 * @param {!proto.flow.execution.GetAccountAtBlockIDRequest} message
 * @param {!jspb.BinaryWriter} writer
 * @suppress {unusedLocalVariables} f is only used for nested messages
 */
proto.flow.execution.GetAccountAtBlockIDRequest.serializeBinaryToWriter = function (message, writer) {
    var f = undefined;
    f = message.getBlockId_asU8();
    if (f.length > 0) {
        writer.writeBytes(1, f);
    }
    f = message.getAddress_asU8();
    if (f.length > 0) {
        writer.writeBytes(2, f);
    }
};
/**
 * optional bytes block_id = 1;
 * @return {!(string|Uint8Array)}
 */
proto.flow.execution.GetAccountAtBlockIDRequest.prototype.getBlockId = function () {
    return /** @type {!(string|Uint8Array)} */ (jspb.Message.getFieldWithDefault(this, 1, ""));
};
/**
 * optional bytes block_id = 1;
 * This is a type-conversion wrapper around `getBlockId()`
 * @return {string}
 */
proto.flow.execution.GetAccountAtBlockIDRequest.prototype.getBlockId_asB64 = function () {
    return /** @type {string} */ (jspb.Message.bytesAsB64(this.getBlockId()));
};
/**
 * optional bytes block_id = 1;
 * Note that Uint8Array is not supported on all browsers.
 * @see http://caniuse.com/Uint8Array
 * This is a type-conversion wrapper around `getBlockId()`
 * @return {!Uint8Array}
 */
proto.flow.execution.GetAccountAtBlockIDRequest.prototype.getBlockId_asU8 = function () {
    return /** @type {!Uint8Array} */ (jspb.Message.bytesAsU8(this.getBlockId()));
};
/**
 * @param {!(string|Uint8Array)} value
 * @return {!proto.flow.execution.GetAccountAtBlockIDRequest} returns this
 */
proto.flow.execution.GetAccountAtBlockIDRequest.prototype.setBlockId = function (value) {
    return jspb.Message.setProto3BytesField(this, 1, value);
};
/**
 * optional bytes address = 2;
 * @return {!(string|Uint8Array)}
 */
proto.flow.execution.GetAccountAtBlockIDRequest.prototype.getAddress = function () {
    return /** @type {!(string|Uint8Array)} */ (jspb.Message.getFieldWithDefault(this, 2, ""));
};
/**
 * optional bytes address = 2;
 * This is a type-conversion wrapper around `getAddress()`
 * @return {string}
 */
proto.flow.execution.GetAccountAtBlockIDRequest.prototype.getAddress_asB64 = function () {
    return /** @type {string} */ (jspb.Message.bytesAsB64(this.getAddress()));
};
/**
 * optional bytes address = 2;
 * Note that Uint8Array is not supported on all browsers.
 * @see http://caniuse.com/Uint8Array
 * This is a type-conversion wrapper around `getAddress()`
 * @return {!Uint8Array}
 */
proto.flow.execution.GetAccountAtBlockIDRequest.prototype.getAddress_asU8 = function () {
    return /** @type {!Uint8Array} */ (jspb.Message.bytesAsU8(this.getAddress()));
};
/**
 * @param {!(string|Uint8Array)} value
 * @return {!proto.flow.execution.GetAccountAtBlockIDRequest} returns this
 */
proto.flow.execution.GetAccountAtBlockIDRequest.prototype.setAddress = function (value) {
    return jspb.Message.setProto3BytesField(this, 2, value);
};
if (jspb.Message.GENERATE_TO_OBJECT) {
    /**
     * Creates an object representation of this proto.
     * Field names that are reserved in JavaScript and will be renamed to pb_name.
     * Optional fields that are not set will be set to undefined.
     * To access a reserved field use, foo.pb_<name>, eg, foo.pb_default.
     * For the list of reserved names please see:
     *     net/proto2/compiler/js/internal/generator.cc#kKeyword.
     * @param {boolean=} opt_includeInstance Deprecated. whether to include the
     *     JSPB instance for transitional soy proto support:
     *     http://goto/soy-param-migration
     * @return {!Object}
     */
    proto.flow.execution.GetAccountAtBlockIDResponse.prototype.toObject = function (opt_includeInstance) {
        return proto.flow.execution.GetAccountAtBlockIDResponse.toObject(opt_includeInstance, this);
    };
    /**
     * Static version of the {@see toObject} method.
     * @param {boolean|undefined} includeInstance Deprecated. Whether to include
     *     the JSPB instance for transitional soy proto support:
     *     http://goto/soy-param-migration
     * @param {!proto.flow.execution.GetAccountAtBlockIDResponse} msg The msg instance to transform.
     * @return {!Object}
     * @suppress {unusedLocalVariables} f is only used for nested messages
     */
    proto.flow.execution.GetAccountAtBlockIDResponse.toObject = function (includeInstance, msg) {
        var f, obj = {
            account: (f = msg.getAccount()) && flow_entities_account_pb.Account.toObject(includeInstance, f)
        };
        if (includeInstance) {
            obj.$jspbMessageInstance = msg;
        }
        return obj;
    };
}
/**
 * Deserializes binary data (in protobuf wire format).
 * @param {jspb.ByteSource} bytes The bytes to deserialize.
 * @return {!proto.flow.execution.GetAccountAtBlockIDResponse}
 */
proto.flow.execution.GetAccountAtBlockIDResponse.deserializeBinary = function (bytes) {
    var reader = new jspb.BinaryReader(bytes);
    var msg = new proto.flow.execution.GetAccountAtBlockIDResponse;
    return proto.flow.execution.GetAccountAtBlockIDResponse.deserializeBinaryFromReader(msg, reader);
};
/**
 * Deserializes binary data (in protobuf wire format) from the
 * given reader into the given message object.
 * @param {!proto.flow.execution.GetAccountAtBlockIDResponse} msg The message object to deserialize into.
 * @param {!jspb.BinaryReader} reader The BinaryReader to use.
 * @return {!proto.flow.execution.GetAccountAtBlockIDResponse}
 */
proto.flow.execution.GetAccountAtBlockIDResponse.deserializeBinaryFromReader = function (msg, reader) {
    while (reader.nextField()) {
        if (reader.isEndGroup()) {
            break;
        }
        var field = reader.getFieldNumber();
        switch (field) {
            case 1:
                var value = new flow_entities_account_pb.Account;
                reader.readMessage(value, flow_entities_account_pb.Account.deserializeBinaryFromReader);
                msg.setAccount(value);
                break;
            default:
                reader.skipField();
                break;
        }
    }
    return msg;
};
/**
 * Serializes the message to binary data (in protobuf wire format).
 * @return {!Uint8Array}
 */
proto.flow.execution.GetAccountAtBlockIDResponse.prototype.serializeBinary = function () {
    var writer = new jspb.BinaryWriter();
    proto.flow.execution.GetAccountAtBlockIDResponse.serializeBinaryToWriter(this, writer);
    return writer.getResultBuffer();
};
/**
 * Serializes the given message to binary data (in protobuf wire
 * format), writing to the given BinaryWriter.
 * @param {!proto.flow.execution.GetAccountAtBlockIDResponse} message
 * @param {!jspb.BinaryWriter} writer
 * @suppress {unusedLocalVariables} f is only used for nested messages
 */
proto.flow.execution.GetAccountAtBlockIDResponse.serializeBinaryToWriter = function (message, writer) {
    var f = undefined;
    f = message.getAccount();
    if (f != null) {
        writer.writeMessage(1, f, flow_entities_account_pb.Account.serializeBinaryToWriter);
    }
};
/**
 * optional flow.entities.Account account = 1;
 * @return {?proto.flow.entities.Account}
 */
proto.flow.execution.GetAccountAtBlockIDResponse.prototype.getAccount = function () {
    return /** @type{?proto.flow.entities.Account} */ (jspb.Message.getWrapperField(this, flow_entities_account_pb.Account, 1));
};
/**
 * @param {?proto.flow.entities.Account|undefined} value
 * @return {!proto.flow.execution.GetAccountAtBlockIDResponse} returns this
*/
proto.flow.execution.GetAccountAtBlockIDResponse.prototype.setAccount = function (value) {
    return jspb.Message.setWrapperField(this, 1, value);
};
/**
 * Clears the message field making it undefined.
 * @return {!proto.flow.execution.GetAccountAtBlockIDResponse} returns this
 */
proto.flow.execution.GetAccountAtBlockIDResponse.prototype.clearAccount = function () {
    return this.setAccount(undefined);
};
/**
 * Returns whether this field is set.
 * @return {boolean}
 */
proto.flow.execution.GetAccountAtBlockIDResponse.prototype.hasAccount = function () {
    return jspb.Message.getField(this, 1) != null;
};
/**
 * List of repeated fields within this message type.
 * @private {!Array<number>}
 * @const
 */
proto.flow.execution.ExecuteScriptAtBlockIDRequest.repeatedFields_ = [3];
if (jspb.Message.GENERATE_TO_OBJECT) {
    /**
     * Creates an object representation of this proto.
     * Field names that are reserved in JavaScript and will be renamed to pb_name.
     * Optional fields that are not set will be set to undefined.
     * To access a reserved field use, foo.pb_<name>, eg, foo.pb_default.
     * For the list of reserved names please see:
     *     net/proto2/compiler/js/internal/generator.cc#kKeyword.
     * @param {boolean=} opt_includeInstance Deprecated. whether to include the
     *     JSPB instance for transitional soy proto support:
     *     http://goto/soy-param-migration
     * @return {!Object}
     */
    proto.flow.execution.ExecuteScriptAtBlockIDRequest.prototype.toObject = function (opt_includeInstance) {
        return proto.flow.execution.ExecuteScriptAtBlockIDRequest.toObject(opt_includeInstance, this);
    };
    /**
     * Static version of the {@see toObject} method.
     * @param {boolean|undefined} includeInstance Deprecated. Whether to include
     *     the JSPB instance for transitional soy proto support:
     *     http://goto/soy-param-migration
     * @param {!proto.flow.execution.ExecuteScriptAtBlockIDRequest} msg The msg instance to transform.
     * @return {!Object}
     * @suppress {unusedLocalVariables} f is only used for nested messages
     */
    proto.flow.execution.ExecuteScriptAtBlockIDRequest.toObject = function (includeInstance, msg) {
        var f, obj = {
            blockId: msg.getBlockId_asB64(),
            script: msg.getScript_asB64(),
            argumentsList: msg.getArgumentsList_asB64()
        };
        if (includeInstance) {
            obj.$jspbMessageInstance = msg;
        }
        return obj;
    };
}
/**
 * Deserializes binary data (in protobuf wire format).
 * @param {jspb.ByteSource} bytes The bytes to deserialize.
 * @return {!proto.flow.execution.ExecuteScriptAtBlockIDRequest}
 */
proto.flow.execution.ExecuteScriptAtBlockIDRequest.deserializeBinary = function (bytes) {
    var reader = new jspb.BinaryReader(bytes);
    var msg = new proto.flow.execution.ExecuteScriptAtBlockIDRequest;
    return proto.flow.execution.ExecuteScriptAtBlockIDRequest.deserializeBinaryFromReader(msg, reader);
};
/**
 * Deserializes binary data (in protobuf wire format) from the
 * given reader into the given message object.
 * @param {!proto.flow.execution.ExecuteScriptAtBlockIDRequest} msg The message object to deserialize into.
 * @param {!jspb.BinaryReader} reader The BinaryReader to use.
 * @return {!proto.flow.execution.ExecuteScriptAtBlockIDRequest}
 */
proto.flow.execution.ExecuteScriptAtBlockIDRequest.deserializeBinaryFromReader = function (msg, reader) {
    while (reader.nextField()) {
        if (reader.isEndGroup()) {
            break;
        }
        var field = reader.getFieldNumber();
        switch (field) {
            case 1:
                var value = /** @type {!Uint8Array} */ (reader.readBytes());
                msg.setBlockId(value);
                break;
            case 2:
                var value = /** @type {!Uint8Array} */ (reader.readBytes());
                msg.setScript(value);
                break;
            case 3:
                var value = /** @type {!Uint8Array} */ (reader.readBytes());
                msg.addArguments(value);
                break;
            default:
                reader.skipField();
                break;
        }
    }
    return msg;
};
/**
 * Serializes the message to binary data (in protobuf wire format).
 * @return {!Uint8Array}
 */
proto.flow.execution.ExecuteScriptAtBlockIDRequest.prototype.serializeBinary = function () {
    var writer = new jspb.BinaryWriter();
    proto.flow.execution.ExecuteScriptAtBlockIDRequest.serializeBinaryToWriter(this, writer);
    return writer.getResultBuffer();
};
/**
 * Serializes the given message to binary data (in protobuf wire
 * format), writing to the given BinaryWriter.
 * @param {!proto.flow.execution.ExecuteScriptAtBlockIDRequest} message
 * @param {!jspb.BinaryWriter} writer
 * @suppress {unusedLocalVariables} f is only used for nested messages
 */
proto.flow.execution.ExecuteScriptAtBlockIDRequest.serializeBinaryToWriter = function (message, writer) {
    var f = undefined;
    f = message.getBlockId_asU8();
    if (f.length > 0) {
        writer.writeBytes(1, f);
    }
    f = message.getScript_asU8();
    if (f.length > 0) {
        writer.writeBytes(2, f);
    }
    f = message.getArgumentsList_asU8();
    if (f.length > 0) {
        writer.writeRepeatedBytes(3, f);
    }
};
/**
 * optional bytes block_id = 1;
 * @return {!(string|Uint8Array)}
 */
proto.flow.execution.ExecuteScriptAtBlockIDRequest.prototype.getBlockId = function () {
    return /** @type {!(string|Uint8Array)} */ (jspb.Message.getFieldWithDefault(this, 1, ""));
};
/**
 * optional bytes block_id = 1;
 * This is a type-conversion wrapper around `getBlockId()`
 * @return {string}
 */
proto.flow.execution.ExecuteScriptAtBlockIDRequest.prototype.getBlockId_asB64 = function () {
    return /** @type {string} */ (jspb.Message.bytesAsB64(this.getBlockId()));
};
/**
 * optional bytes block_id = 1;
 * Note that Uint8Array is not supported on all browsers.
 * @see http://caniuse.com/Uint8Array
 * This is a type-conversion wrapper around `getBlockId()`
 * @return {!Uint8Array}
 */
proto.flow.execution.ExecuteScriptAtBlockIDRequest.prototype.getBlockId_asU8 = function () {
    return /** @type {!Uint8Array} */ (jspb.Message.bytesAsU8(this.getBlockId()));
};
/**
 * @param {!(string|Uint8Array)} value
 * @return {!proto.flow.execution.ExecuteScriptAtBlockIDRequest} returns this
 */
proto.flow.execution.ExecuteScriptAtBlockIDRequest.prototype.setBlockId = function (value) {
    return jspb.Message.setProto3BytesField(this, 1, value);
};
/**
 * optional bytes script = 2;
 * @return {!(string|Uint8Array)}
 */
proto.flow.execution.ExecuteScriptAtBlockIDRequest.prototype.getScript = function () {
    return /** @type {!(string|Uint8Array)} */ (jspb.Message.getFieldWithDefault(this, 2, ""));
};
/**
 * optional bytes script = 2;
 * This is a type-conversion wrapper around `getScript()`
 * @return {string}
 */
proto.flow.execution.ExecuteScriptAtBlockIDRequest.prototype.getScript_asB64 = function () {
    return /** @type {string} */ (jspb.Message.bytesAsB64(this.getScript()));
};
/**
 * optional bytes script = 2;
 * Note that Uint8Array is not supported on all browsers.
 * @see http://caniuse.com/Uint8Array
 * This is a type-conversion wrapper around `getScript()`
 * @return {!Uint8Array}
 */
proto.flow.execution.ExecuteScriptAtBlockIDRequest.prototype.getScript_asU8 = function () {
    return /** @type {!Uint8Array} */ (jspb.Message.bytesAsU8(this.getScript()));
};
/**
 * @param {!(string|Uint8Array)} value
 * @return {!proto.flow.execution.ExecuteScriptAtBlockIDRequest} returns this
 */
proto.flow.execution.ExecuteScriptAtBlockIDRequest.prototype.setScript = function (value) {
    return jspb.Message.setProto3BytesField(this, 2, value);
};
/**
 * repeated bytes arguments = 3;
 * @return {!(Array<!Uint8Array>|Array<string>)}
 */
proto.flow.execution.ExecuteScriptAtBlockIDRequest.prototype.getArgumentsList = function () {
    return /** @type {!(Array<!Uint8Array>|Array<string>)} */ (jspb.Message.getRepeatedField(this, 3));
};
/**
 * repeated bytes arguments = 3;
 * This is a type-conversion wrapper around `getArgumentsList()`
 * @return {!Array<string>}
 */
proto.flow.execution.ExecuteScriptAtBlockIDRequest.prototype.getArgumentsList_asB64 = function () {
    return /** @type {!Array<string>} */ (jspb.Message.bytesListAsB64(this.getArgumentsList()));
};
/**
 * repeated bytes arguments = 3;
 * Note that Uint8Array is not supported on all browsers.
 * @see http://caniuse.com/Uint8Array
 * This is a type-conversion wrapper around `getArgumentsList()`
 * @return {!Array<!Uint8Array>}
 */
proto.flow.execution.ExecuteScriptAtBlockIDRequest.prototype.getArgumentsList_asU8 = function () {
    return /** @type {!Array<!Uint8Array>} */ (jspb.Message.bytesListAsU8(this.getArgumentsList()));
};
/**
 * @param {!(Array<!Uint8Array>|Array<string>)} value
 * @return {!proto.flow.execution.ExecuteScriptAtBlockIDRequest} returns this
 */
proto.flow.execution.ExecuteScriptAtBlockIDRequest.prototype.setArgumentsList = function (value) {
    return jspb.Message.setField(this, 3, value || []);
};
/**
 * @param {!(string|Uint8Array)} value
 * @param {number=} opt_index
 * @return {!proto.flow.execution.ExecuteScriptAtBlockIDRequest} returns this
 */
proto.flow.execution.ExecuteScriptAtBlockIDRequest.prototype.addArguments = function (value, opt_index) {
    return jspb.Message.addToRepeatedField(this, 3, value, opt_index);
};
/**
 * Clears the list making it empty but non-null.
 * @return {!proto.flow.execution.ExecuteScriptAtBlockIDRequest} returns this
 */
proto.flow.execution.ExecuteScriptAtBlockIDRequest.prototype.clearArgumentsList = function () {
    return this.setArgumentsList([]);
};
if (jspb.Message.GENERATE_TO_OBJECT) {
    /**
     * Creates an object representation of this proto.
     * Field names that are reserved in JavaScript and will be renamed to pb_name.
     * Optional fields that are not set will be set to undefined.
     * To access a reserved field use, foo.pb_<name>, eg, foo.pb_default.
     * For the list of reserved names please see:
     *     net/proto2/compiler/js/internal/generator.cc#kKeyword.
     * @param {boolean=} opt_includeInstance Deprecated. whether to include the
     *     JSPB instance for transitional soy proto support:
     *     http://goto/soy-param-migration
     * @return {!Object}
     */
    proto.flow.execution.ExecuteScriptAtBlockIDResponse.prototype.toObject = function (opt_includeInstance) {
        return proto.flow.execution.ExecuteScriptAtBlockIDResponse.toObject(opt_includeInstance, this);
    };
    /**
     * Static version of the {@see toObject} method.
     * @param {boolean|undefined} includeInstance Deprecated. Whether to include
     *     the JSPB instance for transitional soy proto support:
     *     http://goto/soy-param-migration
     * @param {!proto.flow.execution.ExecuteScriptAtBlockIDResponse} msg The msg instance to transform.
     * @return {!Object}
     * @suppress {unusedLocalVariables} f is only used for nested messages
     */
    proto.flow.execution.ExecuteScriptAtBlockIDResponse.toObject = function (includeInstance, msg) {
        var f, obj = {
            value: msg.getValue_asB64(),
            computationUsage: jspb.Message.getFieldWithDefault(msg, 2, 0)
        };
        if (includeInstance) {
            obj.$jspbMessageInstance = msg;
        }
        return obj;
    };
}
/**
 * Deserializes binary data (in protobuf wire format).
 * @param {jspb.ByteSource} bytes The bytes to deserialize.
 * @return {!proto.flow.execution.ExecuteScriptAtBlockIDResponse}
 */
proto.flow.execution.ExecuteScriptAtBlockIDResponse.deserializeBinary = function (bytes) {
    var reader = new jspb.BinaryReader(bytes);
    var msg = new proto.flow.execution.ExecuteScriptAtBlockIDResponse;
    return proto.flow.execution.ExecuteScriptAtBlockIDResponse.deserializeBinaryFromReader(msg, reader);
};
/**
 * Deserializes binary data (in protobuf wire format) from the
 * given reader into the given message object.
 * @param {!proto.flow.execution.ExecuteScriptAtBlockIDResponse} msg The message object to deserialize into.
 * @param {!jspb.BinaryReader} reader The BinaryReader to use.
 * @return {!proto.flow.execution.ExecuteScriptAtBlockIDResponse}
 */
proto.flow.execution.ExecuteScriptAtBlockIDResponse.deserializeBinaryFromReader = function (msg, reader) {
    while (reader.nextField()) {
        if (reader.isEndGroup()) {
            break;
        }
        var field = reader.getFieldNumber();
        switch (field) {
            case 1:
                var value = /** @type {!Uint8Array} */ (reader.readBytes());
                msg.setValue(value);
                break;
            case 2:
                var value = /** @type {number} */ (reader.readUint64());
                msg.setComputationUsage(value);
                break;
            default:
                reader.skipField();
                break;
        }
    }
    return msg;
};
/**
 * Serializes the message to binary data (in protobuf wire format).
 * @return {!Uint8Array}
 */
proto.flow.execution.ExecuteScriptAtBlockIDResponse.prototype.serializeBinary = function () {
    var writer = new jspb.BinaryWriter();
    proto.flow.execution.ExecuteScriptAtBlockIDResponse.serializeBinaryToWriter(this, writer);
    return writer.getResultBuffer();
};
/**
 * Serializes the given message to binary data (in protobuf wire
 * format), writing to the given BinaryWriter.
 * @param {!proto.flow.execution.ExecuteScriptAtBlockIDResponse} message
 * @param {!jspb.BinaryWriter} writer
 * @suppress {unusedLocalVariables} f is only used for nested messages
 */
proto.flow.execution.ExecuteScriptAtBlockIDResponse.serializeBinaryToWriter = function (message, writer) {
    var f = undefined;
    f = message.getValue_asU8();
    if (f.length > 0) {
        writer.writeBytes(1, f);
    }
    f = message.getComputationUsage();
    if (f !== 0) {
        writer.writeUint64(2, f);
    }
};
/**
 * optional bytes value = 1;
 * @return {!(string|Uint8Array)}
 */
proto.flow.execution.ExecuteScriptAtBlockIDResponse.prototype.getValue = function () {
    return /** @type {!(string|Uint8Array)} */ (jspb.Message.getFieldWithDefault(this, 1, ""));
};
/**
 * optional bytes value = 1;
 * This is a type-conversion wrapper around `getValue()`
 * @return {string}
 */
proto.flow.execution.ExecuteScriptAtBlockIDResponse.prototype.getValue_asB64 = function () {
    return /** @type {string} */ (jspb.Message.bytesAsB64(this.getValue()));
};
/**
 * optional bytes value = 1;
 * Note that Uint8Array is not supported on all browsers.
 * @see http://caniuse.com/Uint8Array
 * This is a type-conversion wrapper around `getValue()`
 * @return {!Uint8Array}
 */
proto.flow.execution.ExecuteScriptAtBlockIDResponse.prototype.getValue_asU8 = function () {
    return /** @type {!Uint8Array} */ (jspb.Message.bytesAsU8(this.getValue()));
};
/**
 * @param {!(string|Uint8Array)} value
 * @return {!proto.flow.execution.ExecuteScriptAtBlockIDResponse} returns this
 */
proto.flow.execution.ExecuteScriptAtBlockIDResponse.prototype.setValue = function (value) {
    return jspb.Message.setProto3BytesField(this, 1, value);
};
/**
 * optional uint64 computation_usage = 2;
 * @return {number}
 */
proto.flow.execution.ExecuteScriptAtBlockIDResponse.prototype.getComputationUsage = function () {
    return /** @type {number} */ (jspb.Message.getFieldWithDefault(this, 2, 0));
};
/**
 * @param {number} value
 * @return {!proto.flow.execution.ExecuteScriptAtBlockIDResponse} returns this
 */
proto.flow.execution.ExecuteScriptAtBlockIDResponse.prototype.setComputationUsage = function (value) {
    return jspb.Message.setProto3IntField(this, 2, value);
};
/**
 * List of repeated fields within this message type.
 * @private {!Array<number>}
 * @const
 */
proto.flow.execution.GetEventsForBlockIDsResponse.repeatedFields_ = [1];
if (jspb.Message.GENERATE_TO_OBJECT) {
    /**
     * Creates an object representation of this proto.
     * Field names that are reserved in JavaScript and will be renamed to pb_name.
     * Optional fields that are not set will be set to undefined.
     * To access a reserved field use, foo.pb_<name>, eg, foo.pb_default.
     * For the list of reserved names please see:
     *     net/proto2/compiler/js/internal/generator.cc#kKeyword.
     * @param {boolean=} opt_includeInstance Deprecated. whether to include the
     *     JSPB instance for transitional soy proto support:
     *     http://goto/soy-param-migration
     * @return {!Object}
     */
    proto.flow.execution.GetEventsForBlockIDsResponse.prototype.toObject = function (opt_includeInstance) {
        return proto.flow.execution.GetEventsForBlockIDsResponse.toObject(opt_includeInstance, this);
    };
    /**
     * Static version of the {@see toObject} method.
     * @param {boolean|undefined} includeInstance Deprecated. Whether to include
     *     the JSPB instance for transitional soy proto support:
     *     http://goto/soy-param-migration
     * @param {!proto.flow.execution.GetEventsForBlockIDsResponse} msg The msg instance to transform.
     * @return {!Object}
     * @suppress {unusedLocalVariables} f is only used for nested messages
     */
    proto.flow.execution.GetEventsForBlockIDsResponse.toObject = function (includeInstance, msg) {
        var f, obj = {
            resultsList: jspb.Message.toObjectList(msg.getResultsList(), proto.flow.execution.GetEventsForBlockIDsResponse.Result.toObject, includeInstance),
            eventEncodingVersion: jspb.Message.getFieldWithDefault(msg, 2, 0)
        };
        if (includeInstance) {
            obj.$jspbMessageInstance = msg;
        }
        return obj;
    };
}
/**
 * Deserializes binary data (in protobuf wire format).
 * @param {jspb.ByteSource} bytes The bytes to deserialize.
 * @return {!proto.flow.execution.GetEventsForBlockIDsResponse}
 */
proto.flow.execution.GetEventsForBlockIDsResponse.deserializeBinary = function (bytes) {
    var reader = new jspb.BinaryReader(bytes);
    var msg = new proto.flow.execution.GetEventsForBlockIDsResponse;
    return proto.flow.execution.GetEventsForBlockIDsResponse.deserializeBinaryFromReader(msg, reader);
};
/**
 * Deserializes binary data (in protobuf wire format) from the
 * given reader into the given message object.
 * @param {!proto.flow.execution.GetEventsForBlockIDsResponse} msg The message object to deserialize into.
 * @param {!jspb.BinaryReader} reader The BinaryReader to use.
 * @return {!proto.flow.execution.GetEventsForBlockIDsResponse}
 */
proto.flow.execution.GetEventsForBlockIDsResponse.deserializeBinaryFromReader = function (msg, reader) {
    while (reader.nextField()) {
        if (reader.isEndGroup()) {
            break;
        }
        var field = reader.getFieldNumber();
        switch (field) {
            case 1:
                var value = new proto.flow.execution.GetEventsForBlockIDsResponse.Result;
                reader.readMessage(value, proto.flow.execution.GetEventsForBlockIDsResponse.Result.deserializeBinaryFromReader);
                msg.addResults(value);
                break;
            case 2:
                var value = /** @type {!proto.flow.entities.EventEncodingVersion} */ (reader.readEnum());
                msg.setEventEncodingVersion(value);
                break;
            default:
                reader.skipField();
                break;
        }
    }
    return msg;
};
/**
 * Serializes the message to binary data (in protobuf wire format).
 * @return {!Uint8Array}
 */
proto.flow.execution.GetEventsForBlockIDsResponse.prototype.serializeBinary = function () {
    var writer = new jspb.BinaryWriter();
    proto.flow.execution.GetEventsForBlockIDsResponse.serializeBinaryToWriter(this, writer);
    return writer.getResultBuffer();
};
/**
 * Serializes the given message to binary data (in protobuf wire
 * format), writing to the given BinaryWriter.
 * @param {!proto.flow.execution.GetEventsForBlockIDsResponse} message
 * @param {!jspb.BinaryWriter} writer
 * @suppress {unusedLocalVariables} f is only used for nested messages
 */
proto.flow.execution.GetEventsForBlockIDsResponse.serializeBinaryToWriter = function (message, writer) {
    var f = undefined;
    f = message.getResultsList();
    if (f.length > 0) {
        writer.writeRepeatedMessage(1, f, proto.flow.execution.GetEventsForBlockIDsResponse.Result.serializeBinaryToWriter);
    }
    f = message.getEventEncodingVersion();
    if (f !== 0.0) {
        writer.writeEnum(2, f);
    }
};
/**
 * List of repeated fields within this message type.
 * @private {!Array<number>}
 * @const
 */
proto.flow.execution.GetEventsForBlockIDsResponse.Result.repeatedFields_ = [3];
if (jspb.Message.GENERATE_TO_OBJECT) {
    /**
     * Creates an object representation of this proto.
     * Field names that are reserved in JavaScript and will be renamed to pb_name.
     * Optional fields that are not set will be set to undefined.
     * To access a reserved field use, foo.pb_<name>, eg, foo.pb_default.
     * For the list of reserved names please see:
     *     net/proto2/compiler/js/internal/generator.cc#kKeyword.
     * @param {boolean=} opt_includeInstance Deprecated. whether to include the
     *     JSPB instance for transitional soy proto support:
     *     http://goto/soy-param-migration
     * @return {!Object}
     */
    proto.flow.execution.GetEventsForBlockIDsResponse.Result.prototype.toObject = function (opt_includeInstance) {
        return proto.flow.execution.GetEventsForBlockIDsResponse.Result.toObject(opt_includeInstance, this);
    };
    /**
     * Static version of the {@see toObject} method.
     * @param {boolean|undefined} includeInstance Deprecated. Whether to include
     *     the JSPB instance for transitional soy proto support:
     *     http://goto/soy-param-migration
     * @param {!proto.flow.execution.GetEventsForBlockIDsResponse.Result} msg The msg instance to transform.
     * @return {!Object}
     * @suppress {unusedLocalVariables} f is only used for nested messages
     */
    proto.flow.execution.GetEventsForBlockIDsResponse.Result.toObject = function (includeInstance, msg) {
        var f, obj = {
            blockId: msg.getBlockId_asB64(),
            blockHeight: jspb.Message.getFieldWithDefault(msg, 2, 0),
            eventsList: jspb.Message.toObjectList(msg.getEventsList(), flow_entities_event_pb.Event.toObject, includeInstance)
        };
        if (includeInstance) {
            obj.$jspbMessageInstance = msg;
        }
        return obj;
    };
}
/**
 * Deserializes binary data (in protobuf wire format).
 * @param {jspb.ByteSource} bytes The bytes to deserialize.
 * @return {!proto.flow.execution.GetEventsForBlockIDsResponse.Result}
 */
proto.flow.execution.GetEventsForBlockIDsResponse.Result.deserializeBinary = function (bytes) {
    var reader = new jspb.BinaryReader(bytes);
    var msg = new proto.flow.execution.GetEventsForBlockIDsResponse.Result;
    return proto.flow.execution.GetEventsForBlockIDsResponse.Result.deserializeBinaryFromReader(msg, reader);
};
/**
 * Deserializes binary data (in protobuf wire format) from the
 * given reader into the given message object.
 * @param {!proto.flow.execution.GetEventsForBlockIDsResponse.Result} msg The message object to deserialize into.
 * @param {!jspb.BinaryReader} reader The BinaryReader to use.
 * @return {!proto.flow.execution.GetEventsForBlockIDsResponse.Result}
 */
proto.flow.execution.GetEventsForBlockIDsResponse.Result.deserializeBinaryFromReader = function (msg, reader) {
    while (reader.nextField()) {
        if (reader.isEndGroup()) {
            break;
        }
        var field = reader.getFieldNumber();
        switch (field) {
            case 1:
                var value = /** @type {!Uint8Array} */ (reader.readBytes());
                msg.setBlockId(value);
                break;
            case 2:
                var value = /** @type {number} */ (reader.readUint64());
                msg.setBlockHeight(value);
                break;
            case 3:
                var value = new flow_entities_event_pb.Event;
                reader.readMessage(value, flow_entities_event_pb.Event.deserializeBinaryFromReader);
                msg.addEvents(value);
                break;
            default:
                reader.skipField();
                break;
        }
    }
    return msg;
};
/**
 * Serializes the message to binary data (in protobuf wire format).
 * @return {!Uint8Array}
 */
proto.flow.execution.GetEventsForBlockIDsResponse.Result.prototype.serializeBinary = function () {
    var writer = new jspb.BinaryWriter();
    proto.flow.execution.GetEventsForBlockIDsResponse.Result.serializeBinaryToWriter(this, writer);
    return writer.getResultBuffer();
};
/**
 * Serializes the given message to binary data (in protobuf wire
 * format), writing to the given BinaryWriter.
 * @param {!proto.flow.execution.GetEventsForBlockIDsResponse.Result} message
 * @param {!jspb.BinaryWriter} writer
 * @suppress {unusedLocalVariables} f is only used for nested messages
 */
proto.flow.execution.GetEventsForBlockIDsResponse.Result.serializeBinaryToWriter = function (message, writer) {
    var f = undefined;
    f = message.getBlockId_asU8();
    if (f.length > 0) {
        writer.writeBytes(1, f);
    }
    f = message.getBlockHeight();
    if (f !== 0) {
        writer.writeUint64(2, f);
    }
    f = message.getEventsList();
    if (f.length > 0) {
        writer.writeRepeatedMessage(3, f, flow_entities_event_pb.Event.serializeBinaryToWriter);
    }
};
/**
 * optional bytes block_id = 1;
 * @return {!(string|Uint8Array)}
 */
proto.flow.execution.GetEventsForBlockIDsResponse.Result.prototype.getBlockId = function () {
    return /** @type {!(string|Uint8Array)} */ (jspb.Message.getFieldWithDefault(this, 1, ""));
};
/**
 * optional bytes block_id = 1;
 * This is a type-conversion wrapper around `getBlockId()`
 * @return {string}
 */
proto.flow.execution.GetEventsForBlockIDsResponse.Result.prototype.getBlockId_asB64 = function () {
    return /** @type {string} */ (jspb.Message.bytesAsB64(this.getBlockId()));
};
/**
 * optional bytes block_id = 1;
 * Note that Uint8Array is not supported on all browsers.
 * @see http://caniuse.com/Uint8Array
 * This is a type-conversion wrapper around `getBlockId()`
 * @return {!Uint8Array}
 */
proto.flow.execution.GetEventsForBlockIDsResponse.Result.prototype.getBlockId_asU8 = function () {
    return /** @type {!Uint8Array} */ (jspb.Message.bytesAsU8(this.getBlockId()));
};
/**
 * @param {!(string|Uint8Array)} value
 * @return {!proto.flow.execution.GetEventsForBlockIDsResponse.Result} returns this
 */
proto.flow.execution.GetEventsForBlockIDsResponse.Result.prototype.setBlockId = function (value) {
    return jspb.Message.setProto3BytesField(this, 1, value);
};
/**
 * optional uint64 block_height = 2;
 * @return {number}
 */
proto.flow.execution.GetEventsForBlockIDsResponse.Result.prototype.getBlockHeight = function () {
    return /** @type {number} */ (jspb.Message.getFieldWithDefault(this, 2, 0));
};
/**
 * @param {number} value
 * @return {!proto.flow.execution.GetEventsForBlockIDsResponse.Result} returns this
 */
proto.flow.execution.GetEventsForBlockIDsResponse.Result.prototype.setBlockHeight = function (value) {
    return jspb.Message.setProto3IntField(this, 2, value);
};
/**
 * repeated flow.entities.Event events = 3;
 * @return {!Array<!proto.flow.entities.Event>}
 */
proto.flow.execution.GetEventsForBlockIDsResponse.Result.prototype.getEventsList = function () {
    return /** @type{!Array<!proto.flow.entities.Event>} */ (jspb.Message.getRepeatedWrapperField(this, flow_entities_event_pb.Event, 3));
};
/**
 * @param {!Array<!proto.flow.entities.Event>} value
 * @return {!proto.flow.execution.GetEventsForBlockIDsResponse.Result} returns this
*/
proto.flow.execution.GetEventsForBlockIDsResponse.Result.prototype.setEventsList = function (value) {
    return jspb.Message.setRepeatedWrapperField(this, 3, value);
};
/**
 * @param {!proto.flow.entities.Event=} opt_value
 * @param {number=} opt_index
 * @return {!proto.flow.entities.Event}
 */
proto.flow.execution.GetEventsForBlockIDsResponse.Result.prototype.addEvents = function (opt_value, opt_index) {
    return jspb.Message.addToRepeatedWrapperField(this, 3, opt_value, proto.flow.entities.Event, opt_index);
};
/**
 * Clears the list making it empty but non-null.
 * @return {!proto.flow.execution.GetEventsForBlockIDsResponse.Result} returns this
 */
proto.flow.execution.GetEventsForBlockIDsResponse.Result.prototype.clearEventsList = function () {
    return this.setEventsList([]);
};
/**
 * repeated Result results = 1;
 * @return {!Array<!proto.flow.execution.GetEventsForBlockIDsResponse.Result>}
 */
proto.flow.execution.GetEventsForBlockIDsResponse.prototype.getResultsList = function () {
    return /** @type{!Array<!proto.flow.execution.GetEventsForBlockIDsResponse.Result>} */ (jspb.Message.getRepeatedWrapperField(this, proto.flow.execution.GetEventsForBlockIDsResponse.Result, 1));
};
/**
 * @param {!Array<!proto.flow.execution.GetEventsForBlockIDsResponse.Result>} value
 * @return {!proto.flow.execution.GetEventsForBlockIDsResponse} returns this
*/
proto.flow.execution.GetEventsForBlockIDsResponse.prototype.setResultsList = function (value) {
    return jspb.Message.setRepeatedWrapperField(this, 1, value);
};
/**
 * @param {!proto.flow.execution.GetEventsForBlockIDsResponse.Result=} opt_value
 * @param {number=} opt_index
 * @return {!proto.flow.execution.GetEventsForBlockIDsResponse.Result}
 */
proto.flow.execution.GetEventsForBlockIDsResponse.prototype.addResults = function (opt_value, opt_index) {
    return jspb.Message.addToRepeatedWrapperField(this, 1, opt_value, proto.flow.execution.GetEventsForBlockIDsResponse.Result, opt_index);
};
/**
 * Clears the list making it empty but non-null.
 * @return {!proto.flow.execution.GetEventsForBlockIDsResponse} returns this
 */
proto.flow.execution.GetEventsForBlockIDsResponse.prototype.clearResultsList = function () {
    return this.setResultsList([]);
};
/**
 * optional flow.entities.EventEncodingVersion event_encoding_version = 2;
 * @return {!proto.flow.entities.EventEncodingVersion}
 */
proto.flow.execution.GetEventsForBlockIDsResponse.prototype.getEventEncodingVersion = function () {
    return /** @type {!proto.flow.entities.EventEncodingVersion} */ (jspb.Message.getFieldWithDefault(this, 2, 0));
};
/**
 * @param {!proto.flow.entities.EventEncodingVersion} value
 * @return {!proto.flow.execution.GetEventsForBlockIDsResponse} returns this
 */
proto.flow.execution.GetEventsForBlockIDsResponse.prototype.setEventEncodingVersion = function (value) {
    return jspb.Message.setProto3EnumField(this, 2, value);
};
/**
 * List of repeated fields within this message type.
 * @private {!Array<number>}
 * @const
 */
proto.flow.execution.GetEventsForBlockIDsRequest.repeatedFields_ = [2];
if (jspb.Message.GENERATE_TO_OBJECT) {
    /**
     * Creates an object representation of this proto.
     * Field names that are reserved in JavaScript and will be renamed to pb_name.
     * Optional fields that are not set will be set to undefined.
     * To access a reserved field use, foo.pb_<name>, eg, foo.pb_default.
     * For the list of reserved names please see:
     *     net/proto2/compiler/js/internal/generator.cc#kKeyword.
     * @param {boolean=} opt_includeInstance Deprecated. whether to include the
     *     JSPB instance for transitional soy proto support:
     *     http://goto/soy-param-migration
     * @return {!Object}
     */
    proto.flow.execution.GetEventsForBlockIDsRequest.prototype.toObject = function (opt_includeInstance) {
        return proto.flow.execution.GetEventsForBlockIDsRequest.toObject(opt_includeInstance, this);
    };
    /**
     * Static version of the {@see toObject} method.
     * @param {boolean|undefined} includeInstance Deprecated. Whether to include
     *     the JSPB instance for transitional soy proto support:
     *     http://goto/soy-param-migration
     * @param {!proto.flow.execution.GetEventsForBlockIDsRequest} msg The msg instance to transform.
     * @return {!Object}
     * @suppress {unusedLocalVariables} f is only used for nested messages
     */
    proto.flow.execution.GetEventsForBlockIDsRequest.toObject = function (includeInstance, msg) {
        var f, obj = {
            type: jspb.Message.getFieldWithDefault(msg, 1, ""),
            blockIdsList: msg.getBlockIdsList_asB64()
        };
        if (includeInstance) {
            obj.$jspbMessageInstance = msg;
        }
        return obj;
    };
}
/**
 * Deserializes binary data (in protobuf wire format).
 * @param {jspb.ByteSource} bytes The bytes to deserialize.
 * @return {!proto.flow.execution.GetEventsForBlockIDsRequest}
 */
proto.flow.execution.GetEventsForBlockIDsRequest.deserializeBinary = function (bytes) {
    var reader = new jspb.BinaryReader(bytes);
    var msg = new proto.flow.execution.GetEventsForBlockIDsRequest;
    return proto.flow.execution.GetEventsForBlockIDsRequest.deserializeBinaryFromReader(msg, reader);
};
/**
 * Deserializes binary data (in protobuf wire format) from the
 * given reader into the given message object.
 * @param {!proto.flow.execution.GetEventsForBlockIDsRequest} msg The message object to deserialize into.
 * @param {!jspb.BinaryReader} reader The BinaryReader to use.
 * @return {!proto.flow.execution.GetEventsForBlockIDsRequest}
 */
proto.flow.execution.GetEventsForBlockIDsRequest.deserializeBinaryFromReader = function (msg, reader) {
    while (reader.nextField()) {
        if (reader.isEndGroup()) {
            break;
        }
        var field = reader.getFieldNumber();
        switch (field) {
            case 1:
                var value = /** @type {string} */ (reader.readString());
                msg.setType(value);
                break;
            case 2:
                var value = /** @type {!Uint8Array} */ (reader.readBytes());
                msg.addBlockIds(value);
                break;
            default:
                reader.skipField();
                break;
        }
    }
    return msg;
};
/**
 * Serializes the message to binary data (in protobuf wire format).
 * @return {!Uint8Array}
 */
proto.flow.execution.GetEventsForBlockIDsRequest.prototype.serializeBinary = function () {
    var writer = new jspb.BinaryWriter();
    proto.flow.execution.GetEventsForBlockIDsRequest.serializeBinaryToWriter(this, writer);
    return writer.getResultBuffer();
};
/**
 * Serializes the given message to binary data (in protobuf wire
 * format), writing to the given BinaryWriter.
 * @param {!proto.flow.execution.GetEventsForBlockIDsRequest} message
 * @param {!jspb.BinaryWriter} writer
 * @suppress {unusedLocalVariables} f is only used for nested messages
 */
proto.flow.execution.GetEventsForBlockIDsRequest.serializeBinaryToWriter = function (message, writer) {
    var f = undefined;
    f = message.getType();
    if (f.length > 0) {
        writer.writeString(1, f);
    }
    f = message.getBlockIdsList_asU8();
    if (f.length > 0) {
        writer.writeRepeatedBytes(2, f);
    }
};
/**
 * optional string type = 1;
 * @return {string}
 */
proto.flow.execution.GetEventsForBlockIDsRequest.prototype.getType = function () {
    return /** @type {string} */ (jspb.Message.getFieldWithDefault(this, 1, ""));
};
/**
 * @param {string} value
 * @return {!proto.flow.execution.GetEventsForBlockIDsRequest} returns this
 */
proto.flow.execution.GetEventsForBlockIDsRequest.prototype.setType = function (value) {
    return jspb.Message.setProto3StringField(this, 1, value);
};
/**
 * repeated bytes block_ids = 2;
 * @return {!(Array<!Uint8Array>|Array<string>)}
 */
proto.flow.execution.GetEventsForBlockIDsRequest.prototype.getBlockIdsList = function () {
    return /** @type {!(Array<!Uint8Array>|Array<string>)} */ (jspb.Message.getRepeatedField(this, 2));
};
/**
 * repeated bytes block_ids = 2;
 * This is a type-conversion wrapper around `getBlockIdsList()`
 * @return {!Array<string>}
 */
proto.flow.execution.GetEventsForBlockIDsRequest.prototype.getBlockIdsList_asB64 = function () {
    return /** @type {!Array<string>} */ (jspb.Message.bytesListAsB64(this.getBlockIdsList()));
};
/**
 * repeated bytes block_ids = 2;
 * Note that Uint8Array is not supported on all browsers.
 * @see http://caniuse.com/Uint8Array
 * This is a type-conversion wrapper around `getBlockIdsList()`
 * @return {!Array<!Uint8Array>}
 */
proto.flow.execution.GetEventsForBlockIDsRequest.prototype.getBlockIdsList_asU8 = function () {
    return /** @type {!Array<!Uint8Array>} */ (jspb.Message.bytesListAsU8(this.getBlockIdsList()));
};
/**
 * @param {!(Array<!Uint8Array>|Array<string>)} value
 * @return {!proto.flow.execution.GetEventsForBlockIDsRequest} returns this
 */
proto.flow.execution.GetEventsForBlockIDsRequest.prototype.setBlockIdsList = function (value) {
    return jspb.Message.setField(this, 2, value || []);
};
/**
 * @param {!(string|Uint8Array)} value
 * @param {number=} opt_index
 * @return {!proto.flow.execution.GetEventsForBlockIDsRequest} returns this
 */
proto.flow.execution.GetEventsForBlockIDsRequest.prototype.addBlockIds = function (value, opt_index) {
    return jspb.Message.addToRepeatedField(this, 2, value, opt_index);
};
/**
 * Clears the list making it empty but non-null.
 * @return {!proto.flow.execution.GetEventsForBlockIDsRequest} returns this
 */
proto.flow.execution.GetEventsForBlockIDsRequest.prototype.clearBlockIdsList = function () {
    return this.setBlockIdsList([]);
};
if (jspb.Message.GENERATE_TO_OBJECT) {
    /**
     * Creates an object representation of this proto.
     * Field names that are reserved in JavaScript and will be renamed to pb_name.
     * Optional fields that are not set will be set to undefined.
     * To access a reserved field use, foo.pb_<name>, eg, foo.pb_default.
     * For the list of reserved names please see:
     *     net/proto2/compiler/js/internal/generator.cc#kKeyword.
     * @param {boolean=} opt_includeInstance Deprecated. whether to include the
     *     JSPB instance for transitional soy proto support:
     *     http://goto/soy-param-migration
     * @return {!Object}
     */
    proto.flow.execution.GetTransactionResultRequest.prototype.toObject = function (opt_includeInstance) {
        return proto.flow.execution.GetTransactionResultRequest.toObject(opt_includeInstance, this);
    };
    /**
     * Static version of the {@see toObject} method.
     * @param {boolean|undefined} includeInstance Deprecated. Whether to include
     *     the JSPB instance for transitional soy proto support:
     *     http://goto/soy-param-migration
     * @param {!proto.flow.execution.GetTransactionResultRequest} msg The msg instance to transform.
     * @return {!Object}
     * @suppress {unusedLocalVariables} f is only used for nested messages
     */
    proto.flow.execution.GetTransactionResultRequest.toObject = function (includeInstance, msg) {
        var f, obj = {
            blockId: msg.getBlockId_asB64(),
            transactionId: msg.getTransactionId_asB64()
        };
        if (includeInstance) {
            obj.$jspbMessageInstance = msg;
        }
        return obj;
    };
}
/**
 * Deserializes binary data (in protobuf wire format).
 * @param {jspb.ByteSource} bytes The bytes to deserialize.
 * @return {!proto.flow.execution.GetTransactionResultRequest}
 */
proto.flow.execution.GetTransactionResultRequest.deserializeBinary = function (bytes) {
    var reader = new jspb.BinaryReader(bytes);
    var msg = new proto.flow.execution.GetTransactionResultRequest;
    return proto.flow.execution.GetTransactionResultRequest.deserializeBinaryFromReader(msg, reader);
};
/**
 * Deserializes binary data (in protobuf wire format) from the
 * given reader into the given message object.
 * @param {!proto.flow.execution.GetTransactionResultRequest} msg The message object to deserialize into.
 * @param {!jspb.BinaryReader} reader The BinaryReader to use.
 * @return {!proto.flow.execution.GetTransactionResultRequest}
 */
proto.flow.execution.GetTransactionResultRequest.deserializeBinaryFromReader = function (msg, reader) {
    while (reader.nextField()) {
        if (reader.isEndGroup()) {
            break;
        }
        var field = reader.getFieldNumber();
        switch (field) {
            case 1:
                var value = /** @type {!Uint8Array} */ (reader.readBytes());
                msg.setBlockId(value);
                break;
            case 2:
                var value = /** @type {!Uint8Array} */ (reader.readBytes());
                msg.setTransactionId(value);
                break;
            default:
                reader.skipField();
                break;
        }
    }
    return msg;
};
/**
 * Serializes the message to binary data (in protobuf wire format).
 * @return {!Uint8Array}
 */
proto.flow.execution.GetTransactionResultRequest.prototype.serializeBinary = function () {
    var writer = new jspb.BinaryWriter();
    proto.flow.execution.GetTransactionResultRequest.serializeBinaryToWriter(this, writer);
    return writer.getResultBuffer();
};
/**
 * Serializes the given message to binary data (in protobuf wire
 * format), writing to the given BinaryWriter.
 * @param {!proto.flow.execution.GetTransactionResultRequest} message
 * @param {!jspb.BinaryWriter} writer
 * @suppress {unusedLocalVariables} f is only used for nested messages
 */
proto.flow.execution.GetTransactionResultRequest.serializeBinaryToWriter = function (message, writer) {
    var f = undefined;
    f = message.getBlockId_asU8();
    if (f.length > 0) {
        writer.writeBytes(1, f);
    }
    f = message.getTransactionId_asU8();
    if (f.length > 0) {
        writer.writeBytes(2, f);
    }
};
/**
 * optional bytes block_id = 1;
 * @return {!(string|Uint8Array)}
 */
proto.flow.execution.GetTransactionResultRequest.prototype.getBlockId = function () {
    return /** @type {!(string|Uint8Array)} */ (jspb.Message.getFieldWithDefault(this, 1, ""));
};
/**
 * optional bytes block_id = 1;
 * This is a type-conversion wrapper around `getBlockId()`
 * @return {string}
 */
proto.flow.execution.GetTransactionResultRequest.prototype.getBlockId_asB64 = function () {
    return /** @type {string} */ (jspb.Message.bytesAsB64(this.getBlockId()));
};
/**
 * optional bytes block_id = 1;
 * Note that Uint8Array is not supported on all browsers.
 * @see http://caniuse.com/Uint8Array
 * This is a type-conversion wrapper around `getBlockId()`
 * @return {!Uint8Array}
 */
proto.flow.execution.GetTransactionResultRequest.prototype.getBlockId_asU8 = function () {
    return /** @type {!Uint8Array} */ (jspb.Message.bytesAsU8(this.getBlockId()));
};
/**
 * @param {!(string|Uint8Array)} value
 * @return {!proto.flow.execution.GetTransactionResultRequest} returns this
 */
proto.flow.execution.GetTransactionResultRequest.prototype.setBlockId = function (value) {
    return jspb.Message.setProto3BytesField(this, 1, value);
};
/**
 * optional bytes transaction_id = 2;
 * @return {!(string|Uint8Array)}
 */
proto.flow.execution.GetTransactionResultRequest.prototype.getTransactionId = function () {
    return /** @type {!(string|Uint8Array)} */ (jspb.Message.getFieldWithDefault(this, 2, ""));
};
/**
 * optional bytes transaction_id = 2;
 * This is a type-conversion wrapper around `getTransactionId()`
 * @return {string}
 */
proto.flow.execution.GetTransactionResultRequest.prototype.getTransactionId_asB64 = function () {
    return /** @type {string} */ (jspb.Message.bytesAsB64(this.getTransactionId()));
};
/**
 * optional bytes transaction_id = 2;
 * Note that Uint8Array is not supported on all browsers.
 * @see http://caniuse.com/Uint8Array
 * This is a type-conversion wrapper around `getTransactionId()`
 * @return {!Uint8Array}
 */
proto.flow.execution.GetTransactionResultRequest.prototype.getTransactionId_asU8 = function () {
    return /** @type {!Uint8Array} */ (jspb.Message.bytesAsU8(this.getTransactionId()));
};
/**
 * @param {!(string|Uint8Array)} value
 * @return {!proto.flow.execution.GetTransactionResultRequest} returns this
 */
proto.flow.execution.GetTransactionResultRequest.prototype.setTransactionId = function (value) {
    return jspb.Message.setProto3BytesField(this, 2, value);
};
if (jspb.Message.GENERATE_TO_OBJECT) {
    /**
     * Creates an object representation of this proto.
     * Field names that are reserved in JavaScript and will be renamed to pb_name.
     * Optional fields that are not set will be set to undefined.
     * To access a reserved field use, foo.pb_<name>, eg, foo.pb_default.
     * For the list of reserved names please see:
     *     net/proto2/compiler/js/internal/generator.cc#kKeyword.
     * @param {boolean=} opt_includeInstance Deprecated. whether to include the
     *     JSPB instance for transitional soy proto support:
     *     http://goto/soy-param-migration
     * @return {!Object}
     */
    proto.flow.execution.GetTransactionByIndexRequest.prototype.toObject = function (opt_includeInstance) {
        return proto.flow.execution.GetTransactionByIndexRequest.toObject(opt_includeInstance, this);
    };
    /**
     * Static version of the {@see toObject} method.
     * @param {boolean|undefined} includeInstance Deprecated. Whether to include
     *     the JSPB instance for transitional soy proto support:
     *     http://goto/soy-param-migration
     * @param {!proto.flow.execution.GetTransactionByIndexRequest} msg The msg instance to transform.
     * @return {!Object}
     * @suppress {unusedLocalVariables} f is only used for nested messages
     */
    proto.flow.execution.GetTransactionByIndexRequest.toObject = function (includeInstance, msg) {
        var f, obj = {
            blockId: msg.getBlockId_asB64(),
            index: jspb.Message.getFieldWithDefault(msg, 2, 0)
        };
        if (includeInstance) {
            obj.$jspbMessageInstance = msg;
        }
        return obj;
    };
}
/**
 * Deserializes binary data (in protobuf wire format).
 * @param {jspb.ByteSource} bytes The bytes to deserialize.
 * @return {!proto.flow.execution.GetTransactionByIndexRequest}
 */
proto.flow.execution.GetTransactionByIndexRequest.deserializeBinary = function (bytes) {
    var reader = new jspb.BinaryReader(bytes);
    var msg = new proto.flow.execution.GetTransactionByIndexRequest;
    return proto.flow.execution.GetTransactionByIndexRequest.deserializeBinaryFromReader(msg, reader);
};
/**
 * Deserializes binary data (in protobuf wire format) from the
 * given reader into the given message object.
 * @param {!proto.flow.execution.GetTransactionByIndexRequest} msg The message object to deserialize into.
 * @param {!jspb.BinaryReader} reader The BinaryReader to use.
 * @return {!proto.flow.execution.GetTransactionByIndexRequest}
 */
proto.flow.execution.GetTransactionByIndexRequest.deserializeBinaryFromReader = function (msg, reader) {
    while (reader.nextField()) {
        if (reader.isEndGroup()) {
            break;
        }
        var field = reader.getFieldNumber();
        switch (field) {
            case 1:
                var value = /** @type {!Uint8Array} */ (reader.readBytes());
                msg.setBlockId(value);
                break;
            case 2:
                var value = /** @type {number} */ (reader.readUint32());
                msg.setIndex(value);
                break;
            default:
                reader.skipField();
                break;
        }
    }
    return msg;
};
/**
 * Serializes the message to binary data (in protobuf wire format).
 * @return {!Uint8Array}
 */
proto.flow.execution.GetTransactionByIndexRequest.prototype.serializeBinary = function () {
    var writer = new jspb.BinaryWriter();
    proto.flow.execution.GetTransactionByIndexRequest.serializeBinaryToWriter(this, writer);
    return writer.getResultBuffer();
};
/**
 * Serializes the given message to binary data (in protobuf wire
 * format), writing to the given BinaryWriter.
 * @param {!proto.flow.execution.GetTransactionByIndexRequest} message
 * @param {!jspb.BinaryWriter} writer
 * @suppress {unusedLocalVariables} f is only used for nested messages
 */
proto.flow.execution.GetTransactionByIndexRequest.serializeBinaryToWriter = function (message, writer) {
    var f = undefined;
    f = message.getBlockId_asU8();
    if (f.length > 0) {
        writer.writeBytes(1, f);
    }
    f = message.getIndex();
    if (f !== 0) {
        writer.writeUint32(2, f);
    }
};
/**
 * optional bytes block_id = 1;
 * @return {!(string|Uint8Array)}
 */
proto.flow.execution.GetTransactionByIndexRequest.prototype.getBlockId = function () {
    return /** @type {!(string|Uint8Array)} */ (jspb.Message.getFieldWithDefault(this, 1, ""));
};
/**
 * optional bytes block_id = 1;
 * This is a type-conversion wrapper around `getBlockId()`
 * @return {string}
 */
proto.flow.execution.GetTransactionByIndexRequest.prototype.getBlockId_asB64 = function () {
    return /** @type {string} */ (jspb.Message.bytesAsB64(this.getBlockId()));
};
/**
 * optional bytes block_id = 1;
 * Note that Uint8Array is not supported on all browsers.
 * @see http://caniuse.com/Uint8Array
 * This is a type-conversion wrapper around `getBlockId()`
 * @return {!Uint8Array}
 */
proto.flow.execution.GetTransactionByIndexRequest.prototype.getBlockId_asU8 = function () {
    return /** @type {!Uint8Array} */ (jspb.Message.bytesAsU8(this.getBlockId()));
};
/**
 * @param {!(string|Uint8Array)} value
 * @return {!proto.flow.execution.GetTransactionByIndexRequest} returns this
 */
proto.flow.execution.GetTransactionByIndexRequest.prototype.setBlockId = function (value) {
    return jspb.Message.setProto3BytesField(this, 1, value);
};
/**
 * optional uint32 index = 2;
 * @return {number}
 */
proto.flow.execution.GetTransactionByIndexRequest.prototype.getIndex = function () {
    return /** @type {number} */ (jspb.Message.getFieldWithDefault(this, 2, 0));
};
/**
 * @param {number} value
 * @return {!proto.flow.execution.GetTransactionByIndexRequest} returns this
 */
proto.flow.execution.GetTransactionByIndexRequest.prototype.setIndex = function (value) {
    return jspb.Message.setProto3IntField(this, 2, value);
};
/**
 * List of repeated fields within this message type.
 * @private {!Array<number>}
 * @const
 */
proto.flow.execution.GetTransactionResultResponse.repeatedFields_ = [3];
if (jspb.Message.GENERATE_TO_OBJECT) {
    /**
     * Creates an object representation of this proto.
     * Field names that are reserved in JavaScript and will be renamed to pb_name.
     * Optional fields that are not set will be set to undefined.
     * To access a reserved field use, foo.pb_<name>, eg, foo.pb_default.
     * For the list of reserved names please see:
     *     net/proto2/compiler/js/internal/generator.cc#kKeyword.
     * @param {boolean=} opt_includeInstance Deprecated. whether to include the
     *     JSPB instance for transitional soy proto support:
     *     http://goto/soy-param-migration
     * @return {!Object}
     */
    proto.flow.execution.GetTransactionResultResponse.prototype.toObject = function (opt_includeInstance) {
        return proto.flow.execution.GetTransactionResultResponse.toObject(opt_includeInstance, this);
    };
    /**
     * Static version of the {@see toObject} method.
     * @param {boolean|undefined} includeInstance Deprecated. Whether to include
     *     the JSPB instance for transitional soy proto support:
     *     http://goto/soy-param-migration
     * @param {!proto.flow.execution.GetTransactionResultResponse} msg The msg instance to transform.
     * @return {!Object}
     * @suppress {unusedLocalVariables} f is only used for nested messages
     */
    proto.flow.execution.GetTransactionResultResponse.toObject = function (includeInstance, msg) {
        var f, obj = {
            statusCode: jspb.Message.getFieldWithDefault(msg, 1, 0),
            errorMessage: jspb.Message.getFieldWithDefault(msg, 2, ""),
            eventsList: jspb.Message.toObjectList(msg.getEventsList(), flow_entities_event_pb.Event.toObject, includeInstance),
            eventEncodingVersion: jspb.Message.getFieldWithDefault(msg, 4, 0),
            computationUsage: jspb.Message.getFieldWithDefault(msg, 5, 0)
        };
        if (includeInstance) {
            obj.$jspbMessageInstance = msg;
        }
        return obj;
    };
}
/**
 * Deserializes binary data (in protobuf wire format).
 * @param {jspb.ByteSource} bytes The bytes to deserialize.
 * @return {!proto.flow.execution.GetTransactionResultResponse}
 */
proto.flow.execution.GetTransactionResultResponse.deserializeBinary = function (bytes) {
    var reader = new jspb.BinaryReader(bytes);
    var msg = new proto.flow.execution.GetTransactionResultResponse;
    return proto.flow.execution.GetTransactionResultResponse.deserializeBinaryFromReader(msg, reader);
};
/**
 * Deserializes binary data (in protobuf wire format) from the
 * given reader into the given message object.
 * @param {!proto.flow.execution.GetTransactionResultResponse} msg The message object to deserialize into.
 * @param {!jspb.BinaryReader} reader The BinaryReader to use.
 * @return {!proto.flow.execution.GetTransactionResultResponse}
 */
proto.flow.execution.GetTransactionResultResponse.deserializeBinaryFromReader = function (msg, reader) {
    while (reader.nextField()) {
        if (reader.isEndGroup()) {
            break;
        }
        var field = reader.getFieldNumber();
        switch (field) {
            case 1:
                var value = /** @type {number} */ (reader.readUint32());
                msg.setStatusCode(value);
                break;
            case 2:
                var value = /** @type {string} */ (reader.readString());
                msg.setErrorMessage(value);
                break;
            case 3:
                var value = new flow_entities_event_pb.Event;
                reader.readMessage(value, flow_entities_event_pb.Event.deserializeBinaryFromReader);
                msg.addEvents(value);
                break;
            case 4:
                var value = /** @type {!proto.flow.entities.EventEncodingVersion} */ (reader.readEnum());
                msg.setEventEncodingVersion(value);
                break;
            case 5:
                var value = /** @type {number} */ (reader.readUint64());
                msg.setComputationUsage(value);
                break;
            default:
                reader.skipField();
                break;
        }
    }
    return msg;
};
/**
 * Serializes the message to binary data (in protobuf wire format).
 * @return {!Uint8Array}
 */
proto.flow.execution.GetTransactionResultResponse.prototype.serializeBinary = function () {
    var writer = new jspb.BinaryWriter();
    proto.flow.execution.GetTransactionResultResponse.serializeBinaryToWriter(this, writer);
    return writer.getResultBuffer();
};
/**
 * Serializes the given message to binary data (in protobuf wire
 * format), writing to the given BinaryWriter.
 * @param {!proto.flow.execution.GetTransactionResultResponse} message
 * @param {!jspb.BinaryWriter} writer
 * @suppress {unusedLocalVariables} f is only used for nested messages
 */
proto.flow.execution.GetTransactionResultResponse.serializeBinaryToWriter = function (message, writer) {
    var f = undefined;
    f = message.getStatusCode();
    if (f !== 0) {
        writer.writeUint32(1, f);
    }
    f = message.getErrorMessage();
    if (f.length > 0) {
        writer.writeString(2, f);
    }
    f = message.getEventsList();
    if (f.length > 0) {
        writer.writeRepeatedMessage(3, f, flow_entities_event_pb.Event.serializeBinaryToWriter);
    }
    f = message.getEventEncodingVersion();
    if (f !== 0.0) {
        writer.writeEnum(4, f);
    }
    f = message.getComputationUsage();
    if (f !== 0) {
        writer.writeUint64(5, f);
    }
};
/**
 * optional uint32 status_code = 1;
 * @return {number}
 */
proto.flow.execution.GetTransactionResultResponse.prototype.getStatusCode = function () {
    return /** @type {number} */ (jspb.Message.getFieldWithDefault(this, 1, 0));
};
/**
 * @param {number} value
 * @return {!proto.flow.execution.GetTransactionResultResponse} returns this
 */
proto.flow.execution.GetTransactionResultResponse.prototype.setStatusCode = function (value) {
    return jspb.Message.setProto3IntField(this, 1, value);
};
/**
 * optional string error_message = 2;
 * @return {string}
 */
proto.flow.execution.GetTransactionResultResponse.prototype.getErrorMessage = function () {
    return /** @type {string} */ (jspb.Message.getFieldWithDefault(this, 2, ""));
};
/**
 * @param {string} value
 * @return {!proto.flow.execution.GetTransactionResultResponse} returns this
 */
proto.flow.execution.GetTransactionResultResponse.prototype.setErrorMessage = function (value) {
    return jspb.Message.setProto3StringField(this, 2, value);
};
/**
 * repeated flow.entities.Event events = 3;
 * @return {!Array<!proto.flow.entities.Event>}
 */
proto.flow.execution.GetTransactionResultResponse.prototype.getEventsList = function () {
    return /** @type{!Array<!proto.flow.entities.Event>} */ (jspb.Message.getRepeatedWrapperField(this, flow_entities_event_pb.Event, 3));
};
/**
 * @param {!Array<!proto.flow.entities.Event>} value
 * @return {!proto.flow.execution.GetTransactionResultResponse} returns this
*/
proto.flow.execution.GetTransactionResultResponse.prototype.setEventsList = function (value) {
    return jspb.Message.setRepeatedWrapperField(this, 3, value);
};
/**
 * @param {!proto.flow.entities.Event=} opt_value
 * @param {number=} opt_index
 * @return {!proto.flow.entities.Event}
 */
proto.flow.execution.GetTransactionResultResponse.prototype.addEvents = function (opt_value, opt_index) {
    return jspb.Message.addToRepeatedWrapperField(this, 3, opt_value, proto.flow.entities.Event, opt_index);
};
/**
 * Clears the list making it empty but non-null.
 * @return {!proto.flow.execution.GetTransactionResultResponse} returns this
 */
proto.flow.execution.GetTransactionResultResponse.prototype.clearEventsList = function () {
    return this.setEventsList([]);
};
/**
 * optional flow.entities.EventEncodingVersion event_encoding_version = 4;
 * @return {!proto.flow.entities.EventEncodingVersion}
 */
proto.flow.execution.GetTransactionResultResponse.prototype.getEventEncodingVersion = function () {
    return /** @type {!proto.flow.entities.EventEncodingVersion} */ (jspb.Message.getFieldWithDefault(this, 4, 0));
};
/**
 * @param {!proto.flow.entities.EventEncodingVersion} value
 * @return {!proto.flow.execution.GetTransactionResultResponse} returns this
 */
proto.flow.execution.GetTransactionResultResponse.prototype.setEventEncodingVersion = function (value) {
    return jspb.Message.setProto3EnumField(this, 4, value);
};
/**
 * optional uint64 computation_usage = 5;
 * @return {number}
 */
proto.flow.execution.GetTransactionResultResponse.prototype.getComputationUsage = function () {
    return /** @type {number} */ (jspb.Message.getFieldWithDefault(this, 5, 0));
};
/**
 * @param {number} value
 * @return {!proto.flow.execution.GetTransactionResultResponse} returns this
 */
proto.flow.execution.GetTransactionResultResponse.prototype.setComputationUsage = function (value) {
    return jspb.Message.setProto3IntField(this, 5, value);
};
if (jspb.Message.GENERATE_TO_OBJECT) {
    /**
     * Creates an object representation of this proto.
     * Field names that are reserved in JavaScript and will be renamed to pb_name.
     * Optional fields that are not set will be set to undefined.
     * To access a reserved field use, foo.pb_<name>, eg, foo.pb_default.
     * For the list of reserved names please see:
     *     net/proto2/compiler/js/internal/generator.cc#kKeyword.
     * @param {boolean=} opt_includeInstance Deprecated. whether to include the
     *     JSPB instance for transitional soy proto support:
     *     http://goto/soy-param-migration
     * @return {!Object}
     */
    proto.flow.execution.GetTransactionsByBlockIDRequest.prototype.toObject = function (opt_includeInstance) {
        return proto.flow.execution.GetTransactionsByBlockIDRequest.toObject(opt_includeInstance, this);
    };
    /**
     * Static version of the {@see toObject} method.
     * @param {boolean|undefined} includeInstance Deprecated. Whether to include
     *     the JSPB instance for transitional soy proto support:
     *     http://goto/soy-param-migration
     * @param {!proto.flow.execution.GetTransactionsByBlockIDRequest} msg The msg instance to transform.
     * @return {!Object}
     * @suppress {unusedLocalVariables} f is only used for nested messages
     */
    proto.flow.execution.GetTransactionsByBlockIDRequest.toObject = function (includeInstance, msg) {
        var f, obj = {
            blockId: msg.getBlockId_asB64()
        };
        if (includeInstance) {
            obj.$jspbMessageInstance = msg;
        }
        return obj;
    };
}
/**
 * Deserializes binary data (in protobuf wire format).
 * @param {jspb.ByteSource} bytes The bytes to deserialize.
 * @return {!proto.flow.execution.GetTransactionsByBlockIDRequest}
 */
proto.flow.execution.GetTransactionsByBlockIDRequest.deserializeBinary = function (bytes) {
    var reader = new jspb.BinaryReader(bytes);
    var msg = new proto.flow.execution.GetTransactionsByBlockIDRequest;
    return proto.flow.execution.GetTransactionsByBlockIDRequest.deserializeBinaryFromReader(msg, reader);
};
/**
 * Deserializes binary data (in protobuf wire format) from the
 * given reader into the given message object.
 * @param {!proto.flow.execution.GetTransactionsByBlockIDRequest} msg The message object to deserialize into.
 * @param {!jspb.BinaryReader} reader The BinaryReader to use.
 * @return {!proto.flow.execution.GetTransactionsByBlockIDRequest}
 */
proto.flow.execution.GetTransactionsByBlockIDRequest.deserializeBinaryFromReader = function (msg, reader) {
    while (reader.nextField()) {
        if (reader.isEndGroup()) {
            break;
        }
        var field = reader.getFieldNumber();
        switch (field) {
            case 1:
                var value = /** @type {!Uint8Array} */ (reader.readBytes());
                msg.setBlockId(value);
                break;
            default:
                reader.skipField();
                break;
        }
    }
    return msg;
};
/**
 * Serializes the message to binary data (in protobuf wire format).
 * @return {!Uint8Array}
 */
proto.flow.execution.GetTransactionsByBlockIDRequest.prototype.serializeBinary = function () {
    var writer = new jspb.BinaryWriter();
    proto.flow.execution.GetTransactionsByBlockIDRequest.serializeBinaryToWriter(this, writer);
    return writer.getResultBuffer();
};
/**
 * Serializes the given message to binary data (in protobuf wire
 * format), writing to the given BinaryWriter.
 * @param {!proto.flow.execution.GetTransactionsByBlockIDRequest} message
 * @param {!jspb.BinaryWriter} writer
 * @suppress {unusedLocalVariables} f is only used for nested messages
 */
proto.flow.execution.GetTransactionsByBlockIDRequest.serializeBinaryToWriter = function (message, writer) {
    var f = undefined;
    f = message.getBlockId_asU8();
    if (f.length > 0) {
        writer.writeBytes(1, f);
    }
};
/**
 * optional bytes block_id = 1;
 * @return {!(string|Uint8Array)}
 */
proto.flow.execution.GetTransactionsByBlockIDRequest.prototype.getBlockId = function () {
    return /** @type {!(string|Uint8Array)} */ (jspb.Message.getFieldWithDefault(this, 1, ""));
};
/**
 * optional bytes block_id = 1;
 * This is a type-conversion wrapper around `getBlockId()`
 * @return {string}
 */
proto.flow.execution.GetTransactionsByBlockIDRequest.prototype.getBlockId_asB64 = function () {
    return /** @type {string} */ (jspb.Message.bytesAsB64(this.getBlockId()));
};
/**
 * optional bytes block_id = 1;
 * Note that Uint8Array is not supported on all browsers.
 * @see http://caniuse.com/Uint8Array
 * This is a type-conversion wrapper around `getBlockId()`
 * @return {!Uint8Array}
 */
proto.flow.execution.GetTransactionsByBlockIDRequest.prototype.getBlockId_asU8 = function () {
    return /** @type {!Uint8Array} */ (jspb.Message.bytesAsU8(this.getBlockId()));
};
/**
 * @param {!(string|Uint8Array)} value
 * @return {!proto.flow.execution.GetTransactionsByBlockIDRequest} returns this
 */
proto.flow.execution.GetTransactionsByBlockIDRequest.prototype.setBlockId = function (value) {
    return jspb.Message.setProto3BytesField(this, 1, value);
};
/**
 * List of repeated fields within this message type.
 * @private {!Array<number>}
 * @const
 */
proto.flow.execution.GetTransactionResultsResponse.repeatedFields_ = [1];
if (jspb.Message.GENERATE_TO_OBJECT) {
    /**
     * Creates an object representation of this proto.
     * Field names that are reserved in JavaScript and will be renamed to pb_name.
     * Optional fields that are not set will be set to undefined.
     * To access a reserved field use, foo.pb_<name>, eg, foo.pb_default.
     * For the list of reserved names please see:
     *     net/proto2/compiler/js/internal/generator.cc#kKeyword.
     * @param {boolean=} opt_includeInstance Deprecated. whether to include the
     *     JSPB instance for transitional soy proto support:
     *     http://goto/soy-param-migration
     * @return {!Object}
     */
    proto.flow.execution.GetTransactionResultsResponse.prototype.toObject = function (opt_includeInstance) {
        return proto.flow.execution.GetTransactionResultsResponse.toObject(opt_includeInstance, this);
    };
    /**
     * Static version of the {@see toObject} method.
     * @param {boolean|undefined} includeInstance Deprecated. Whether to include
     *     the JSPB instance for transitional soy proto support:
     *     http://goto/soy-param-migration
     * @param {!proto.flow.execution.GetTransactionResultsResponse} msg The msg instance to transform.
     * @return {!Object}
     * @suppress {unusedLocalVariables} f is only used for nested messages
     */
    proto.flow.execution.GetTransactionResultsResponse.toObject = function (includeInstance, msg) {
        var f, obj = {
            transactionResultsList: jspb.Message.toObjectList(msg.getTransactionResultsList(), proto.flow.execution.GetTransactionResultResponse.toObject, includeInstance),
            eventEncodingVersion: jspb.Message.getFieldWithDefault(msg, 2, 0)
        };
        if (includeInstance) {
            obj.$jspbMessageInstance = msg;
        }
        return obj;
    };
}
/**
 * Deserializes binary data (in protobuf wire format).
 * @param {jspb.ByteSource} bytes The bytes to deserialize.
 * @return {!proto.flow.execution.GetTransactionResultsResponse}
 */
proto.flow.execution.GetTransactionResultsResponse.deserializeBinary = function (bytes) {
    var reader = new jspb.BinaryReader(bytes);
    var msg = new proto.flow.execution.GetTransactionResultsResponse;
    return proto.flow.execution.GetTransactionResultsResponse.deserializeBinaryFromReader(msg, reader);
};
/**
 * Deserializes binary data (in protobuf wire format) from the
 * given reader into the given message object.
 * @param {!proto.flow.execution.GetTransactionResultsResponse} msg The message object to deserialize into.
 * @param {!jspb.BinaryReader} reader The BinaryReader to use.
 * @return {!proto.flow.execution.GetTransactionResultsResponse}
 */
proto.flow.execution.GetTransactionResultsResponse.deserializeBinaryFromReader = function (msg, reader) {
    while (reader.nextField()) {
        if (reader.isEndGroup()) {
            break;
        }
        var field = reader.getFieldNumber();
        switch (field) {
            case 1:
                var value = new proto.flow.execution.GetTransactionResultResponse;
                reader.readMessage(value, proto.flow.execution.GetTransactionResultResponse.deserializeBinaryFromReader);
                msg.addTransactionResults(value);
                break;
            case 2:
                var value = /** @type {!proto.flow.entities.EventEncodingVersion} */ (reader.readEnum());
                msg.setEventEncodingVersion(value);
                break;
            default:
                reader.skipField();
                break;
        }
    }
    return msg;
};
/**
 * Serializes the message to binary data (in protobuf wire format).
 * @return {!Uint8Array}
 */
proto.flow.execution.GetTransactionResultsResponse.prototype.serializeBinary = function () {
    var writer = new jspb.BinaryWriter();
    proto.flow.execution.GetTransactionResultsResponse.serializeBinaryToWriter(this, writer);
    return writer.getResultBuffer();
};
/**
 * Serializes the given message to binary data (in protobuf wire
 * format), writing to the given BinaryWriter.
 * @param {!proto.flow.execution.GetTransactionResultsResponse} message
 * @param {!jspb.BinaryWriter} writer
 * @suppress {unusedLocalVariables} f is only used for nested messages
 */
proto.flow.execution.GetTransactionResultsResponse.serializeBinaryToWriter = function (message, writer) {
    var f = undefined;
    f = message.getTransactionResultsList();
    if (f.length > 0) {
        writer.writeRepeatedMessage(1, f, proto.flow.execution.GetTransactionResultResponse.serializeBinaryToWriter);
    }
    f = message.getEventEncodingVersion();
    if (f !== 0.0) {
        writer.writeEnum(2, f);
    }
};
/**
 * repeated GetTransactionResultResponse transaction_results = 1;
 * @return {!Array<!proto.flow.execution.GetTransactionResultResponse>}
 */
proto.flow.execution.GetTransactionResultsResponse.prototype.getTransactionResultsList = function () {
    return /** @type{!Array<!proto.flow.execution.GetTransactionResultResponse>} */ (jspb.Message.getRepeatedWrapperField(this, proto.flow.execution.GetTransactionResultResponse, 1));
};
/**
 * @param {!Array<!proto.flow.execution.GetTransactionResultResponse>} value
 * @return {!proto.flow.execution.GetTransactionResultsResponse} returns this
*/
proto.flow.execution.GetTransactionResultsResponse.prototype.setTransactionResultsList = function (value) {
    return jspb.Message.setRepeatedWrapperField(this, 1, value);
};
/**
 * @param {!proto.flow.execution.GetTransactionResultResponse=} opt_value
 * @param {number=} opt_index
 * @return {!proto.flow.execution.GetTransactionResultResponse}
 */
proto.flow.execution.GetTransactionResultsResponse.prototype.addTransactionResults = function (opt_value, opt_index) {
    return jspb.Message.addToRepeatedWrapperField(this, 1, opt_value, proto.flow.execution.GetTransactionResultResponse, opt_index);
};
/**
 * Clears the list making it empty but non-null.
 * @return {!proto.flow.execution.GetTransactionResultsResponse} returns this
 */
proto.flow.execution.GetTransactionResultsResponse.prototype.clearTransactionResultsList = function () {
    return this.setTransactionResultsList([]);
};
/**
 * optional flow.entities.EventEncodingVersion event_encoding_version = 2;
 * @return {!proto.flow.entities.EventEncodingVersion}
 */
proto.flow.execution.GetTransactionResultsResponse.prototype.getEventEncodingVersion = function () {
    return /** @type {!proto.flow.entities.EventEncodingVersion} */ (jspb.Message.getFieldWithDefault(this, 2, 0));
};
/**
 * @param {!proto.flow.entities.EventEncodingVersion} value
 * @return {!proto.flow.execution.GetTransactionResultsResponse} returns this
 */
proto.flow.execution.GetTransactionResultsResponse.prototype.setEventEncodingVersion = function (value) {
    return jspb.Message.setProto3EnumField(this, 2, value);
};
if (jspb.Message.GENERATE_TO_OBJECT) {
    /**
     * Creates an object representation of this proto.
     * Field names that are reserved in JavaScript and will be renamed to pb_name.
     * Optional fields that are not set will be set to undefined.
     * To access a reserved field use, foo.pb_<name>, eg, foo.pb_default.
     * For the list of reserved names please see:
     *     net/proto2/compiler/js/internal/generator.cc#kKeyword.
     * @param {boolean=} opt_includeInstance Deprecated. whether to include the
     *     JSPB instance for transitional soy proto support:
     *     http://goto/soy-param-migration
     * @return {!Object}
     */
    proto.flow.execution.GetTransactionErrorMessageRequest.prototype.toObject = function (opt_includeInstance) {
        return proto.flow.execution.GetTransactionErrorMessageRequest.toObject(opt_includeInstance, this);
    };
    /**
     * Static version of the {@see toObject} method.
     * @param {boolean|undefined} includeInstance Deprecated. Whether to include
     *     the JSPB instance for transitional soy proto support:
     *     http://goto/soy-param-migration
     * @param {!proto.flow.execution.GetTransactionErrorMessageRequest} msg The msg instance to transform.
     * @return {!Object}
     * @suppress {unusedLocalVariables} f is only used for nested messages
     */
    proto.flow.execution.GetTransactionErrorMessageRequest.toObject = function (includeInstance, msg) {
        var f, obj = {
            blockId: msg.getBlockId_asB64(),
            transactionId: msg.getTransactionId_asB64()
        };
        if (includeInstance) {
            obj.$jspbMessageInstance = msg;
        }
        return obj;
    };
}
/**
 * Deserializes binary data (in protobuf wire format).
 * @param {jspb.ByteSource} bytes The bytes to deserialize.
 * @return {!proto.flow.execution.GetTransactionErrorMessageRequest}
 */
proto.flow.execution.GetTransactionErrorMessageRequest.deserializeBinary = function (bytes) {
    var reader = new jspb.BinaryReader(bytes);
    var msg = new proto.flow.execution.GetTransactionErrorMessageRequest;
    return proto.flow.execution.GetTransactionErrorMessageRequest.deserializeBinaryFromReader(msg, reader);
};
/**
 * Deserializes binary data (in protobuf wire format) from the
 * given reader into the given message object.
 * @param {!proto.flow.execution.GetTransactionErrorMessageRequest} msg The message object to deserialize into.
 * @param {!jspb.BinaryReader} reader The BinaryReader to use.
 * @return {!proto.flow.execution.GetTransactionErrorMessageRequest}
 */
proto.flow.execution.GetTransactionErrorMessageRequest.deserializeBinaryFromReader = function (msg, reader) {
    while (reader.nextField()) {
        if (reader.isEndGroup()) {
            break;
        }
        var field = reader.getFieldNumber();
        switch (field) {
            case 1:
                var value = /** @type {!Uint8Array} */ (reader.readBytes());
                msg.setBlockId(value);
                break;
            case 2:
                var value = /** @type {!Uint8Array} */ (reader.readBytes());
                msg.setTransactionId(value);
                break;
            default:
                reader.skipField();
                break;
        }
    }
    return msg;
};
/**
 * Serializes the message to binary data (in protobuf wire format).
 * @return {!Uint8Array}
 */
proto.flow.execution.GetTransactionErrorMessageRequest.prototype.serializeBinary = function () {
    var writer = new jspb.BinaryWriter();
    proto.flow.execution.GetTransactionErrorMessageRequest.serializeBinaryToWriter(this, writer);
    return writer.getResultBuffer();
};
/**
 * Serializes the given message to binary data (in protobuf wire
 * format), writing to the given BinaryWriter.
 * @param {!proto.flow.execution.GetTransactionErrorMessageRequest} message
 * @param {!jspb.BinaryWriter} writer
 * @suppress {unusedLocalVariables} f is only used for nested messages
 */
proto.flow.execution.GetTransactionErrorMessageRequest.serializeBinaryToWriter = function (message, writer) {
    var f = undefined;
    f = message.getBlockId_asU8();
    if (f.length > 0) {
        writer.writeBytes(1, f);
    }
    f = message.getTransactionId_asU8();
    if (f.length > 0) {
        writer.writeBytes(2, f);
    }
};
/**
 * optional bytes block_id = 1;
 * @return {!(string|Uint8Array)}
 */
proto.flow.execution.GetTransactionErrorMessageRequest.prototype.getBlockId = function () {
    return /** @type {!(string|Uint8Array)} */ (jspb.Message.getFieldWithDefault(this, 1, ""));
};
/**
 * optional bytes block_id = 1;
 * This is a type-conversion wrapper around `getBlockId()`
 * @return {string}
 */
proto.flow.execution.GetTransactionErrorMessageRequest.prototype.getBlockId_asB64 = function () {
    return /** @type {string} */ (jspb.Message.bytesAsB64(this.getBlockId()));
};
/**
 * optional bytes block_id = 1;
 * Note that Uint8Array is not supported on all browsers.
 * @see http://caniuse.com/Uint8Array
 * This is a type-conversion wrapper around `getBlockId()`
 * @return {!Uint8Array}
 */
proto.flow.execution.GetTransactionErrorMessageRequest.prototype.getBlockId_asU8 = function () {
    return /** @type {!Uint8Array} */ (jspb.Message.bytesAsU8(this.getBlockId()));
};
/**
 * @param {!(string|Uint8Array)} value
 * @return {!proto.flow.execution.GetTransactionErrorMessageRequest} returns this
 */
proto.flow.execution.GetTransactionErrorMessageRequest.prototype.setBlockId = function (value) {
    return jspb.Message.setProto3BytesField(this, 1, value);
};
/**
 * optional bytes transaction_id = 2;
 * @return {!(string|Uint8Array)}
 */
proto.flow.execution.GetTransactionErrorMessageRequest.prototype.getTransactionId = function () {
    return /** @type {!(string|Uint8Array)} */ (jspb.Message.getFieldWithDefault(this, 2, ""));
};
/**
 * optional bytes transaction_id = 2;
 * This is a type-conversion wrapper around `getTransactionId()`
 * @return {string}
 */
proto.flow.execution.GetTransactionErrorMessageRequest.prototype.getTransactionId_asB64 = function () {
    return /** @type {string} */ (jspb.Message.bytesAsB64(this.getTransactionId()));
};
/**
 * optional bytes transaction_id = 2;
 * Note that Uint8Array is not supported on all browsers.
 * @see http://caniuse.com/Uint8Array
 * This is a type-conversion wrapper around `getTransactionId()`
 * @return {!Uint8Array}
 */
proto.flow.execution.GetTransactionErrorMessageRequest.prototype.getTransactionId_asU8 = function () {
    return /** @type {!Uint8Array} */ (jspb.Message.bytesAsU8(this.getTransactionId()));
};
/**
 * @param {!(string|Uint8Array)} value
 * @return {!proto.flow.execution.GetTransactionErrorMessageRequest} returns this
 */
proto.flow.execution.GetTransactionErrorMessageRequest.prototype.setTransactionId = function (value) {
    return jspb.Message.setProto3BytesField(this, 2, value);
};
if (jspb.Message.GENERATE_TO_OBJECT) {
    /**
     * Creates an object representation of this proto.
     * Field names that are reserved in JavaScript and will be renamed to pb_name.
     * Optional fields that are not set will be set to undefined.
     * To access a reserved field use, foo.pb_<name>, eg, foo.pb_default.
     * For the list of reserved names please see:
     *     net/proto2/compiler/js/internal/generator.cc#kKeyword.
     * @param {boolean=} opt_includeInstance Deprecated. whether to include the
     *     JSPB instance for transitional soy proto support:
     *     http://goto/soy-param-migration
     * @return {!Object}
     */
    proto.flow.execution.GetTransactionErrorMessageByIndexRequest.prototype.toObject = function (opt_includeInstance) {
        return proto.flow.execution.GetTransactionErrorMessageByIndexRequest.toObject(opt_includeInstance, this);
    };
    /**
     * Static version of the {@see toObject} method.
     * @param {boolean|undefined} includeInstance Deprecated. Whether to include
     *     the JSPB instance for transitional soy proto support:
     *     http://goto/soy-param-migration
     * @param {!proto.flow.execution.GetTransactionErrorMessageByIndexRequest} msg The msg instance to transform.
     * @return {!Object}
     * @suppress {unusedLocalVariables} f is only used for nested messages
     */
    proto.flow.execution.GetTransactionErrorMessageByIndexRequest.toObject = function (includeInstance, msg) {
        var f, obj = {
            blockId: msg.getBlockId_asB64(),
            index: jspb.Message.getFieldWithDefault(msg, 2, 0)
        };
        if (includeInstance) {
            obj.$jspbMessageInstance = msg;
        }
        return obj;
    };
}
/**
 * Deserializes binary data (in protobuf wire format).
 * @param {jspb.ByteSource} bytes The bytes to deserialize.
 * @return {!proto.flow.execution.GetTransactionErrorMessageByIndexRequest}
 */
proto.flow.execution.GetTransactionErrorMessageByIndexRequest.deserializeBinary = function (bytes) {
    var reader = new jspb.BinaryReader(bytes);
    var msg = new proto.flow.execution.GetTransactionErrorMessageByIndexRequest;
    return proto.flow.execution.GetTransactionErrorMessageByIndexRequest.deserializeBinaryFromReader(msg, reader);
};
/**
 * Deserializes binary data (in protobuf wire format) from the
 * given reader into the given message object.
 * @param {!proto.flow.execution.GetTransactionErrorMessageByIndexRequest} msg The message object to deserialize into.
 * @param {!jspb.BinaryReader} reader The BinaryReader to use.
 * @return {!proto.flow.execution.GetTransactionErrorMessageByIndexRequest}
 */
proto.flow.execution.GetTransactionErrorMessageByIndexRequest.deserializeBinaryFromReader = function (msg, reader) {
    while (reader.nextField()) {
        if (reader.isEndGroup()) {
            break;
        }
        var field = reader.getFieldNumber();
        switch (field) {
            case 1:
                var value = /** @type {!Uint8Array} */ (reader.readBytes());
                msg.setBlockId(value);
                break;
            case 2:
                var value = /** @type {number} */ (reader.readUint32());
                msg.setIndex(value);
                break;
            default:
                reader.skipField();
                break;
        }
    }
    return msg;
};
/**
 * Serializes the message to binary data (in protobuf wire format).
 * @return {!Uint8Array}
 */
proto.flow.execution.GetTransactionErrorMessageByIndexRequest.prototype.serializeBinary = function () {
    var writer = new jspb.BinaryWriter();
    proto.flow.execution.GetTransactionErrorMessageByIndexRequest.serializeBinaryToWriter(this, writer);
    return writer.getResultBuffer();
};
/**
 * Serializes the given message to binary data (in protobuf wire
 * format), writing to the given BinaryWriter.
 * @param {!proto.flow.execution.GetTransactionErrorMessageByIndexRequest} message
 * @param {!jspb.BinaryWriter} writer
 * @suppress {unusedLocalVariables} f is only used for nested messages
 */
proto.flow.execution.GetTransactionErrorMessageByIndexRequest.serializeBinaryToWriter = function (message, writer) {
    var f = undefined;
    f = message.getBlockId_asU8();
    if (f.length > 0) {
        writer.writeBytes(1, f);
    }
    f = message.getIndex();
    if (f !== 0) {
        writer.writeUint32(2, f);
    }
};
/**
 * optional bytes block_id = 1;
 * @return {!(string|Uint8Array)}
 */
proto.flow.execution.GetTransactionErrorMessageByIndexRequest.prototype.getBlockId = function () {
    return /** @type {!(string|Uint8Array)} */ (jspb.Message.getFieldWithDefault(this, 1, ""));
};
/**
 * optional bytes block_id = 1;
 * This is a type-conversion wrapper around `getBlockId()`
 * @return {string}
 */
proto.flow.execution.GetTransactionErrorMessageByIndexRequest.prototype.getBlockId_asB64 = function () {
    return /** @type {string} */ (jspb.Message.bytesAsB64(this.getBlockId()));
};
/**
 * optional bytes block_id = 1;
 * Note that Uint8Array is not supported on all browsers.
 * @see http://caniuse.com/Uint8Array
 * This is a type-conversion wrapper around `getBlockId()`
 * @return {!Uint8Array}
 */
proto.flow.execution.GetTransactionErrorMessageByIndexRequest.prototype.getBlockId_asU8 = function () {
    return /** @type {!Uint8Array} */ (jspb.Message.bytesAsU8(this.getBlockId()));
};
/**
 * @param {!(string|Uint8Array)} value
 * @return {!proto.flow.execution.GetTransactionErrorMessageByIndexRequest} returns this
 */
proto.flow.execution.GetTransactionErrorMessageByIndexRequest.prototype.setBlockId = function (value) {
    return jspb.Message.setProto3BytesField(this, 1, value);
};
/**
 * optional uint32 index = 2;
 * @return {number}
 */
proto.flow.execution.GetTransactionErrorMessageByIndexRequest.prototype.getIndex = function () {
    return /** @type {number} */ (jspb.Message.getFieldWithDefault(this, 2, 0));
};
/**
 * @param {number} value
 * @return {!proto.flow.execution.GetTransactionErrorMessageByIndexRequest} returns this
 */
proto.flow.execution.GetTransactionErrorMessageByIndexRequest.prototype.setIndex = function (value) {
    return jspb.Message.setProto3IntField(this, 2, value);
};
if (jspb.Message.GENERATE_TO_OBJECT) {
    /**
     * Creates an object representation of this proto.
     * Field names that are reserved in JavaScript and will be renamed to pb_name.
     * Optional fields that are not set will be set to undefined.
     * To access a reserved field use, foo.pb_<name>, eg, foo.pb_default.
     * For the list of reserved names please see:
     *     net/proto2/compiler/js/internal/generator.cc#kKeyword.
     * @param {boolean=} opt_includeInstance Deprecated. whether to include the
     *     JSPB instance for transitional soy proto support:
     *     http://goto/soy-param-migration
     * @return {!Object}
     */
    proto.flow.execution.GetTransactionErrorMessageResponse.prototype.toObject = function (opt_includeInstance) {
        return proto.flow.execution.GetTransactionErrorMessageResponse.toObject(opt_includeInstance, this);
    };
    /**
     * Static version of the {@see toObject} method.
     * @param {boolean|undefined} includeInstance Deprecated. Whether to include
     *     the JSPB instance for transitional soy proto support:
     *     http://goto/soy-param-migration
     * @param {!proto.flow.execution.GetTransactionErrorMessageResponse} msg The msg instance to transform.
     * @return {!Object}
     * @suppress {unusedLocalVariables} f is only used for nested messages
     */
    proto.flow.execution.GetTransactionErrorMessageResponse.toObject = function (includeInstance, msg) {
        var f, obj = {
            transactionId: msg.getTransactionId_asB64(),
            errorMessage: jspb.Message.getFieldWithDefault(msg, 2, "")
        };
        if (includeInstance) {
            obj.$jspbMessageInstance = msg;
        }
        return obj;
    };
}
/**
 * Deserializes binary data (in protobuf wire format).
 * @param {jspb.ByteSource} bytes The bytes to deserialize.
 * @return {!proto.flow.execution.GetTransactionErrorMessageResponse}
 */
proto.flow.execution.GetTransactionErrorMessageResponse.deserializeBinary = function (bytes) {
    var reader = new jspb.BinaryReader(bytes);
    var msg = new proto.flow.execution.GetTransactionErrorMessageResponse;
    return proto.flow.execution.GetTransactionErrorMessageResponse.deserializeBinaryFromReader(msg, reader);
};
/**
 * Deserializes binary data (in protobuf wire format) from the
 * given reader into the given message object.
 * @param {!proto.flow.execution.GetTransactionErrorMessageResponse} msg The message object to deserialize into.
 * @param {!jspb.BinaryReader} reader The BinaryReader to use.
 * @return {!proto.flow.execution.GetTransactionErrorMessageResponse}
 */
proto.flow.execution.GetTransactionErrorMessageResponse.deserializeBinaryFromReader = function (msg, reader) {
    while (reader.nextField()) {
        if (reader.isEndGroup()) {
            break;
        }
        var field = reader.getFieldNumber();
        switch (field) {
            case 1:
                var value = /** @type {!Uint8Array} */ (reader.readBytes());
                msg.setTransactionId(value);
                break;
            case 2:
                var value = /** @type {string} */ (reader.readString());
                msg.setErrorMessage(value);
                break;
            default:
                reader.skipField();
                break;
        }
    }
    return msg;
};
/**
 * Serializes the message to binary data (in protobuf wire format).
 * @return {!Uint8Array}
 */
proto.flow.execution.GetTransactionErrorMessageResponse.prototype.serializeBinary = function () {
    var writer = new jspb.BinaryWriter();
    proto.flow.execution.GetTransactionErrorMessageResponse.serializeBinaryToWriter(this, writer);
    return writer.getResultBuffer();
};
/**
 * Serializes the given message to binary data (in protobuf wire
 * format), writing to the given BinaryWriter.
 * @param {!proto.flow.execution.GetTransactionErrorMessageResponse} message
 * @param {!jspb.BinaryWriter} writer
 * @suppress {unusedLocalVariables} f is only used for nested messages
 */
proto.flow.execution.GetTransactionErrorMessageResponse.serializeBinaryToWriter = function (message, writer) {
    var f = undefined;
    f = message.getTransactionId_asU8();
    if (f.length > 0) {
        writer.writeBytes(1, f);
    }
    f = message.getErrorMessage();
    if (f.length > 0) {
        writer.writeString(2, f);
    }
};
/**
 * optional bytes transaction_id = 1;
 * @return {!(string|Uint8Array)}
 */
proto.flow.execution.GetTransactionErrorMessageResponse.prototype.getTransactionId = function () {
    return /** @type {!(string|Uint8Array)} */ (jspb.Message.getFieldWithDefault(this, 1, ""));
};
/**
 * optional bytes transaction_id = 1;
 * This is a type-conversion wrapper around `getTransactionId()`
 * @return {string}
 */
proto.flow.execution.GetTransactionErrorMessageResponse.prototype.getTransactionId_asB64 = function () {
    return /** @type {string} */ (jspb.Message.bytesAsB64(this.getTransactionId()));
};
/**
 * optional bytes transaction_id = 1;
 * Note that Uint8Array is not supported on all browsers.
 * @see http://caniuse.com/Uint8Array
 * This is a type-conversion wrapper around `getTransactionId()`
 * @return {!Uint8Array}
 */
proto.flow.execution.GetTransactionErrorMessageResponse.prototype.getTransactionId_asU8 = function () {
    return /** @type {!Uint8Array} */ (jspb.Message.bytesAsU8(this.getTransactionId()));
};
/**
 * @param {!(string|Uint8Array)} value
 * @return {!proto.flow.execution.GetTransactionErrorMessageResponse} returns this
 */
proto.flow.execution.GetTransactionErrorMessageResponse.prototype.setTransactionId = function (value) {
    return jspb.Message.setProto3BytesField(this, 1, value);
};
/**
 * optional string error_message = 2;
 * @return {string}
 */
proto.flow.execution.GetTransactionErrorMessageResponse.prototype.getErrorMessage = function () {
    return /** @type {string} */ (jspb.Message.getFieldWithDefault(this, 2, ""));
};
/**
 * @param {string} value
 * @return {!proto.flow.execution.GetTransactionErrorMessageResponse} returns this
 */
proto.flow.execution.GetTransactionErrorMessageResponse.prototype.setErrorMessage = function (value) {
    return jspb.Message.setProto3StringField(this, 2, value);
};
if (jspb.Message.GENERATE_TO_OBJECT) {
    /**
     * Creates an object representation of this proto.
     * Field names that are reserved in JavaScript and will be renamed to pb_name.
     * Optional fields that are not set will be set to undefined.
     * To access a reserved field use, foo.pb_<name>, eg, foo.pb_default.
     * For the list of reserved names please see:
     *     net/proto2/compiler/js/internal/generator.cc#kKeyword.
     * @param {boolean=} opt_includeInstance Deprecated. whether to include the
     *     JSPB instance for transitional soy proto support:
     *     http://goto/soy-param-migration
     * @return {!Object}
     */
    proto.flow.execution.GetTransactionErrorMessagesByBlockIDRequest.prototype.toObject = function (opt_includeInstance) {
        return proto.flow.execution.GetTransactionErrorMessagesByBlockIDRequest.toObject(opt_includeInstance, this);
    };
    /**
     * Static version of the {@see toObject} method.
     * @param {boolean|undefined} includeInstance Deprecated. Whether to include
     *     the JSPB instance for transitional soy proto support:
     *     http://goto/soy-param-migration
     * @param {!proto.flow.execution.GetTransactionErrorMessagesByBlockIDRequest} msg The msg instance to transform.
     * @return {!Object}
     * @suppress {unusedLocalVariables} f is only used for nested messages
     */
    proto.flow.execution.GetTransactionErrorMessagesByBlockIDRequest.toObject = function (includeInstance, msg) {
        var f, obj = {
            blockId: msg.getBlockId_asB64()
        };
        if (includeInstance) {
            obj.$jspbMessageInstance = msg;
        }
        return obj;
    };
}
/**
 * Deserializes binary data (in protobuf wire format).
 * @param {jspb.ByteSource} bytes The bytes to deserialize.
 * @return {!proto.flow.execution.GetTransactionErrorMessagesByBlockIDRequest}
 */
proto.flow.execution.GetTransactionErrorMessagesByBlockIDRequest.deserializeBinary = function (bytes) {
    var reader = new jspb.BinaryReader(bytes);
    var msg = new proto.flow.execution.GetTransactionErrorMessagesByBlockIDRequest;
    return proto.flow.execution.GetTransactionErrorMessagesByBlockIDRequest.deserializeBinaryFromReader(msg, reader);
};
/**
 * Deserializes binary data (in protobuf wire format) from the
 * given reader into the given message object.
 * @param {!proto.flow.execution.GetTransactionErrorMessagesByBlockIDRequest} msg The message object to deserialize into.
 * @param {!jspb.BinaryReader} reader The BinaryReader to use.
 * @return {!proto.flow.execution.GetTransactionErrorMessagesByBlockIDRequest}
 */
proto.flow.execution.GetTransactionErrorMessagesByBlockIDRequest.deserializeBinaryFromReader = function (msg, reader) {
    while (reader.nextField()) {
        if (reader.isEndGroup()) {
            break;
        }
        var field = reader.getFieldNumber();
        switch (field) {
            case 1:
                var value = /** @type {!Uint8Array} */ (reader.readBytes());
                msg.setBlockId(value);
                break;
            default:
                reader.skipField();
                break;
        }
    }
    return msg;
};
/**
 * Serializes the message to binary data (in protobuf wire format).
 * @return {!Uint8Array}
 */
proto.flow.execution.GetTransactionErrorMessagesByBlockIDRequest.prototype.serializeBinary = function () {
    var writer = new jspb.BinaryWriter();
    proto.flow.execution.GetTransactionErrorMessagesByBlockIDRequest.serializeBinaryToWriter(this, writer);
    return writer.getResultBuffer();
};
/**
 * Serializes the given message to binary data (in protobuf wire
 * format), writing to the given BinaryWriter.
 * @param {!proto.flow.execution.GetTransactionErrorMessagesByBlockIDRequest} message
 * @param {!jspb.BinaryWriter} writer
 * @suppress {unusedLocalVariables} f is only used for nested messages
 */
proto.flow.execution.GetTransactionErrorMessagesByBlockIDRequest.serializeBinaryToWriter = function (message, writer) {
    var f = undefined;
    f = message.getBlockId_asU8();
    if (f.length > 0) {
        writer.writeBytes(1, f);
    }
};
/**
 * optional bytes block_id = 1;
 * @return {!(string|Uint8Array)}
 */
proto.flow.execution.GetTransactionErrorMessagesByBlockIDRequest.prototype.getBlockId = function () {
    return /** @type {!(string|Uint8Array)} */ (jspb.Message.getFieldWithDefault(this, 1, ""));
};
/**
 * optional bytes block_id = 1;
 * This is a type-conversion wrapper around `getBlockId()`
 * @return {string}
 */
proto.flow.execution.GetTransactionErrorMessagesByBlockIDRequest.prototype.getBlockId_asB64 = function () {
    return /** @type {string} */ (jspb.Message.bytesAsB64(this.getBlockId()));
};
/**
 * optional bytes block_id = 1;
 * Note that Uint8Array is not supported on all browsers.
 * @see http://caniuse.com/Uint8Array
 * This is a type-conversion wrapper around `getBlockId()`
 * @return {!Uint8Array}
 */
proto.flow.execution.GetTransactionErrorMessagesByBlockIDRequest.prototype.getBlockId_asU8 = function () {
    return /** @type {!Uint8Array} */ (jspb.Message.bytesAsU8(this.getBlockId()));
};
/**
 * @param {!(string|Uint8Array)} value
 * @return {!proto.flow.execution.GetTransactionErrorMessagesByBlockIDRequest} returns this
 */
proto.flow.execution.GetTransactionErrorMessagesByBlockIDRequest.prototype.setBlockId = function (value) {
    return jspb.Message.setProto3BytesField(this, 1, value);
};
/**
 * List of repeated fields within this message type.
 * @private {!Array<number>}
 * @const
 */
proto.flow.execution.GetTransactionErrorMessagesResponse.repeatedFields_ = [1];
if (jspb.Message.GENERATE_TO_OBJECT) {
    /**
     * Creates an object representation of this proto.
     * Field names that are reserved in JavaScript and will be renamed to pb_name.
     * Optional fields that are not set will be set to undefined.
     * To access a reserved field use, foo.pb_<name>, eg, foo.pb_default.
     * For the list of reserved names please see:
     *     net/proto2/compiler/js/internal/generator.cc#kKeyword.
     * @param {boolean=} opt_includeInstance Deprecated. whether to include the
     *     JSPB instance for transitional soy proto support:
     *     http://goto/soy-param-migration
     * @return {!Object}
     */
    proto.flow.execution.GetTransactionErrorMessagesResponse.prototype.toObject = function (opt_includeInstance) {
        return proto.flow.execution.GetTransactionErrorMessagesResponse.toObject(opt_includeInstance, this);
    };
    /**
     * Static version of the {@see toObject} method.
     * @param {boolean|undefined} includeInstance Deprecated. Whether to include
     *     the JSPB instance for transitional soy proto support:
     *     http://goto/soy-param-migration
     * @param {!proto.flow.execution.GetTransactionErrorMessagesResponse} msg The msg instance to transform.
     * @return {!Object}
     * @suppress {unusedLocalVariables} f is only used for nested messages
     */
    proto.flow.execution.GetTransactionErrorMessagesResponse.toObject = function (includeInstance, msg) {
        var f, obj = {
            resultsList: jspb.Message.toObjectList(msg.getResultsList(), proto.flow.execution.GetTransactionErrorMessagesResponse.Result.toObject, includeInstance)
        };
        if (includeInstance) {
            obj.$jspbMessageInstance = msg;
        }
        return obj;
    };
}
/**
 * Deserializes binary data (in protobuf wire format).
 * @param {jspb.ByteSource} bytes The bytes to deserialize.
 * @return {!proto.flow.execution.GetTransactionErrorMessagesResponse}
 */
proto.flow.execution.GetTransactionErrorMessagesResponse.deserializeBinary = function (bytes) {
    var reader = new jspb.BinaryReader(bytes);
    var msg = new proto.flow.execution.GetTransactionErrorMessagesResponse;
    return proto.flow.execution.GetTransactionErrorMessagesResponse.deserializeBinaryFromReader(msg, reader);
};
/**
 * Deserializes binary data (in protobuf wire format) from the
 * given reader into the given message object.
 * @param {!proto.flow.execution.GetTransactionErrorMessagesResponse} msg The message object to deserialize into.
 * @param {!jspb.BinaryReader} reader The BinaryReader to use.
 * @return {!proto.flow.execution.GetTransactionErrorMessagesResponse}
 */
proto.flow.execution.GetTransactionErrorMessagesResponse.deserializeBinaryFromReader = function (msg, reader) {
    while (reader.nextField()) {
        if (reader.isEndGroup()) {
            break;
        }
        var field = reader.getFieldNumber();
        switch (field) {
            case 1:
                var value = new proto.flow.execution.GetTransactionErrorMessagesResponse.Result;
                reader.readMessage(value, proto.flow.execution.GetTransactionErrorMessagesResponse.Result.deserializeBinaryFromReader);
                msg.addResults(value);
                break;
            default:
                reader.skipField();
                break;
        }
    }
    return msg;
};
/**
 * Serializes the message to binary data (in protobuf wire format).
 * @return {!Uint8Array}
 */
proto.flow.execution.GetTransactionErrorMessagesResponse.prototype.serializeBinary = function () {
    var writer = new jspb.BinaryWriter();
    proto.flow.execution.GetTransactionErrorMessagesResponse.serializeBinaryToWriter(this, writer);
    return writer.getResultBuffer();
};
/**
 * Serializes the given message to binary data (in protobuf wire
 * format), writing to the given BinaryWriter.
 * @param {!proto.flow.execution.GetTransactionErrorMessagesResponse} message
 * @param {!jspb.BinaryWriter} writer
 * @suppress {unusedLocalVariables} f is only used for nested messages
 */
proto.flow.execution.GetTransactionErrorMessagesResponse.serializeBinaryToWriter = function (message, writer) {
    var f = undefined;
    f = message.getResultsList();
    if (f.length > 0) {
        writer.writeRepeatedMessage(1, f, proto.flow.execution.GetTransactionErrorMessagesResponse.Result.serializeBinaryToWriter);
    }
};
if (jspb.Message.GENERATE_TO_OBJECT) {
    /**
     * Creates an object representation of this proto.
     * Field names that are reserved in JavaScript and will be renamed to pb_name.
     * Optional fields that are not set will be set to undefined.
     * To access a reserved field use, foo.pb_<name>, eg, foo.pb_default.
     * For the list of reserved names please see:
     *     net/proto2/compiler/js/internal/generator.cc#kKeyword.
     * @param {boolean=} opt_includeInstance Deprecated. whether to include the
     *     JSPB instance for transitional soy proto support:
     *     http://goto/soy-param-migration
     * @return {!Object}
     */
    proto.flow.execution.GetTransactionErrorMessagesResponse.Result.prototype.toObject = function (opt_includeInstance) {
        return proto.flow.execution.GetTransactionErrorMessagesResponse.Result.toObject(opt_includeInstance, this);
    };
    /**
     * Static version of the {@see toObject} method.
     * @param {boolean|undefined} includeInstance Deprecated. Whether to include
     *     the JSPB instance for transitional soy proto support:
     *     http://goto/soy-param-migration
     * @param {!proto.flow.execution.GetTransactionErrorMessagesResponse.Result} msg The msg instance to transform.
     * @return {!Object}
     * @suppress {unusedLocalVariables} f is only used for nested messages
     */
    proto.flow.execution.GetTransactionErrorMessagesResponse.Result.toObject = function (includeInstance, msg) {
        var f, obj = {
            transactionId: msg.getTransactionId_asB64(),
            index: jspb.Message.getFieldWithDefault(msg, 2, 0),
            errorMessage: jspb.Message.getFieldWithDefault(msg, 3, "")
        };
        if (includeInstance) {
            obj.$jspbMessageInstance = msg;
        }
        return obj;
    };
}
/**
 * Deserializes binary data (in protobuf wire format).
 * @param {jspb.ByteSource} bytes The bytes to deserialize.
 * @return {!proto.flow.execution.GetTransactionErrorMessagesResponse.Result}
 */
proto.flow.execution.GetTransactionErrorMessagesResponse.Result.deserializeBinary = function (bytes) {
    var reader = new jspb.BinaryReader(bytes);
    var msg = new proto.flow.execution.GetTransactionErrorMessagesResponse.Result;
    return proto.flow.execution.GetTransactionErrorMessagesResponse.Result.deserializeBinaryFromReader(msg, reader);
};
/**
 * Deserializes binary data (in protobuf wire format) from the
 * given reader into the given message object.
 * @param {!proto.flow.execution.GetTransactionErrorMessagesResponse.Result} msg The message object to deserialize into.
 * @param {!jspb.BinaryReader} reader The BinaryReader to use.
 * @return {!proto.flow.execution.GetTransactionErrorMessagesResponse.Result}
 */
proto.flow.execution.GetTransactionErrorMessagesResponse.Result.deserializeBinaryFromReader = function (msg, reader) {
    while (reader.nextField()) {
        if (reader.isEndGroup()) {
            break;
        }
        var field = reader.getFieldNumber();
        switch (field) {
            case 1:
                var value = /** @type {!Uint8Array} */ (reader.readBytes());
                msg.setTransactionId(value);
                break;
            case 2:
                var value = /** @type {number} */ (reader.readUint32());
                msg.setIndex(value);
                break;
            case 3:
                var value = /** @type {string} */ (reader.readString());
                msg.setErrorMessage(value);
                break;
            default:
                reader.skipField();
                break;
        }
    }
    return msg;
};
/**
 * Serializes the message to binary data (in protobuf wire format).
 * @return {!Uint8Array}
 */
proto.flow.execution.GetTransactionErrorMessagesResponse.Result.prototype.serializeBinary = function () {
    var writer = new jspb.BinaryWriter();
    proto.flow.execution.GetTransactionErrorMessagesResponse.Result.serializeBinaryToWriter(this, writer);
    return writer.getResultBuffer();
};
/**
 * Serializes the given message to binary data (in protobuf wire
 * format), writing to the given BinaryWriter.
 * @param {!proto.flow.execution.GetTransactionErrorMessagesResponse.Result} message
 * @param {!jspb.BinaryWriter} writer
 * @suppress {unusedLocalVariables} f is only used for nested messages
 */
proto.flow.execution.GetTransactionErrorMessagesResponse.Result.serializeBinaryToWriter = function (message, writer) {
    var f = undefined;
    f = message.getTransactionId_asU8();
    if (f.length > 0) {
        writer.writeBytes(1, f);
    }
    f = message.getIndex();
    if (f !== 0) {
        writer.writeUint32(2, f);
    }
    f = message.getErrorMessage();
    if (f.length > 0) {
        writer.writeString(3, f);
    }
};
/**
 * optional bytes transaction_id = 1;
 * @return {!(string|Uint8Array)}
 */
proto.flow.execution.GetTransactionErrorMessagesResponse.Result.prototype.getTransactionId = function () {
    return /** @type {!(string|Uint8Array)} */ (jspb.Message.getFieldWithDefault(this, 1, ""));
};
/**
 * optional bytes transaction_id = 1;
 * This is a type-conversion wrapper around `getTransactionId()`
 * @return {string}
 */
proto.flow.execution.GetTransactionErrorMessagesResponse.Result.prototype.getTransactionId_asB64 = function () {
    return /** @type {string} */ (jspb.Message.bytesAsB64(this.getTransactionId()));
};
/**
 * optional bytes transaction_id = 1;
 * Note that Uint8Array is not supported on all browsers.
 * @see http://caniuse.com/Uint8Array
 * This is a type-conversion wrapper around `getTransactionId()`
 * @return {!Uint8Array}
 */
proto.flow.execution.GetTransactionErrorMessagesResponse.Result.prototype.getTransactionId_asU8 = function () {
    return /** @type {!Uint8Array} */ (jspb.Message.bytesAsU8(this.getTransactionId()));
};
/**
 * @param {!(string|Uint8Array)} value
 * @return {!proto.flow.execution.GetTransactionErrorMessagesResponse.Result} returns this
 */
proto.flow.execution.GetTransactionErrorMessagesResponse.Result.prototype.setTransactionId = function (value) {
    return jspb.Message.setProto3BytesField(this, 1, value);
};
/**
 * optional uint32 index = 2;
 * @return {number}
 */
proto.flow.execution.GetTransactionErrorMessagesResponse.Result.prototype.getIndex = function () {
    return /** @type {number} */ (jspb.Message.getFieldWithDefault(this, 2, 0));
};
/**
 * @param {number} value
 * @return {!proto.flow.execution.GetTransactionErrorMessagesResponse.Result} returns this
 */
proto.flow.execution.GetTransactionErrorMessagesResponse.Result.prototype.setIndex = function (value) {
    return jspb.Message.setProto3IntField(this, 2, value);
};
/**
 * optional string error_message = 3;
 * @return {string}
 */
proto.flow.execution.GetTransactionErrorMessagesResponse.Result.prototype.getErrorMessage = function () {
    return /** @type {string} */ (jspb.Message.getFieldWithDefault(this, 3, ""));
};
/**
 * @param {string} value
 * @return {!proto.flow.execution.GetTransactionErrorMessagesResponse.Result} returns this
 */
proto.flow.execution.GetTransactionErrorMessagesResponse.Result.prototype.setErrorMessage = function (value) {
    return jspb.Message.setProto3StringField(this, 3, value);
};
/**
 * repeated Result results = 1;
 * @return {!Array<!proto.flow.execution.GetTransactionErrorMessagesResponse.Result>}
 */
proto.flow.execution.GetTransactionErrorMessagesResponse.prototype.getResultsList = function () {
    return /** @type{!Array<!proto.flow.execution.GetTransactionErrorMessagesResponse.Result>} */ (jspb.Message.getRepeatedWrapperField(this, proto.flow.execution.GetTransactionErrorMessagesResponse.Result, 1));
};
/**
 * @param {!Array<!proto.flow.execution.GetTransactionErrorMessagesResponse.Result>} value
 * @return {!proto.flow.execution.GetTransactionErrorMessagesResponse} returns this
*/
proto.flow.execution.GetTransactionErrorMessagesResponse.prototype.setResultsList = function (value) {
    return jspb.Message.setRepeatedWrapperField(this, 1, value);
};
/**
 * @param {!proto.flow.execution.GetTransactionErrorMessagesResponse.Result=} opt_value
 * @param {number=} opt_index
 * @return {!proto.flow.execution.GetTransactionErrorMessagesResponse.Result}
 */
proto.flow.execution.GetTransactionErrorMessagesResponse.prototype.addResults = function (opt_value, opt_index) {
    return jspb.Message.addToRepeatedWrapperField(this, 1, opt_value, proto.flow.execution.GetTransactionErrorMessagesResponse.Result, opt_index);
};
/**
 * Clears the list making it empty but non-null.
 * @return {!proto.flow.execution.GetTransactionErrorMessagesResponse} returns this
 */
proto.flow.execution.GetTransactionErrorMessagesResponse.prototype.clearResultsList = function () {
    return this.setResultsList([]);
};
if (jspb.Message.GENERATE_TO_OBJECT) {
    /**
     * Creates an object representation of this proto.
     * Field names that are reserved in JavaScript and will be renamed to pb_name.
     * Optional fields that are not set will be set to undefined.
     * To access a reserved field use, foo.pb_<name>, eg, foo.pb_default.
     * For the list of reserved names please see:
     *     net/proto2/compiler/js/internal/generator.cc#kKeyword.
     * @param {boolean=} opt_includeInstance Deprecated. whether to include the
     *     JSPB instance for transitional soy proto support:
     *     http://goto/soy-param-migration
     * @return {!Object}
     */
    proto.flow.execution.GetRegisterAtBlockIDRequest.prototype.toObject = function (opt_includeInstance) {
        return proto.flow.execution.GetRegisterAtBlockIDRequest.toObject(opt_includeInstance, this);
    };
    /**
     * Static version of the {@see toObject} method.
     * @param {boolean|undefined} includeInstance Deprecated. Whether to include
     *     the JSPB instance for transitional soy proto support:
     *     http://goto/soy-param-migration
     * @param {!proto.flow.execution.GetRegisterAtBlockIDRequest} msg The msg instance to transform.
     * @return {!Object}
     * @suppress {unusedLocalVariables} f is only used for nested messages
     */
    proto.flow.execution.GetRegisterAtBlockIDRequest.toObject = function (includeInstance, msg) {
        var f, obj = {
            blockId: msg.getBlockId_asB64(),
            registerOwner: msg.getRegisterOwner_asB64(),
            registerKey: msg.getRegisterKey_asB64()
        };
        if (includeInstance) {
            obj.$jspbMessageInstance = msg;
        }
        return obj;
    };
}
/**
 * Deserializes binary data (in protobuf wire format).
 * @param {jspb.ByteSource} bytes The bytes to deserialize.
 * @return {!proto.flow.execution.GetRegisterAtBlockIDRequest}
 */
proto.flow.execution.GetRegisterAtBlockIDRequest.deserializeBinary = function (bytes) {
    var reader = new jspb.BinaryReader(bytes);
    var msg = new proto.flow.execution.GetRegisterAtBlockIDRequest;
    return proto.flow.execution.GetRegisterAtBlockIDRequest.deserializeBinaryFromReader(msg, reader);
};
/**
 * Deserializes binary data (in protobuf wire format) from the
 * given reader into the given message object.
 * @param {!proto.flow.execution.GetRegisterAtBlockIDRequest} msg The message object to deserialize into.
 * @param {!jspb.BinaryReader} reader The BinaryReader to use.
 * @return {!proto.flow.execution.GetRegisterAtBlockIDRequest}
 */
proto.flow.execution.GetRegisterAtBlockIDRequest.deserializeBinaryFromReader = function (msg, reader) {
    while (reader.nextField()) {
        if (reader.isEndGroup()) {
            break;
        }
        var field = reader.getFieldNumber();
        switch (field) {
            case 1:
                var value = /** @type {!Uint8Array} */ (reader.readBytes());
                msg.setBlockId(value);
                break;
            case 2:
                var value = /** @type {!Uint8Array} */ (reader.readBytes());
                msg.setRegisterOwner(value);
                break;
            case 4:
                var value = /** @type {!Uint8Array} */ (reader.readBytes());
                msg.setRegisterKey(value);
                break;
            default:
                reader.skipField();
                break;
        }
    }
    return msg;
};
/**
 * Serializes the message to binary data (in protobuf wire format).
 * @return {!Uint8Array}
 */
proto.flow.execution.GetRegisterAtBlockIDRequest.prototype.serializeBinary = function () {
    var writer = new jspb.BinaryWriter();
    proto.flow.execution.GetRegisterAtBlockIDRequest.serializeBinaryToWriter(this, writer);
    return writer.getResultBuffer();
};
/**
 * Serializes the given message to binary data (in protobuf wire
 * format), writing to the given BinaryWriter.
 * @param {!proto.flow.execution.GetRegisterAtBlockIDRequest} message
 * @param {!jspb.BinaryWriter} writer
 * @suppress {unusedLocalVariables} f is only used for nested messages
 */
proto.flow.execution.GetRegisterAtBlockIDRequest.serializeBinaryToWriter = function (message, writer) {
    var f = undefined;
    f = message.getBlockId_asU8();
    if (f.length > 0) {
        writer.writeBytes(1, f);
    }
    f = message.getRegisterOwner_asU8();
    if (f.length > 0) {
        writer.writeBytes(2, f);
    }
    f = message.getRegisterKey_asU8();
    if (f.length > 0) {
        writer.writeBytes(4, f);
    }
};
/**
 * optional bytes block_id = 1;
 * @return {!(string|Uint8Array)}
 */
proto.flow.execution.GetRegisterAtBlockIDRequest.prototype.getBlockId = function () {
    return /** @type {!(string|Uint8Array)} */ (jspb.Message.getFieldWithDefault(this, 1, ""));
};
/**
 * optional bytes block_id = 1;
 * This is a type-conversion wrapper around `getBlockId()`
 * @return {string}
 */
proto.flow.execution.GetRegisterAtBlockIDRequest.prototype.getBlockId_asB64 = function () {
    return /** @type {string} */ (jspb.Message.bytesAsB64(this.getBlockId()));
};
/**
 * optional bytes block_id = 1;
 * Note that Uint8Array is not supported on all browsers.
 * @see http://caniuse.com/Uint8Array
 * This is a type-conversion wrapper around `getBlockId()`
 * @return {!Uint8Array}
 */
proto.flow.execution.GetRegisterAtBlockIDRequest.prototype.getBlockId_asU8 = function () {
    return /** @type {!Uint8Array} */ (jspb.Message.bytesAsU8(this.getBlockId()));
};
/**
 * @param {!(string|Uint8Array)} value
 * @return {!proto.flow.execution.GetRegisterAtBlockIDRequest} returns this
 */
proto.flow.execution.GetRegisterAtBlockIDRequest.prototype.setBlockId = function (value) {
    return jspb.Message.setProto3BytesField(this, 1, value);
};
/**
 * optional bytes register_owner = 2;
 * @return {!(string|Uint8Array)}
 */
proto.flow.execution.GetRegisterAtBlockIDRequest.prototype.getRegisterOwner = function () {
    return /** @type {!(string|Uint8Array)} */ (jspb.Message.getFieldWithDefault(this, 2, ""));
};
/**
 * optional bytes register_owner = 2;
 * This is a type-conversion wrapper around `getRegisterOwner()`
 * @return {string}
 */
proto.flow.execution.GetRegisterAtBlockIDRequest.prototype.getRegisterOwner_asB64 = function () {
    return /** @type {string} */ (jspb.Message.bytesAsB64(this.getRegisterOwner()));
};
/**
 * optional bytes register_owner = 2;
 * Note that Uint8Array is not supported on all browsers.
 * @see http://caniuse.com/Uint8Array
 * This is a type-conversion wrapper around `getRegisterOwner()`
 * @return {!Uint8Array}
 */
proto.flow.execution.GetRegisterAtBlockIDRequest.prototype.getRegisterOwner_asU8 = function () {
    return /** @type {!Uint8Array} */ (jspb.Message.bytesAsU8(this.getRegisterOwner()));
};
/**
 * @param {!(string|Uint8Array)} value
 * @return {!proto.flow.execution.GetRegisterAtBlockIDRequest} returns this
 */
proto.flow.execution.GetRegisterAtBlockIDRequest.prototype.setRegisterOwner = function (value) {
    return jspb.Message.setProto3BytesField(this, 2, value);
};
/**
 * optional bytes register_key = 4;
 * @return {!(string|Uint8Array)}
 */
proto.flow.execution.GetRegisterAtBlockIDRequest.prototype.getRegisterKey = function () {
    return /** @type {!(string|Uint8Array)} */ (jspb.Message.getFieldWithDefault(this, 4, ""));
};
/**
 * optional bytes register_key = 4;
 * This is a type-conversion wrapper around `getRegisterKey()`
 * @return {string}
 */
proto.flow.execution.GetRegisterAtBlockIDRequest.prototype.getRegisterKey_asB64 = function () {
    return /** @type {string} */ (jspb.Message.bytesAsB64(this.getRegisterKey()));
};
/**
 * optional bytes register_key = 4;
 * Note that Uint8Array is not supported on all browsers.
 * @see http://caniuse.com/Uint8Array
 * This is a type-conversion wrapper around `getRegisterKey()`
 * @return {!Uint8Array}
 */
proto.flow.execution.GetRegisterAtBlockIDRequest.prototype.getRegisterKey_asU8 = function () {
    return /** @type {!Uint8Array} */ (jspb.Message.bytesAsU8(this.getRegisterKey()));
};
/**
 * @param {!(string|Uint8Array)} value
 * @return {!proto.flow.execution.GetRegisterAtBlockIDRequest} returns this
 */
proto.flow.execution.GetRegisterAtBlockIDRequest.prototype.setRegisterKey = function (value) {
    return jspb.Message.setProto3BytesField(this, 4, value);
};
if (jspb.Message.GENERATE_TO_OBJECT) {
    /**
     * Creates an object representation of this proto.
     * Field names that are reserved in JavaScript and will be renamed to pb_name.
     * Optional fields that are not set will be set to undefined.
     * To access a reserved field use, foo.pb_<name>, eg, foo.pb_default.
     * For the list of reserved names please see:
     *     net/proto2/compiler/js/internal/generator.cc#kKeyword.
     * @param {boolean=} opt_includeInstance Deprecated. whether to include the
     *     JSPB instance for transitional soy proto support:
     *     http://goto/soy-param-migration
     * @return {!Object}
     */
    proto.flow.execution.GetRegisterAtBlockIDResponse.prototype.toObject = function (opt_includeInstance) {
        return proto.flow.execution.GetRegisterAtBlockIDResponse.toObject(opt_includeInstance, this);
    };
    /**
     * Static version of the {@see toObject} method.
     * @param {boolean|undefined} includeInstance Deprecated. Whether to include
     *     the JSPB instance for transitional soy proto support:
     *     http://goto/soy-param-migration
     * @param {!proto.flow.execution.GetRegisterAtBlockIDResponse} msg The msg instance to transform.
     * @return {!Object}
     * @suppress {unusedLocalVariables} f is only used for nested messages
     */
    proto.flow.execution.GetRegisterAtBlockIDResponse.toObject = function (includeInstance, msg) {
        var f, obj = {
            value: msg.getValue_asB64()
        };
        if (includeInstance) {
            obj.$jspbMessageInstance = msg;
        }
        return obj;
    };
}
/**
 * Deserializes binary data (in protobuf wire format).
 * @param {jspb.ByteSource} bytes The bytes to deserialize.
 * @return {!proto.flow.execution.GetRegisterAtBlockIDResponse}
 */
proto.flow.execution.GetRegisterAtBlockIDResponse.deserializeBinary = function (bytes) {
    var reader = new jspb.BinaryReader(bytes);
    var msg = new proto.flow.execution.GetRegisterAtBlockIDResponse;
    return proto.flow.execution.GetRegisterAtBlockIDResponse.deserializeBinaryFromReader(msg, reader);
};
/**
 * Deserializes binary data (in protobuf wire format) from the
 * given reader into the given message object.
 * @param {!proto.flow.execution.GetRegisterAtBlockIDResponse} msg The message object to deserialize into.
 * @param {!jspb.BinaryReader} reader The BinaryReader to use.
 * @return {!proto.flow.execution.GetRegisterAtBlockIDResponse}
 */
proto.flow.execution.GetRegisterAtBlockIDResponse.deserializeBinaryFromReader = function (msg, reader) {
    while (reader.nextField()) {
        if (reader.isEndGroup()) {
            break;
        }
        var field = reader.getFieldNumber();
        switch (field) {
            case 1:
                var value = /** @type {!Uint8Array} */ (reader.readBytes());
                msg.setValue(value);
                break;
            default:
                reader.skipField();
                break;
        }
    }
    return msg;
};
/**
 * Serializes the message to binary data (in protobuf wire format).
 * @return {!Uint8Array}
 */
proto.flow.execution.GetRegisterAtBlockIDResponse.prototype.serializeBinary = function () {
    var writer = new jspb.BinaryWriter();
    proto.flow.execution.GetRegisterAtBlockIDResponse.serializeBinaryToWriter(this, writer);
    return writer.getResultBuffer();
};
/**
 * Serializes the given message to binary data (in protobuf wire
 * format), writing to the given BinaryWriter.
 * @param {!proto.flow.execution.GetRegisterAtBlockIDResponse} message
 * @param {!jspb.BinaryWriter} writer
 * @suppress {unusedLocalVariables} f is only used for nested messages
 */
proto.flow.execution.GetRegisterAtBlockIDResponse.serializeBinaryToWriter = function (message, writer) {
    var f = undefined;
    f = message.getValue_asU8();
    if (f.length > 0) {
        writer.writeBytes(1, f);
    }
};
/**
 * optional bytes value = 1;
 * @return {!(string|Uint8Array)}
 */
proto.flow.execution.GetRegisterAtBlockIDResponse.prototype.getValue = function () {
    return /** @type {!(string|Uint8Array)} */ (jspb.Message.getFieldWithDefault(this, 1, ""));
};
/**
 * optional bytes value = 1;
 * This is a type-conversion wrapper around `getValue()`
 * @return {string}
 */
proto.flow.execution.GetRegisterAtBlockIDResponse.prototype.getValue_asB64 = function () {
    return /** @type {string} */ (jspb.Message.bytesAsB64(this.getValue()));
};
/**
 * optional bytes value = 1;
 * Note that Uint8Array is not supported on all browsers.
 * @see http://caniuse.com/Uint8Array
 * This is a type-conversion wrapper around `getValue()`
 * @return {!Uint8Array}
 */
proto.flow.execution.GetRegisterAtBlockIDResponse.prototype.getValue_asU8 = function () {
    return /** @type {!Uint8Array} */ (jspb.Message.bytesAsU8(this.getValue()));
};
/**
 * @param {!(string|Uint8Array)} value
 * @return {!proto.flow.execution.GetRegisterAtBlockIDResponse} returns this
 */
proto.flow.execution.GetRegisterAtBlockIDResponse.prototype.setValue = function (value) {
    return jspb.Message.setProto3BytesField(this, 1, value);
};
if (jspb.Message.GENERATE_TO_OBJECT) {
    /**
     * Creates an object representation of this proto.
     * Field names that are reserved in JavaScript and will be renamed to pb_name.
     * Optional fields that are not set will be set to undefined.
     * To access a reserved field use, foo.pb_<name>, eg, foo.pb_default.
     * For the list of reserved names please see:
     *     net/proto2/compiler/js/internal/generator.cc#kKeyword.
     * @param {boolean=} opt_includeInstance Deprecated. whether to include the
     *     JSPB instance for transitional soy proto support:
     *     http://goto/soy-param-migration
     * @return {!Object}
     */
    proto.flow.execution.GetLatestBlockHeaderRequest.prototype.toObject = function (opt_includeInstance) {
        return proto.flow.execution.GetLatestBlockHeaderRequest.toObject(opt_includeInstance, this);
    };
    /**
     * Static version of the {@see toObject} method.
     * @param {boolean|undefined} includeInstance Deprecated. Whether to include
     *     the JSPB instance for transitional soy proto support:
     *     http://goto/soy-param-migration
     * @param {!proto.flow.execution.GetLatestBlockHeaderRequest} msg The msg instance to transform.
     * @return {!Object}
     * @suppress {unusedLocalVariables} f is only used for nested messages
     */
    proto.flow.execution.GetLatestBlockHeaderRequest.toObject = function (includeInstance, msg) {
        var f, obj = {
            isSealed: jspb.Message.getBooleanFieldWithDefault(msg, 1, false)
        };
        if (includeInstance) {
            obj.$jspbMessageInstance = msg;
        }
        return obj;
    };
}
/**
 * Deserializes binary data (in protobuf wire format).
 * @param {jspb.ByteSource} bytes The bytes to deserialize.
 * @return {!proto.flow.execution.GetLatestBlockHeaderRequest}
 */
proto.flow.execution.GetLatestBlockHeaderRequest.deserializeBinary = function (bytes) {
    var reader = new jspb.BinaryReader(bytes);
    var msg = new proto.flow.execution.GetLatestBlockHeaderRequest;
    return proto.flow.execution.GetLatestBlockHeaderRequest.deserializeBinaryFromReader(msg, reader);
};
/**
 * Deserializes binary data (in protobuf wire format) from the
 * given reader into the given message object.
 * @param {!proto.flow.execution.GetLatestBlockHeaderRequest} msg The message object to deserialize into.
 * @param {!jspb.BinaryReader} reader The BinaryReader to use.
 * @return {!proto.flow.execution.GetLatestBlockHeaderRequest}
 */
proto.flow.execution.GetLatestBlockHeaderRequest.deserializeBinaryFromReader = function (msg, reader) {
    while (reader.nextField()) {
        if (reader.isEndGroup()) {
            break;
        }
        var field = reader.getFieldNumber();
        switch (field) {
            case 1:
                var value = /** @type {boolean} */ (reader.readBool());
                msg.setIsSealed(value);
                break;
            default:
                reader.skipField();
                break;
        }
    }
    return msg;
};
/**
 * Serializes the message to binary data (in protobuf wire format).
 * @return {!Uint8Array}
 */
proto.flow.execution.GetLatestBlockHeaderRequest.prototype.serializeBinary = function () {
    var writer = new jspb.BinaryWriter();
    proto.flow.execution.GetLatestBlockHeaderRequest.serializeBinaryToWriter(this, writer);
    return writer.getResultBuffer();
};
/**
 * Serializes the given message to binary data (in protobuf wire
 * format), writing to the given BinaryWriter.
 * @param {!proto.flow.execution.GetLatestBlockHeaderRequest} message
 * @param {!jspb.BinaryWriter} writer
 * @suppress {unusedLocalVariables} f is only used for nested messages
 */
proto.flow.execution.GetLatestBlockHeaderRequest.serializeBinaryToWriter = function (message, writer) {
    var f = undefined;
    f = message.getIsSealed();
    if (f) {
        writer.writeBool(1, f);
    }
};
/**
 * optional bool is_sealed = 1;
 * @return {boolean}
 */
proto.flow.execution.GetLatestBlockHeaderRequest.prototype.getIsSealed = function () {
    return /** @type {boolean} */ (jspb.Message.getBooleanFieldWithDefault(this, 1, false));
};
/**
 * @param {boolean} value
 * @return {!proto.flow.execution.GetLatestBlockHeaderRequest} returns this
 */
proto.flow.execution.GetLatestBlockHeaderRequest.prototype.setIsSealed = function (value) {
    return jspb.Message.setProto3BooleanField(this, 1, value);
};
if (jspb.Message.GENERATE_TO_OBJECT) {
    /**
     * Creates an object representation of this proto.
     * Field names that are reserved in JavaScript and will be renamed to pb_name.
     * Optional fields that are not set will be set to undefined.
     * To access a reserved field use, foo.pb_<name>, eg, foo.pb_default.
     * For the list of reserved names please see:
     *     net/proto2/compiler/js/internal/generator.cc#kKeyword.
     * @param {boolean=} opt_includeInstance Deprecated. whether to include the
     *     JSPB instance for transitional soy proto support:
     *     http://goto/soy-param-migration
     * @return {!Object}
     */
    proto.flow.execution.GetBlockHeaderByIDRequest.prototype.toObject = function (opt_includeInstance) {
        return proto.flow.execution.GetBlockHeaderByIDRequest.toObject(opt_includeInstance, this);
    };
    /**
     * Static version of the {@see toObject} method.
     * @param {boolean|undefined} includeInstance Deprecated. Whether to include
     *     the JSPB instance for transitional soy proto support:
     *     http://goto/soy-param-migration
     * @param {!proto.flow.execution.GetBlockHeaderByIDRequest} msg The msg instance to transform.
     * @return {!Object}
     * @suppress {unusedLocalVariables} f is only used for nested messages
     */
    proto.flow.execution.GetBlockHeaderByIDRequest.toObject = function (includeInstance, msg) {
        var f, obj = {
            id: msg.getId_asB64()
        };
        if (includeInstance) {
            obj.$jspbMessageInstance = msg;
        }
        return obj;
    };
}
/**
 * Deserializes binary data (in protobuf wire format).
 * @param {jspb.ByteSource} bytes The bytes to deserialize.
 * @return {!proto.flow.execution.GetBlockHeaderByIDRequest}
 */
proto.flow.execution.GetBlockHeaderByIDRequest.deserializeBinary = function (bytes) {
    var reader = new jspb.BinaryReader(bytes);
    var msg = new proto.flow.execution.GetBlockHeaderByIDRequest;
    return proto.flow.execution.GetBlockHeaderByIDRequest.deserializeBinaryFromReader(msg, reader);
};
/**
 * Deserializes binary data (in protobuf wire format) from the
 * given reader into the given message object.
 * @param {!proto.flow.execution.GetBlockHeaderByIDRequest} msg The message object to deserialize into.
 * @param {!jspb.BinaryReader} reader The BinaryReader to use.
 * @return {!proto.flow.execution.GetBlockHeaderByIDRequest}
 */
proto.flow.execution.GetBlockHeaderByIDRequest.deserializeBinaryFromReader = function (msg, reader) {
    while (reader.nextField()) {
        if (reader.isEndGroup()) {
            break;
        }
        var field = reader.getFieldNumber();
        switch (field) {
            case 1:
                var value = /** @type {!Uint8Array} */ (reader.readBytes());
                msg.setId(value);
                break;
            default:
                reader.skipField();
                break;
        }
    }
    return msg;
};
/**
 * Serializes the message to binary data (in protobuf wire format).
 * @return {!Uint8Array}
 */
proto.flow.execution.GetBlockHeaderByIDRequest.prototype.serializeBinary = function () {
    var writer = new jspb.BinaryWriter();
    proto.flow.execution.GetBlockHeaderByIDRequest.serializeBinaryToWriter(this, writer);
    return writer.getResultBuffer();
};
/**
 * Serializes the given message to binary data (in protobuf wire
 * format), writing to the given BinaryWriter.
 * @param {!proto.flow.execution.GetBlockHeaderByIDRequest} message
 * @param {!jspb.BinaryWriter} writer
 * @suppress {unusedLocalVariables} f is only used for nested messages
 */
proto.flow.execution.GetBlockHeaderByIDRequest.serializeBinaryToWriter = function (message, writer) {
    var f = undefined;
    f = message.getId_asU8();
    if (f.length > 0) {
        writer.writeBytes(1, f);
    }
};
/**
 * optional bytes id = 1;
 * @return {!(string|Uint8Array)}
 */
proto.flow.execution.GetBlockHeaderByIDRequest.prototype.getId = function () {
    return /** @type {!(string|Uint8Array)} */ (jspb.Message.getFieldWithDefault(this, 1, ""));
};
/**
 * optional bytes id = 1;
 * This is a type-conversion wrapper around `getId()`
 * @return {string}
 */
proto.flow.execution.GetBlockHeaderByIDRequest.prototype.getId_asB64 = function () {
    return /** @type {string} */ (jspb.Message.bytesAsB64(this.getId()));
};
/**
 * optional bytes id = 1;
 * Note that Uint8Array is not supported on all browsers.
 * @see http://caniuse.com/Uint8Array
 * This is a type-conversion wrapper around `getId()`
 * @return {!Uint8Array}
 */
proto.flow.execution.GetBlockHeaderByIDRequest.prototype.getId_asU8 = function () {
    return /** @type {!Uint8Array} */ (jspb.Message.bytesAsU8(this.getId()));
};
/**
 * @param {!(string|Uint8Array)} value
 * @return {!proto.flow.execution.GetBlockHeaderByIDRequest} returns this
 */
proto.flow.execution.GetBlockHeaderByIDRequest.prototype.setId = function (value) {
    return jspb.Message.setProto3BytesField(this, 1, value);
};
if (jspb.Message.GENERATE_TO_OBJECT) {
    /**
     * Creates an object representation of this proto.
     * Field names that are reserved in JavaScript and will be renamed to pb_name.
     * Optional fields that are not set will be set to undefined.
     * To access a reserved field use, foo.pb_<name>, eg, foo.pb_default.
     * For the list of reserved names please see:
     *     net/proto2/compiler/js/internal/generator.cc#kKeyword.
     * @param {boolean=} opt_includeInstance Deprecated. whether to include the
     *     JSPB instance for transitional soy proto support:
     *     http://goto/soy-param-migration
     * @return {!Object}
     */
    proto.flow.execution.BlockHeaderResponse.prototype.toObject = function (opt_includeInstance) {
        return proto.flow.execution.BlockHeaderResponse.toObject(opt_includeInstance, this);
    };
    /**
     * Static version of the {@see toObject} method.
     * @param {boolean|undefined} includeInstance Deprecated. Whether to include
     *     the JSPB instance for transitional soy proto support:
     *     http://goto/soy-param-migration
     * @param {!proto.flow.execution.BlockHeaderResponse} msg The msg instance to transform.
     * @return {!Object}
     * @suppress {unusedLocalVariables} f is only used for nested messages
     */
    proto.flow.execution.BlockHeaderResponse.toObject = function (includeInstance, msg) {
        var f, obj = {
            block: (f = msg.getBlock()) && flow_entities_block_header_pb.BlockHeader.toObject(includeInstance, f)
        };
        if (includeInstance) {
            obj.$jspbMessageInstance = msg;
        }
        return obj;
    };
}
/**
 * Deserializes binary data (in protobuf wire format).
 * @param {jspb.ByteSource} bytes The bytes to deserialize.
 * @return {!proto.flow.execution.BlockHeaderResponse}
 */
proto.flow.execution.BlockHeaderResponse.deserializeBinary = function (bytes) {
    var reader = new jspb.BinaryReader(bytes);
    var msg = new proto.flow.execution.BlockHeaderResponse;
    return proto.flow.execution.BlockHeaderResponse.deserializeBinaryFromReader(msg, reader);
};
/**
 * Deserializes binary data (in protobuf wire format) from the
 * given reader into the given message object.
 * @param {!proto.flow.execution.BlockHeaderResponse} msg The message object to deserialize into.
 * @param {!jspb.BinaryReader} reader The BinaryReader to use.
 * @return {!proto.flow.execution.BlockHeaderResponse}
 */
proto.flow.execution.BlockHeaderResponse.deserializeBinaryFromReader = function (msg, reader) {
    while (reader.nextField()) {
        if (reader.isEndGroup()) {
            break;
        }
        var field = reader.getFieldNumber();
        switch (field) {
            case 1:
                var value = new flow_entities_block_header_pb.BlockHeader;
                reader.readMessage(value, flow_entities_block_header_pb.BlockHeader.deserializeBinaryFromReader);
                msg.setBlock(value);
                break;
            default:
                reader.skipField();
                break;
        }
    }
    return msg;
};
/**
 * Serializes the message to binary data (in protobuf wire format).
 * @return {!Uint8Array}
 */
proto.flow.execution.BlockHeaderResponse.prototype.serializeBinary = function () {
    var writer = new jspb.BinaryWriter();
    proto.flow.execution.BlockHeaderResponse.serializeBinaryToWriter(this, writer);
    return writer.getResultBuffer();
};
/**
 * Serializes the given message to binary data (in protobuf wire
 * format), writing to the given BinaryWriter.
 * @param {!proto.flow.execution.BlockHeaderResponse} message
 * @param {!jspb.BinaryWriter} writer
 * @suppress {unusedLocalVariables} f is only used for nested messages
 */
proto.flow.execution.BlockHeaderResponse.serializeBinaryToWriter = function (message, writer) {
    var f = undefined;
    f = message.getBlock();
    if (f != null) {
        writer.writeMessage(1, f, flow_entities_block_header_pb.BlockHeader.serializeBinaryToWriter);
    }
};
/**
 * optional flow.entities.BlockHeader block = 1;
 * @return {?proto.flow.entities.BlockHeader}
 */
proto.flow.execution.BlockHeaderResponse.prototype.getBlock = function () {
    return /** @type{?proto.flow.entities.BlockHeader} */ (jspb.Message.getWrapperField(this, flow_entities_block_header_pb.BlockHeader, 1));
};
/**
 * @param {?proto.flow.entities.BlockHeader|undefined} value
 * @return {!proto.flow.execution.BlockHeaderResponse} returns this
*/
proto.flow.execution.BlockHeaderResponse.prototype.setBlock = function (value) {
    return jspb.Message.setWrapperField(this, 1, value);
};
/**
 * Clears the message field making it undefined.
 * @return {!proto.flow.execution.BlockHeaderResponse} returns this
 */
proto.flow.execution.BlockHeaderResponse.prototype.clearBlock = function () {
    return this.setBlock(undefined);
};
/**
 * Returns whether this field is set.
 * @return {boolean}
 */
proto.flow.execution.BlockHeaderResponse.prototype.hasBlock = function () {
    return jspb.Message.getField(this, 1) != null;
};

export const BlockHeaderResponse = proto.flow.execution.BlockHeaderResponse;
export const ExecuteScriptAtBlockIDRequest = proto.flow.execution.ExecuteScriptAtBlockIDRequest;
export const ExecuteScriptAtBlockIDResponse = proto.flow.execution.ExecuteScriptAtBlockIDResponse;
export const GetAccountAtBlockIDRequest = proto.flow.execution.GetAccountAtBlockIDRequest;
export const GetAccountAtBlockIDResponse = proto.flow.execution.GetAccountAtBlockIDResponse;
export const GetBlockHeaderByIDRequest = proto.flow.execution.GetBlockHeaderByIDRequest;
export const GetEventsForBlockIDsRequest = proto.flow.execution.GetEventsForBlockIDsRequest;
export const GetEventsForBlockIDsResponse = proto.flow.execution.GetEventsForBlockIDsResponse;
export const Result = proto.flow.execution.GetEventsForBlockIDsResponse.Result;
export const GetLatestBlockHeaderRequest = proto.flow.execution.GetLatestBlockHeaderRequest;
export const GetRegisterAtBlockIDRequest = proto.flow.execution.GetRegisterAtBlockIDRequest;
export const GetRegisterAtBlockIDResponse = proto.flow.execution.GetRegisterAtBlockIDResponse;
export const GetTransactionByIndexRequest = proto.flow.execution.GetTransactionByIndexRequest;
export const GetTransactionErrorMessageByIndexRequest = proto.flow.execution.GetTransactionErrorMessageByIndexRequest;
export const GetTransactionErrorMessageRequest = proto.flow.execution.GetTransactionErrorMessageRequest;
export const GetTransactionErrorMessageResponse = proto.flow.execution.GetTransactionErrorMessageResponse;
export const GetTransactionErrorMessagesByBlockIDRequest = proto.flow.execution.GetTransactionErrorMessagesByBlockIDRequest;
export const GetTransactionErrorMessagesResponse = proto.flow.execution.GetTransactionErrorMessagesResponse;
export const Result = proto.flow.execution.GetTransactionErrorMessagesResponse.Result;
export const GetTransactionResultRequest = proto.flow.execution.GetTransactionResultRequest;
export const GetTransactionResultResponse = proto.flow.execution.GetTransactionResultResponse;
export const GetTransactionResultsResponse = proto.flow.execution.GetTransactionResultsResponse;
export const GetTransactionsByBlockIDRequest = proto.flow.execution.GetTransactionsByBlockIDRequest;
export const PingRequest = proto.flow.execution.PingRequest;
export const PingResponse = proto.flow.execution.PingResponse;
