// source: flow/executiondata/executiondata.proto
/**
 * @fileoverview
 * @enhanceable
 * @suppress {missingRequire} reports error on implicit type usages.
 * @suppress {messageConventions} JS Compiler reports an error if a variable or
 *     field starts with 'MSG_' and isn't a translatable message.
 * @public
 */
// GENERATED CODE -- DO NOT EDIT!
/* eslint-disable */
// @ts-nocheck
import jspb from "google-protobuf";
const goog = jspb;
var global = (typeof globalThis !== 'undefined' && globalThis) ||
    (typeof window !== 'undefined' && window) ||
    (typeof global !== 'undefined' && global) ||
    (typeof self !== 'undefined' && self) ||
    (function () { return this; }).call(null) ||
    Function('return this')();
import flow_entities_block_execution_data_pb from "../../flow/entities/block_execution_data_pb.js";
import flow_entities_event_pb from "../../flow/entities/event_pb.js";
import flow_entities_register_pb from "../../flow/entities/register_pb.js";
import * as google_protobuf_timestamp_pb from "google-protobuf/google/protobuf/timestamp_pb.js";
/**
 * Generated by JsPbCodeGenerator.
 * @param {Array=} opt_data Optional initial data array, typically from a
 * server response, or constructed directly in Javascript. The array is used
 * in place and becomes part of the constructed object. It is not cloned.
 * If no data is provided, the constructed object will be empty, but still
 * valid.
 * @extends {jspb.Message}
 * @constructor
 */
proto.flow.executiondata.GetExecutionDataByBlockIDRequest = function (opt_data) {
    jspb.Message.initialize(this, opt_data, 0, -1, null, null);
};
if (goog.DEBUG && !COMPILED) {
    /**
     * @public
     * @override
     */
    proto.flow.executiondata.GetExecutionDataByBlockIDRequest.displayName = 'proto.flow.executiondata.GetExecutionDataByBlockIDRequest';
}
/**
 * Generated by JsPbCodeGenerator.
 * @param {Array=} opt_data Optional initial data array, typically from a
 * server response, or constructed directly in Javascript. The array is used
 * in place and becomes part of the constructed object. It is not cloned.
 * If no data is provided, the constructed object will be empty, but still
 * valid.
 * @extends {jspb.Message}
 * @constructor
 */
proto.flow.executiondata.GetExecutionDataByBlockIDResponse = function (opt_data) {
    jspb.Message.initialize(this, opt_data, 0, -1, null, null);
};
if (goog.DEBUG && !COMPILED) {
    /**
     * @public
     * @override
     */
    proto.flow.executiondata.GetExecutionDataByBlockIDResponse.displayName = 'proto.flow.executiondata.GetExecutionDataByBlockIDResponse';
}
/**
 * Generated by JsPbCodeGenerator.
 * @param {Array=} opt_data Optional initial data array, typically from a
 * server response, or constructed directly in Javascript. The array is used
 * in place and becomes part of the constructed object. It is not cloned.
 * If no data is provided, the constructed object will be empty, but still
 * valid.
 * @extends {jspb.Message}
 * @constructor
 */
proto.flow.executiondata.SubscribeExecutionDataRequest = function (opt_data) {
    jspb.Message.initialize(this, opt_data, 0, -1, null, null);
};
if (goog.DEBUG && !COMPILED) {
    /**
     * @public
     * @override
     */
    proto.flow.executiondata.SubscribeExecutionDataRequest.displayName = 'proto.flow.executiondata.SubscribeExecutionDataRequest';
}
/**
 * Generated by JsPbCodeGenerator.
 * @param {Array=} opt_data Optional initial data array, typically from a
 * server response, or constructed directly in Javascript. The array is used
 * in place and becomes part of the constructed object. It is not cloned.
 * If no data is provided, the constructed object will be empty, but still
 * valid.
 * @extends {jspb.Message}
 * @constructor
 */
proto.flow.executiondata.SubscribeExecutionDataResponse = function (opt_data) {
    jspb.Message.initialize(this, opt_data, 0, -1, null, null);
};
if (goog.DEBUG && !COMPILED) {
    /**
     * @public
     * @override
     */
    proto.flow.executiondata.SubscribeExecutionDataResponse.displayName = 'proto.flow.executiondata.SubscribeExecutionDataResponse';
}
/**
 * Generated by JsPbCodeGenerator.
 * @param {Array=} opt_data Optional initial data array, typically from a
 * server response, or constructed directly in Javascript. The array is used
 * in place and becomes part of the constructed object. It is not cloned.
 * If no data is provided, the constructed object will be empty, but still
 * valid.
 * @extends {jspb.Message}
 * @constructor
 */
proto.flow.executiondata.SubscribeEventsRequest = function (opt_data) {
    jspb.Message.initialize(this, opt_data, 0, -1, null, null);
};
if (goog.DEBUG && !COMPILED) {
    /**
     * @public
     * @override
     */
    proto.flow.executiondata.SubscribeEventsRequest.displayName = 'proto.flow.executiondata.SubscribeEventsRequest';
}
/**
 * Generated by JsPbCodeGenerator.
 * @param {Array=} opt_data Optional initial data array, typically from a
 * server response, or constructed directly in Javascript. The array is used
 * in place and becomes part of the constructed object. It is not cloned.
 * If no data is provided, the constructed object will be empty, but still
 * valid.
 * @extends {jspb.Message}
 * @constructor
 */
proto.flow.executiondata.SubscribeEventsResponse = function (opt_data) {
    jspb.Message.initialize(this, opt_data, 0, -1, proto.flow.executiondata.SubscribeEventsResponse.repeatedFields_, null);
};
if (goog.DEBUG && !COMPILED) {
    /**
     * @public
     * @override
     */
    proto.flow.executiondata.SubscribeEventsResponse.displayName = 'proto.flow.executiondata.SubscribeEventsResponse';
}
/**
 * Generated by JsPbCodeGenerator.
 * @param {Array=} opt_data Optional initial data array, typically from a
 * server response, or constructed directly in Javascript. The array is used
 * in place and becomes part of the constructed object. It is not cloned.
 * If no data is provided, the constructed object will be empty, but still
 * valid.
 * @extends {jspb.Message}
 * @constructor
 */
proto.flow.executiondata.EventFilter = function (opt_data) {
    jspb.Message.initialize(this, opt_data, 0, -1, proto.flow.executiondata.EventFilter.repeatedFields_, null);
};
if (goog.DEBUG && !COMPILED) {
    /**
     * @public
     * @override
     */
    proto.flow.executiondata.EventFilter.displayName = 'proto.flow.executiondata.EventFilter';
}
/**
 * Generated by JsPbCodeGenerator.
 * @param {Array=} opt_data Optional initial data array, typically from a
 * server response, or constructed directly in Javascript. The array is used
 * in place and becomes part of the constructed object. It is not cloned.
 * If no data is provided, the constructed object will be empty, but still
 * valid.
 * @extends {jspb.Message}
 * @constructor
 */
proto.flow.executiondata.GetRegisterValuesRequest = function (opt_data) {
    jspb.Message.initialize(this, opt_data, 0, -1, proto.flow.executiondata.GetRegisterValuesRequest.repeatedFields_, null);
};
if (goog.DEBUG && !COMPILED) {
    /**
     * @public
     * @override
     */
    proto.flow.executiondata.GetRegisterValuesRequest.displayName = 'proto.flow.executiondata.GetRegisterValuesRequest';
}
/**
 * Generated by JsPbCodeGenerator.
 * @param {Array=} opt_data Optional initial data array, typically from a
 * server response, or constructed directly in Javascript. The array is used
 * in place and becomes part of the constructed object. It is not cloned.
 * If no data is provided, the constructed object will be empty, but still
 * valid.
 * @extends {jspb.Message}
 * @constructor
 */
proto.flow.executiondata.GetRegisterValuesResponse = function (opt_data) {
    jspb.Message.initialize(this, opt_data, 0, -1, proto.flow.executiondata.GetRegisterValuesResponse.repeatedFields_, null);
};
if (goog.DEBUG && !COMPILED) {
    /**
     * @public
     * @override
     */
    proto.flow.executiondata.GetRegisterValuesResponse.displayName = 'proto.flow.executiondata.GetRegisterValuesResponse';
}
if (jspb.Message.GENERATE_TO_OBJECT) {
    /**
     * Creates an object representation of this proto.
     * Field names that are reserved in JavaScript and will be renamed to pb_name.
     * Optional fields that are not set will be set to undefined.
     * To access a reserved field use, foo.pb_<name>, eg, foo.pb_default.
     * For the list of reserved names please see:
     *     net/proto2/compiler/js/internal/generator.cc#kKeyword.
     * @param {boolean=} opt_includeInstance Deprecated. whether to include the
     *     JSPB instance for transitional soy proto support:
     *     http://goto/soy-param-migration
     * @return {!Object}
     */
    proto.flow.executiondata.GetExecutionDataByBlockIDRequest.prototype.toObject = function (opt_includeInstance) {
        return proto.flow.executiondata.GetExecutionDataByBlockIDRequest.toObject(opt_includeInstance, this);
    };
    /**
     * Static version of the {@see toObject} method.
     * @param {boolean|undefined} includeInstance Deprecated. Whether to include
     *     the JSPB instance for transitional soy proto support:
     *     http://goto/soy-param-migration
     * @param {!proto.flow.executiondata.GetExecutionDataByBlockIDRequest} msg The msg instance to transform.
     * @return {!Object}
     * @suppress {unusedLocalVariables} f is only used for nested messages
     */
    proto.flow.executiondata.GetExecutionDataByBlockIDRequest.toObject = function (includeInstance, msg) {
        var f, obj = {
            blockId: msg.getBlockId_asB64(),
            eventEncodingVersion: jspb.Message.getFieldWithDefault(msg, 2, 0)
        };
        if (includeInstance) {
            obj.$jspbMessageInstance = msg;
        }
        return obj;
    };
}
/**
 * Deserializes binary data (in protobuf wire format).
 * @param {jspb.ByteSource} bytes The bytes to deserialize.
 * @return {!proto.flow.executiondata.GetExecutionDataByBlockIDRequest}
 */
proto.flow.executiondata.GetExecutionDataByBlockIDRequest.deserializeBinary = function (bytes) {
    var reader = new jspb.BinaryReader(bytes);
    var msg = new proto.flow.executiondata.GetExecutionDataByBlockIDRequest;
    return proto.flow.executiondata.GetExecutionDataByBlockIDRequest.deserializeBinaryFromReader(msg, reader);
};
/**
 * Deserializes binary data (in protobuf wire format) from the
 * given reader into the given message object.
 * @param {!proto.flow.executiondata.GetExecutionDataByBlockIDRequest} msg The message object to deserialize into.
 * @param {!jspb.BinaryReader} reader The BinaryReader to use.
 * @return {!proto.flow.executiondata.GetExecutionDataByBlockIDRequest}
 */
proto.flow.executiondata.GetExecutionDataByBlockIDRequest.deserializeBinaryFromReader = function (msg, reader) {
    while (reader.nextField()) {
        if (reader.isEndGroup()) {
            break;
        }
        var field = reader.getFieldNumber();
        switch (field) {
            case 1:
                var value = /** @type {!Uint8Array} */ (reader.readBytes());
                msg.setBlockId(value);
                break;
            case 2:
                var value = /** @type {!proto.flow.entities.EventEncodingVersion} */ (reader.readEnum());
                msg.setEventEncodingVersion(value);
                break;
            default:
                reader.skipField();
                break;
        }
    }
    return msg;
};
/**
 * Serializes the message to binary data (in protobuf wire format).
 * @return {!Uint8Array}
 */
proto.flow.executiondata.GetExecutionDataByBlockIDRequest.prototype.serializeBinary = function () {
    var writer = new jspb.BinaryWriter();
    proto.flow.executiondata.GetExecutionDataByBlockIDRequest.serializeBinaryToWriter(this, writer);
    return writer.getResultBuffer();
};
/**
 * Serializes the given message to binary data (in protobuf wire
 * format), writing to the given BinaryWriter.
 * @param {!proto.flow.executiondata.GetExecutionDataByBlockIDRequest} message
 * @param {!jspb.BinaryWriter} writer
 * @suppress {unusedLocalVariables} f is only used for nested messages
 */
proto.flow.executiondata.GetExecutionDataByBlockIDRequest.serializeBinaryToWriter = function (message, writer) {
    var f = undefined;
    f = message.getBlockId_asU8();
    if (f.length > 0) {
        writer.writeBytes(1, f);
    }
    f = message.getEventEncodingVersion();
    if (f !== 0.0) {
        writer.writeEnum(2, f);
    }
};
/**
 * optional bytes block_id = 1;
 * @return {!(string|Uint8Array)}
 */
proto.flow.executiondata.GetExecutionDataByBlockIDRequest.prototype.getBlockId = function () {
    return /** @type {!(string|Uint8Array)} */ (jspb.Message.getFieldWithDefault(this, 1, ""));
};
/**
 * optional bytes block_id = 1;
 * This is a type-conversion wrapper around `getBlockId()`
 * @return {string}
 */
proto.flow.executiondata.GetExecutionDataByBlockIDRequest.prototype.getBlockId_asB64 = function () {
    return /** @type {string} */ (jspb.Message.bytesAsB64(this.getBlockId()));
};
/**
 * optional bytes block_id = 1;
 * Note that Uint8Array is not supported on all browsers.
 * @see http://caniuse.com/Uint8Array
 * This is a type-conversion wrapper around `getBlockId()`
 * @return {!Uint8Array}
 */
proto.flow.executiondata.GetExecutionDataByBlockIDRequest.prototype.getBlockId_asU8 = function () {
    return /** @type {!Uint8Array} */ (jspb.Message.bytesAsU8(this.getBlockId()));
};
/**
 * @param {!(string|Uint8Array)} value
 * @return {!proto.flow.executiondata.GetExecutionDataByBlockIDRequest} returns this
 */
proto.flow.executiondata.GetExecutionDataByBlockIDRequest.prototype.setBlockId = function (value) {
    return jspb.Message.setProto3BytesField(this, 1, value);
};
/**
 * optional flow.entities.EventEncodingVersion event_encoding_version = 2;
 * @return {!proto.flow.entities.EventEncodingVersion}
 */
proto.flow.executiondata.GetExecutionDataByBlockIDRequest.prototype.getEventEncodingVersion = function () {
    return /** @type {!proto.flow.entities.EventEncodingVersion} */ (jspb.Message.getFieldWithDefault(this, 2, 0));
};
/**
 * @param {!proto.flow.entities.EventEncodingVersion} value
 * @return {!proto.flow.executiondata.GetExecutionDataByBlockIDRequest} returns this
 */
proto.flow.executiondata.GetExecutionDataByBlockIDRequest.prototype.setEventEncodingVersion = function (value) {
    return jspb.Message.setProto3EnumField(this, 2, value);
};
if (jspb.Message.GENERATE_TO_OBJECT) {
    /**
     * Creates an object representation of this proto.
     * Field names that are reserved in JavaScript and will be renamed to pb_name.
     * Optional fields that are not set will be set to undefined.
     * To access a reserved field use, foo.pb_<name>, eg, foo.pb_default.
     * For the list of reserved names please see:
     *     net/proto2/compiler/js/internal/generator.cc#kKeyword.
     * @param {boolean=} opt_includeInstance Deprecated. whether to include the
     *     JSPB instance for transitional soy proto support:
     *     http://goto/soy-param-migration
     * @return {!Object}
     */
    proto.flow.executiondata.GetExecutionDataByBlockIDResponse.prototype.toObject = function (opt_includeInstance) {
        return proto.flow.executiondata.GetExecutionDataByBlockIDResponse.toObject(opt_includeInstance, this);
    };
    /**
     * Static version of the {@see toObject} method.
     * @param {boolean|undefined} includeInstance Deprecated. Whether to include
     *     the JSPB instance for transitional soy proto support:
     *     http://goto/soy-param-migration
     * @param {!proto.flow.executiondata.GetExecutionDataByBlockIDResponse} msg The msg instance to transform.
     * @return {!Object}
     * @suppress {unusedLocalVariables} f is only used for nested messages
     */
    proto.flow.executiondata.GetExecutionDataByBlockIDResponse.toObject = function (includeInstance, msg) {
        var f, obj = {
            blockExecutionData: (f = msg.getBlockExecutionData()) && flow_entities_block_execution_data_pb.BlockExecutionData.toObject(includeInstance, f)
        };
        if (includeInstance) {
            obj.$jspbMessageInstance = msg;
        }
        return obj;
    };
}
/**
 * Deserializes binary data (in protobuf wire format).
 * @param {jspb.ByteSource} bytes The bytes to deserialize.
 * @return {!proto.flow.executiondata.GetExecutionDataByBlockIDResponse}
 */
proto.flow.executiondata.GetExecutionDataByBlockIDResponse.deserializeBinary = function (bytes) {
    var reader = new jspb.BinaryReader(bytes);
    var msg = new proto.flow.executiondata.GetExecutionDataByBlockIDResponse;
    return proto.flow.executiondata.GetExecutionDataByBlockIDResponse.deserializeBinaryFromReader(msg, reader);
};
/**
 * Deserializes binary data (in protobuf wire format) from the
 * given reader into the given message object.
 * @param {!proto.flow.executiondata.GetExecutionDataByBlockIDResponse} msg The message object to deserialize into.
 * @param {!jspb.BinaryReader} reader The BinaryReader to use.
 * @return {!proto.flow.executiondata.GetExecutionDataByBlockIDResponse}
 */
proto.flow.executiondata.GetExecutionDataByBlockIDResponse.deserializeBinaryFromReader = function (msg, reader) {
    while (reader.nextField()) {
        if (reader.isEndGroup()) {
            break;
        }
        var field = reader.getFieldNumber();
        switch (field) {
            case 1:
                var value = new flow_entities_block_execution_data_pb.BlockExecutionData;
                reader.readMessage(value, flow_entities_block_execution_data_pb.BlockExecutionData.deserializeBinaryFromReader);
                msg.setBlockExecutionData(value);
                break;
            default:
                reader.skipField();
                break;
        }
    }
    return msg;
};
/**
 * Serializes the message to binary data (in protobuf wire format).
 * @return {!Uint8Array}
 */
proto.flow.executiondata.GetExecutionDataByBlockIDResponse.prototype.serializeBinary = function () {
    var writer = new jspb.BinaryWriter();
    proto.flow.executiondata.GetExecutionDataByBlockIDResponse.serializeBinaryToWriter(this, writer);
    return writer.getResultBuffer();
};
/**
 * Serializes the given message to binary data (in protobuf wire
 * format), writing to the given BinaryWriter.
 * @param {!proto.flow.executiondata.GetExecutionDataByBlockIDResponse} message
 * @param {!jspb.BinaryWriter} writer
 * @suppress {unusedLocalVariables} f is only used for nested messages
 */
proto.flow.executiondata.GetExecutionDataByBlockIDResponse.serializeBinaryToWriter = function (message, writer) {
    var f = undefined;
    f = message.getBlockExecutionData();
    if (f != null) {
        writer.writeMessage(1, f, flow_entities_block_execution_data_pb.BlockExecutionData.serializeBinaryToWriter);
    }
};
/**
 * optional flow.entities.BlockExecutionData block_execution_data = 1;
 * @return {?proto.flow.entities.BlockExecutionData}
 */
proto.flow.executiondata.GetExecutionDataByBlockIDResponse.prototype.getBlockExecutionData = function () {
    return /** @type{?proto.flow.entities.BlockExecutionData} */ (jspb.Message.getWrapperField(this, flow_entities_block_execution_data_pb.BlockExecutionData, 1));
};
/**
 * @param {?proto.flow.entities.BlockExecutionData|undefined} value
 * @return {!proto.flow.executiondata.GetExecutionDataByBlockIDResponse} returns this
*/
proto.flow.executiondata.GetExecutionDataByBlockIDResponse.prototype.setBlockExecutionData = function (value) {
    return jspb.Message.setWrapperField(this, 1, value);
};
/**
 * Clears the message field making it undefined.
 * @return {!proto.flow.executiondata.GetExecutionDataByBlockIDResponse} returns this
 */
proto.flow.executiondata.GetExecutionDataByBlockIDResponse.prototype.clearBlockExecutionData = function () {
    return this.setBlockExecutionData(undefined);
};
/**
 * Returns whether this field is set.
 * @return {boolean}
 */
proto.flow.executiondata.GetExecutionDataByBlockIDResponse.prototype.hasBlockExecutionData = function () {
    return jspb.Message.getField(this, 1) != null;
};
if (jspb.Message.GENERATE_TO_OBJECT) {
    /**
     * Creates an object representation of this proto.
     * Field names that are reserved in JavaScript and will be renamed to pb_name.
     * Optional fields that are not set will be set to undefined.
     * To access a reserved field use, foo.pb_<name>, eg, foo.pb_default.
     * For the list of reserved names please see:
     *     net/proto2/compiler/js/internal/generator.cc#kKeyword.
     * @param {boolean=} opt_includeInstance Deprecated. whether to include the
     *     JSPB instance for transitional soy proto support:
     *     http://goto/soy-param-migration
     * @return {!Object}
     */
    proto.flow.executiondata.SubscribeExecutionDataRequest.prototype.toObject = function (opt_includeInstance) {
        return proto.flow.executiondata.SubscribeExecutionDataRequest.toObject(opt_includeInstance, this);
    };
    /**
     * Static version of the {@see toObject} method.
     * @param {boolean|undefined} includeInstance Deprecated. Whether to include
     *     the JSPB instance for transitional soy proto support:
     *     http://goto/soy-param-migration
     * @param {!proto.flow.executiondata.SubscribeExecutionDataRequest} msg The msg instance to transform.
     * @return {!Object}
     * @suppress {unusedLocalVariables} f is only used for nested messages
     */
    proto.flow.executiondata.SubscribeExecutionDataRequest.toObject = function (includeInstance, msg) {
        var f, obj = {
            startBlockId: msg.getStartBlockId_asB64(),
            startBlockHeight: jspb.Message.getFieldWithDefault(msg, 2, 0),
            eventEncodingVersion: jspb.Message.getFieldWithDefault(msg, 3, 0)
        };
        if (includeInstance) {
            obj.$jspbMessageInstance = msg;
        }
        return obj;
    };
}
/**
 * Deserializes binary data (in protobuf wire format).
 * @param {jspb.ByteSource} bytes The bytes to deserialize.
 * @return {!proto.flow.executiondata.SubscribeExecutionDataRequest}
 */
proto.flow.executiondata.SubscribeExecutionDataRequest.deserializeBinary = function (bytes) {
    var reader = new jspb.BinaryReader(bytes);
    var msg = new proto.flow.executiondata.SubscribeExecutionDataRequest;
    return proto.flow.executiondata.SubscribeExecutionDataRequest.deserializeBinaryFromReader(msg, reader);
};
/**
 * Deserializes binary data (in protobuf wire format) from the
 * given reader into the given message object.
 * @param {!proto.flow.executiondata.SubscribeExecutionDataRequest} msg The message object to deserialize into.
 * @param {!jspb.BinaryReader} reader The BinaryReader to use.
 * @return {!proto.flow.executiondata.SubscribeExecutionDataRequest}
 */
proto.flow.executiondata.SubscribeExecutionDataRequest.deserializeBinaryFromReader = function (msg, reader) {
    while (reader.nextField()) {
        if (reader.isEndGroup()) {
            break;
        }
        var field = reader.getFieldNumber();
        switch (field) {
            case 1:
                var value = /** @type {!Uint8Array} */ (reader.readBytes());
                msg.setStartBlockId(value);
                break;
            case 2:
                var value = /** @type {number} */ (reader.readUint64());
                msg.setStartBlockHeight(value);
                break;
            case 3:
                var value = /** @type {!proto.flow.entities.EventEncodingVersion} */ (reader.readEnum());
                msg.setEventEncodingVersion(value);
                break;
            default:
                reader.skipField();
                break;
        }
    }
    return msg;
};
/**
 * Serializes the message to binary data (in protobuf wire format).
 * @return {!Uint8Array}
 */
proto.flow.executiondata.SubscribeExecutionDataRequest.prototype.serializeBinary = function () {
    var writer = new jspb.BinaryWriter();
    proto.flow.executiondata.SubscribeExecutionDataRequest.serializeBinaryToWriter(this, writer);
    return writer.getResultBuffer();
};
/**
 * Serializes the given message to binary data (in protobuf wire
 * format), writing to the given BinaryWriter.
 * @param {!proto.flow.executiondata.SubscribeExecutionDataRequest} message
 * @param {!jspb.BinaryWriter} writer
 * @suppress {unusedLocalVariables} f is only used for nested messages
 */
proto.flow.executiondata.SubscribeExecutionDataRequest.serializeBinaryToWriter = function (message, writer) {
    var f = undefined;
    f = message.getStartBlockId_asU8();
    if (f.length > 0) {
        writer.writeBytes(1, f);
    }
    f = message.getStartBlockHeight();
    if (f !== 0) {
        writer.writeUint64(2, f);
    }
    f = message.getEventEncodingVersion();
    if (f !== 0.0) {
        writer.writeEnum(3, f);
    }
};
/**
 * optional bytes start_block_id = 1;
 * @return {!(string|Uint8Array)}
 */
proto.flow.executiondata.SubscribeExecutionDataRequest.prototype.getStartBlockId = function () {
    return /** @type {!(string|Uint8Array)} */ (jspb.Message.getFieldWithDefault(this, 1, ""));
};
/**
 * optional bytes start_block_id = 1;
 * This is a type-conversion wrapper around `getStartBlockId()`
 * @return {string}
 */
proto.flow.executiondata.SubscribeExecutionDataRequest.prototype.getStartBlockId_asB64 = function () {
    return /** @type {string} */ (jspb.Message.bytesAsB64(this.getStartBlockId()));
};
/**
 * optional bytes start_block_id = 1;
 * Note that Uint8Array is not supported on all browsers.
 * @see http://caniuse.com/Uint8Array
 * This is a type-conversion wrapper around `getStartBlockId()`
 * @return {!Uint8Array}
 */
proto.flow.executiondata.SubscribeExecutionDataRequest.prototype.getStartBlockId_asU8 = function () {
    return /** @type {!Uint8Array} */ (jspb.Message.bytesAsU8(this.getStartBlockId()));
};
/**
 * @param {!(string|Uint8Array)} value
 * @return {!proto.flow.executiondata.SubscribeExecutionDataRequest} returns this
 */
proto.flow.executiondata.SubscribeExecutionDataRequest.prototype.setStartBlockId = function (value) {
    return jspb.Message.setProto3BytesField(this, 1, value);
};
/**
 * optional uint64 start_block_height = 2;
 * @return {number}
 */
proto.flow.executiondata.SubscribeExecutionDataRequest.prototype.getStartBlockHeight = function () {
    return /** @type {number} */ (jspb.Message.getFieldWithDefault(this, 2, 0));
};
/**
 * @param {number} value
 * @return {!proto.flow.executiondata.SubscribeExecutionDataRequest} returns this
 */
proto.flow.executiondata.SubscribeExecutionDataRequest.prototype.setStartBlockHeight = function (value) {
    return jspb.Message.setProto3IntField(this, 2, value);
};
/**
 * optional flow.entities.EventEncodingVersion event_encoding_version = 3;
 * @return {!proto.flow.entities.EventEncodingVersion}
 */
proto.flow.executiondata.SubscribeExecutionDataRequest.prototype.getEventEncodingVersion = function () {
    return /** @type {!proto.flow.entities.EventEncodingVersion} */ (jspb.Message.getFieldWithDefault(this, 3, 0));
};
/**
 * @param {!proto.flow.entities.EventEncodingVersion} value
 * @return {!proto.flow.executiondata.SubscribeExecutionDataRequest} returns this
 */
proto.flow.executiondata.SubscribeExecutionDataRequest.prototype.setEventEncodingVersion = function (value) {
    return jspb.Message.setProto3EnumField(this, 3, value);
};
if (jspb.Message.GENERATE_TO_OBJECT) {
    /**
     * Creates an object representation of this proto.
     * Field names that are reserved in JavaScript and will be renamed to pb_name.
     * Optional fields that are not set will be set to undefined.
     * To access a reserved field use, foo.pb_<name>, eg, foo.pb_default.
     * For the list of reserved names please see:
     *     net/proto2/compiler/js/internal/generator.cc#kKeyword.
     * @param {boolean=} opt_includeInstance Deprecated. whether to include the
     *     JSPB instance for transitional soy proto support:
     *     http://goto/soy-param-migration
     * @return {!Object}
     */
    proto.flow.executiondata.SubscribeExecutionDataResponse.prototype.toObject = function (opt_includeInstance) {
        return proto.flow.executiondata.SubscribeExecutionDataResponse.toObject(opt_includeInstance, this);
    };
    /**
     * Static version of the {@see toObject} method.
     * @param {boolean|undefined} includeInstance Deprecated. Whether to include
     *     the JSPB instance for transitional soy proto support:
     *     http://goto/soy-param-migration
     * @param {!proto.flow.executiondata.SubscribeExecutionDataResponse} msg The msg instance to transform.
     * @return {!Object}
     * @suppress {unusedLocalVariables} f is only used for nested messages
     */
    proto.flow.executiondata.SubscribeExecutionDataResponse.toObject = function (includeInstance, msg) {
        var f, obj = {
            blockHeight: jspb.Message.getFieldWithDefault(msg, 1, 0),
            blockExecutionData: (f = msg.getBlockExecutionData()) && flow_entities_block_execution_data_pb.BlockExecutionData.toObject(includeInstance, f),
            blockTimestamp: (f = msg.getBlockTimestamp()) && google_protobuf_timestamp_pb.Timestamp.toObject(includeInstance, f)
        };
        if (includeInstance) {
            obj.$jspbMessageInstance = msg;
        }
        return obj;
    };
}
/**
 * Deserializes binary data (in protobuf wire format).
 * @param {jspb.ByteSource} bytes The bytes to deserialize.
 * @return {!proto.flow.executiondata.SubscribeExecutionDataResponse}
 */
proto.flow.executiondata.SubscribeExecutionDataResponse.deserializeBinary = function (bytes) {
    var reader = new jspb.BinaryReader(bytes);
    var msg = new proto.flow.executiondata.SubscribeExecutionDataResponse;
    return proto.flow.executiondata.SubscribeExecutionDataResponse.deserializeBinaryFromReader(msg, reader);
};
/**
 * Deserializes binary data (in protobuf wire format) from the
 * given reader into the given message object.
 * @param {!proto.flow.executiondata.SubscribeExecutionDataResponse} msg The message object to deserialize into.
 * @param {!jspb.BinaryReader} reader The BinaryReader to use.
 * @return {!proto.flow.executiondata.SubscribeExecutionDataResponse}
 */
proto.flow.executiondata.SubscribeExecutionDataResponse.deserializeBinaryFromReader = function (msg, reader) {
    while (reader.nextField()) {
        if (reader.isEndGroup()) {
            break;
        }
        var field = reader.getFieldNumber();
        switch (field) {
            case 1:
                var value = /** @type {number} */ (reader.readUint64());
                msg.setBlockHeight(value);
                break;
            case 2:
                var value = new flow_entities_block_execution_data_pb.BlockExecutionData;
                reader.readMessage(value, flow_entities_block_execution_data_pb.BlockExecutionData.deserializeBinaryFromReader);
                msg.setBlockExecutionData(value);
                break;
            case 3:
                var value = new google_protobuf_timestamp_pb.Timestamp;
                reader.readMessage(value, google_protobuf_timestamp_pb.Timestamp.deserializeBinaryFromReader);
                msg.setBlockTimestamp(value);
                break;
            default:
                reader.skipField();
                break;
        }
    }
    return msg;
};
/**
 * Serializes the message to binary data (in protobuf wire format).
 * @return {!Uint8Array}
 */
proto.flow.executiondata.SubscribeExecutionDataResponse.prototype.serializeBinary = function () {
    var writer = new jspb.BinaryWriter();
    proto.flow.executiondata.SubscribeExecutionDataResponse.serializeBinaryToWriter(this, writer);
    return writer.getResultBuffer();
};
/**
 * Serializes the given message to binary data (in protobuf wire
 * format), writing to the given BinaryWriter.
 * @param {!proto.flow.executiondata.SubscribeExecutionDataResponse} message
 * @param {!jspb.BinaryWriter} writer
 * @suppress {unusedLocalVariables} f is only used for nested messages
 */
proto.flow.executiondata.SubscribeExecutionDataResponse.serializeBinaryToWriter = function (message, writer) {
    var f = undefined;
    f = message.getBlockHeight();
    if (f !== 0) {
        writer.writeUint64(1, f);
    }
    f = message.getBlockExecutionData();
    if (f != null) {
        writer.writeMessage(2, f, flow_entities_block_execution_data_pb.BlockExecutionData.serializeBinaryToWriter);
    }
    f = message.getBlockTimestamp();
    if (f != null) {
        writer.writeMessage(3, f, google_protobuf_timestamp_pb.Timestamp.serializeBinaryToWriter);
    }
};
/**
 * optional uint64 block_height = 1;
 * @return {number}
 */
proto.flow.executiondata.SubscribeExecutionDataResponse.prototype.getBlockHeight = function () {
    return /** @type {number} */ (jspb.Message.getFieldWithDefault(this, 1, 0));
};
/**
 * @param {number} value
 * @return {!proto.flow.executiondata.SubscribeExecutionDataResponse} returns this
 */
proto.flow.executiondata.SubscribeExecutionDataResponse.prototype.setBlockHeight = function (value) {
    return jspb.Message.setProto3IntField(this, 1, value);
};
/**
 * optional flow.entities.BlockExecutionData block_execution_data = 2;
 * @return {?proto.flow.entities.BlockExecutionData}
 */
proto.flow.executiondata.SubscribeExecutionDataResponse.prototype.getBlockExecutionData = function () {
    return /** @type{?proto.flow.entities.BlockExecutionData} */ (jspb.Message.getWrapperField(this, flow_entities_block_execution_data_pb.BlockExecutionData, 2));
};
/**
 * @param {?proto.flow.entities.BlockExecutionData|undefined} value
 * @return {!proto.flow.executiondata.SubscribeExecutionDataResponse} returns this
*/
proto.flow.executiondata.SubscribeExecutionDataResponse.prototype.setBlockExecutionData = function (value) {
    return jspb.Message.setWrapperField(this, 2, value);
};
/**
 * Clears the message field making it undefined.
 * @return {!proto.flow.executiondata.SubscribeExecutionDataResponse} returns this
 */
proto.flow.executiondata.SubscribeExecutionDataResponse.prototype.clearBlockExecutionData = function () {
    return this.setBlockExecutionData(undefined);
};
/**
 * Returns whether this field is set.
 * @return {boolean}
 */
proto.flow.executiondata.SubscribeExecutionDataResponse.prototype.hasBlockExecutionData = function () {
    return jspb.Message.getField(this, 2) != null;
};
/**
 * optional google.protobuf.Timestamp block_timestamp = 3;
 * @return {?proto.google.protobuf.Timestamp}
 */
proto.flow.executiondata.SubscribeExecutionDataResponse.prototype.getBlockTimestamp = function () {
    return /** @type{?proto.google.protobuf.Timestamp} */ (jspb.Message.getWrapperField(this, google_protobuf_timestamp_pb.Timestamp, 3));
};
/**
 * @param {?proto.google.protobuf.Timestamp|undefined} value
 * @return {!proto.flow.executiondata.SubscribeExecutionDataResponse} returns this
*/
proto.flow.executiondata.SubscribeExecutionDataResponse.prototype.setBlockTimestamp = function (value) {
    return jspb.Message.setWrapperField(this, 3, value);
};
/**
 * Clears the message field making it undefined.
 * @return {!proto.flow.executiondata.SubscribeExecutionDataResponse} returns this
 */
proto.flow.executiondata.SubscribeExecutionDataResponse.prototype.clearBlockTimestamp = function () {
    return this.setBlockTimestamp(undefined);
};
/**
 * Returns whether this field is set.
 * @return {boolean}
 */
proto.flow.executiondata.SubscribeExecutionDataResponse.prototype.hasBlockTimestamp = function () {
    return jspb.Message.getField(this, 3) != null;
};
if (jspb.Message.GENERATE_TO_OBJECT) {
    /**
     * Creates an object representation of this proto.
     * Field names that are reserved in JavaScript and will be renamed to pb_name.
     * Optional fields that are not set will be set to undefined.
     * To access a reserved field use, foo.pb_<name>, eg, foo.pb_default.
     * For the list of reserved names please see:
     *     net/proto2/compiler/js/internal/generator.cc#kKeyword.
     * @param {boolean=} opt_includeInstance Deprecated. whether to include the
     *     JSPB instance for transitional soy proto support:
     *     http://goto/soy-param-migration
     * @return {!Object}
     */
    proto.flow.executiondata.SubscribeEventsRequest.prototype.toObject = function (opt_includeInstance) {
        return proto.flow.executiondata.SubscribeEventsRequest.toObject(opt_includeInstance, this);
    };
    /**
     * Static version of the {@see toObject} method.
     * @param {boolean|undefined} includeInstance Deprecated. Whether to include
     *     the JSPB instance for transitional soy proto support:
     *     http://goto/soy-param-migration
     * @param {!proto.flow.executiondata.SubscribeEventsRequest} msg The msg instance to transform.
     * @return {!Object}
     * @suppress {unusedLocalVariables} f is only used for nested messages
     */
    proto.flow.executiondata.SubscribeEventsRequest.toObject = function (includeInstance, msg) {
        var f, obj = {
            startBlockId: msg.getStartBlockId_asB64(),
            startBlockHeight: jspb.Message.getFieldWithDefault(msg, 2, 0),
            filter: (f = msg.getFilter()) && proto.flow.executiondata.EventFilter.toObject(includeInstance, f),
            heartbeatInterval: jspb.Message.getFieldWithDefault(msg, 4, 0),
            eventEncodingVersion: jspb.Message.getFieldWithDefault(msg, 5, 0)
        };
        if (includeInstance) {
            obj.$jspbMessageInstance = msg;
        }
        return obj;
    };
}
/**
 * Deserializes binary data (in protobuf wire format).
 * @param {jspb.ByteSource} bytes The bytes to deserialize.
 * @return {!proto.flow.executiondata.SubscribeEventsRequest}
 */
proto.flow.executiondata.SubscribeEventsRequest.deserializeBinary = function (bytes) {
    var reader = new jspb.BinaryReader(bytes);
    var msg = new proto.flow.executiondata.SubscribeEventsRequest;
    return proto.flow.executiondata.SubscribeEventsRequest.deserializeBinaryFromReader(msg, reader);
};
/**
 * Deserializes binary data (in protobuf wire format) from the
 * given reader into the given message object.
 * @param {!proto.flow.executiondata.SubscribeEventsRequest} msg The message object to deserialize into.
 * @param {!jspb.BinaryReader} reader The BinaryReader to use.
 * @return {!proto.flow.executiondata.SubscribeEventsRequest}
 */
proto.flow.executiondata.SubscribeEventsRequest.deserializeBinaryFromReader = function (msg, reader) {
    while (reader.nextField()) {
        if (reader.isEndGroup()) {
            break;
        }
        var field = reader.getFieldNumber();
        switch (field) {
            case 1:
                var value = /** @type {!Uint8Array} */ (reader.readBytes());
                msg.setStartBlockId(value);
                break;
            case 2:
                var value = /** @type {number} */ (reader.readUint64());
                msg.setStartBlockHeight(value);
                break;
            case 3:
                var value = new proto.flow.executiondata.EventFilter;
                reader.readMessage(value, proto.flow.executiondata.EventFilter.deserializeBinaryFromReader);
                msg.setFilter(value);
                break;
            case 4:
                var value = /** @type {number} */ (reader.readUint64());
                msg.setHeartbeatInterval(value);
                break;
            case 5:
                var value = /** @type {!proto.flow.entities.EventEncodingVersion} */ (reader.readEnum());
                msg.setEventEncodingVersion(value);
                break;
            default:
                reader.skipField();
                break;
        }
    }
    return msg;
};
/**
 * Serializes the message to binary data (in protobuf wire format).
 * @return {!Uint8Array}
 */
proto.flow.executiondata.SubscribeEventsRequest.prototype.serializeBinary = function () {
    var writer = new jspb.BinaryWriter();
    proto.flow.executiondata.SubscribeEventsRequest.serializeBinaryToWriter(this, writer);
    return writer.getResultBuffer();
};
/**
 * Serializes the given message to binary data (in protobuf wire
 * format), writing to the given BinaryWriter.
 * @param {!proto.flow.executiondata.SubscribeEventsRequest} message
 * @param {!jspb.BinaryWriter} writer
 * @suppress {unusedLocalVariables} f is only used for nested messages
 */
proto.flow.executiondata.SubscribeEventsRequest.serializeBinaryToWriter = function (message, writer) {
    var f = undefined;
    f = message.getStartBlockId_asU8();
    if (f.length > 0) {
        writer.writeBytes(1, f);
    }
    f = message.getStartBlockHeight();
    if (f !== 0) {
        writer.writeUint64(2, f);
    }
    f = message.getFilter();
    if (f != null) {
        writer.writeMessage(3, f, proto.flow.executiondata.EventFilter.serializeBinaryToWriter);
    }
    f = message.getHeartbeatInterval();
    if (f !== 0) {
        writer.writeUint64(4, f);
    }
    f = message.getEventEncodingVersion();
    if (f !== 0.0) {
        writer.writeEnum(5, f);
    }
};
/**
 * optional bytes start_block_id = 1;
 * @return {!(string|Uint8Array)}
 */
proto.flow.executiondata.SubscribeEventsRequest.prototype.getStartBlockId = function () {
    return /** @type {!(string|Uint8Array)} */ (jspb.Message.getFieldWithDefault(this, 1, ""));
};
/**
 * optional bytes start_block_id = 1;
 * This is a type-conversion wrapper around `getStartBlockId()`
 * @return {string}
 */
proto.flow.executiondata.SubscribeEventsRequest.prototype.getStartBlockId_asB64 = function () {
    return /** @type {string} */ (jspb.Message.bytesAsB64(this.getStartBlockId()));
};
/**
 * optional bytes start_block_id = 1;
 * Note that Uint8Array is not supported on all browsers.
 * @see http://caniuse.com/Uint8Array
 * This is a type-conversion wrapper around `getStartBlockId()`
 * @return {!Uint8Array}
 */
proto.flow.executiondata.SubscribeEventsRequest.prototype.getStartBlockId_asU8 = function () {
    return /** @type {!Uint8Array} */ (jspb.Message.bytesAsU8(this.getStartBlockId()));
};
/**
 * @param {!(string|Uint8Array)} value
 * @return {!proto.flow.executiondata.SubscribeEventsRequest} returns this
 */
proto.flow.executiondata.SubscribeEventsRequest.prototype.setStartBlockId = function (value) {
    return jspb.Message.setProto3BytesField(this, 1, value);
};
/**
 * optional uint64 start_block_height = 2;
 * @return {number}
 */
proto.flow.executiondata.SubscribeEventsRequest.prototype.getStartBlockHeight = function () {
    return /** @type {number} */ (jspb.Message.getFieldWithDefault(this, 2, 0));
};
/**
 * @param {number} value
 * @return {!proto.flow.executiondata.SubscribeEventsRequest} returns this
 */
proto.flow.executiondata.SubscribeEventsRequest.prototype.setStartBlockHeight = function (value) {
    return jspb.Message.setProto3IntField(this, 2, value);
};
/**
 * optional EventFilter filter = 3;
 * @return {?proto.flow.executiondata.EventFilter}
 */
proto.flow.executiondata.SubscribeEventsRequest.prototype.getFilter = function () {
    return /** @type{?proto.flow.executiondata.EventFilter} */ (jspb.Message.getWrapperField(this, proto.flow.executiondata.EventFilter, 3));
};
/**
 * @param {?proto.flow.executiondata.EventFilter|undefined} value
 * @return {!proto.flow.executiondata.SubscribeEventsRequest} returns this
*/
proto.flow.executiondata.SubscribeEventsRequest.prototype.setFilter = function (value) {
    return jspb.Message.setWrapperField(this, 3, value);
};
/**
 * Clears the message field making it undefined.
 * @return {!proto.flow.executiondata.SubscribeEventsRequest} returns this
 */
proto.flow.executiondata.SubscribeEventsRequest.prototype.clearFilter = function () {
    return this.setFilter(undefined);
};
/**
 * Returns whether this field is set.
 * @return {boolean}
 */
proto.flow.executiondata.SubscribeEventsRequest.prototype.hasFilter = function () {
    return jspb.Message.getField(this, 3) != null;
};
/**
 * optional uint64 heartbeat_interval = 4;
 * @return {number}
 */
proto.flow.executiondata.SubscribeEventsRequest.prototype.getHeartbeatInterval = function () {
    return /** @type {number} */ (jspb.Message.getFieldWithDefault(this, 4, 0));
};
/**
 * @param {number} value
 * @return {!proto.flow.executiondata.SubscribeEventsRequest} returns this
 */
proto.flow.executiondata.SubscribeEventsRequest.prototype.setHeartbeatInterval = function (value) {
    return jspb.Message.setProto3IntField(this, 4, value);
};
/**
 * optional flow.entities.EventEncodingVersion event_encoding_version = 5;
 * @return {!proto.flow.entities.EventEncodingVersion}
 */
proto.flow.executiondata.SubscribeEventsRequest.prototype.getEventEncodingVersion = function () {
    return /** @type {!proto.flow.entities.EventEncodingVersion} */ (jspb.Message.getFieldWithDefault(this, 5, 0));
};
/**
 * @param {!proto.flow.entities.EventEncodingVersion} value
 * @return {!proto.flow.executiondata.SubscribeEventsRequest} returns this
 */
proto.flow.executiondata.SubscribeEventsRequest.prototype.setEventEncodingVersion = function (value) {
    return jspb.Message.setProto3EnumField(this, 5, value);
};
/**
 * List of repeated fields within this message type.
 * @private {!Array<number>}
 * @const
 */
proto.flow.executiondata.SubscribeEventsResponse.repeatedFields_ = [3];
if (jspb.Message.GENERATE_TO_OBJECT) {
    /**
     * Creates an object representation of this proto.
     * Field names that are reserved in JavaScript and will be renamed to pb_name.
     * Optional fields that are not set will be set to undefined.
     * To access a reserved field use, foo.pb_<name>, eg, foo.pb_default.
     * For the list of reserved names please see:
     *     net/proto2/compiler/js/internal/generator.cc#kKeyword.
     * @param {boolean=} opt_includeInstance Deprecated. whether to include the
     *     JSPB instance for transitional soy proto support:
     *     http://goto/soy-param-migration
     * @return {!Object}
     */
    proto.flow.executiondata.SubscribeEventsResponse.prototype.toObject = function (opt_includeInstance) {
        return proto.flow.executiondata.SubscribeEventsResponse.toObject(opt_includeInstance, this);
    };
    /**
     * Static version of the {@see toObject} method.
     * @param {boolean|undefined} includeInstance Deprecated. Whether to include
     *     the JSPB instance for transitional soy proto support:
     *     http://goto/soy-param-migration
     * @param {!proto.flow.executiondata.SubscribeEventsResponse} msg The msg instance to transform.
     * @return {!Object}
     * @suppress {unusedLocalVariables} f is only used for nested messages
     */
    proto.flow.executiondata.SubscribeEventsResponse.toObject = function (includeInstance, msg) {
        var f, obj = {
            blockId: msg.getBlockId_asB64(),
            blockHeight: jspb.Message.getFieldWithDefault(msg, 2, 0),
            eventsList: jspb.Message.toObjectList(msg.getEventsList(), flow_entities_event_pb.Event.toObject, includeInstance),
            blockTimestamp: (f = msg.getBlockTimestamp()) && google_protobuf_timestamp_pb.Timestamp.toObject(includeInstance, f)
        };
        if (includeInstance) {
            obj.$jspbMessageInstance = msg;
        }
        return obj;
    };
}
/**
 * Deserializes binary data (in protobuf wire format).
 * @param {jspb.ByteSource} bytes The bytes to deserialize.
 * @return {!proto.flow.executiondata.SubscribeEventsResponse}
 */
proto.flow.executiondata.SubscribeEventsResponse.deserializeBinary = function (bytes) {
    var reader = new jspb.BinaryReader(bytes);
    var msg = new proto.flow.executiondata.SubscribeEventsResponse;
    return proto.flow.executiondata.SubscribeEventsResponse.deserializeBinaryFromReader(msg, reader);
};
/**
 * Deserializes binary data (in protobuf wire format) from the
 * given reader into the given message object.
 * @param {!proto.flow.executiondata.SubscribeEventsResponse} msg The message object to deserialize into.
 * @param {!jspb.BinaryReader} reader The BinaryReader to use.
 * @return {!proto.flow.executiondata.SubscribeEventsResponse}
 */
proto.flow.executiondata.SubscribeEventsResponse.deserializeBinaryFromReader = function (msg, reader) {
    while (reader.nextField()) {
        if (reader.isEndGroup()) {
            break;
        }
        var field = reader.getFieldNumber();
        switch (field) {
            case 1:
                var value = /** @type {!Uint8Array} */ (reader.readBytes());
                msg.setBlockId(value);
                break;
            case 2:
                var value = /** @type {number} */ (reader.readUint64());
                msg.setBlockHeight(value);
                break;
            case 3:
                var value = new flow_entities_event_pb.Event;
                reader.readMessage(value, flow_entities_event_pb.Event.deserializeBinaryFromReader);
                msg.addEvents(value);
                break;
            case 4:
                var value = new google_protobuf_timestamp_pb.Timestamp;
                reader.readMessage(value, google_protobuf_timestamp_pb.Timestamp.deserializeBinaryFromReader);
                msg.setBlockTimestamp(value);
                break;
            default:
                reader.skipField();
                break;
        }
    }
    return msg;
};
/**
 * Serializes the message to binary data (in protobuf wire format).
 * @return {!Uint8Array}
 */
proto.flow.executiondata.SubscribeEventsResponse.prototype.serializeBinary = function () {
    var writer = new jspb.BinaryWriter();
    proto.flow.executiondata.SubscribeEventsResponse.serializeBinaryToWriter(this, writer);
    return writer.getResultBuffer();
};
/**
 * Serializes the given message to binary data (in protobuf wire
 * format), writing to the given BinaryWriter.
 * @param {!proto.flow.executiondata.SubscribeEventsResponse} message
 * @param {!jspb.BinaryWriter} writer
 * @suppress {unusedLocalVariables} f is only used for nested messages
 */
proto.flow.executiondata.SubscribeEventsResponse.serializeBinaryToWriter = function (message, writer) {
    var f = undefined;
    f = message.getBlockId_asU8();
    if (f.length > 0) {
        writer.writeBytes(1, f);
    }
    f = message.getBlockHeight();
    if (f !== 0) {
        writer.writeUint64(2, f);
    }
    f = message.getEventsList();
    if (f.length > 0) {
        writer.writeRepeatedMessage(3, f, flow_entities_event_pb.Event.serializeBinaryToWriter);
    }
    f = message.getBlockTimestamp();
    if (f != null) {
        writer.writeMessage(4, f, google_protobuf_timestamp_pb.Timestamp.serializeBinaryToWriter);
    }
};
/**
 * optional bytes block_id = 1;
 * @return {!(string|Uint8Array)}
 */
proto.flow.executiondata.SubscribeEventsResponse.prototype.getBlockId = function () {
    return /** @type {!(string|Uint8Array)} */ (jspb.Message.getFieldWithDefault(this, 1, ""));
};
/**
 * optional bytes block_id = 1;
 * This is a type-conversion wrapper around `getBlockId()`
 * @return {string}
 */
proto.flow.executiondata.SubscribeEventsResponse.prototype.getBlockId_asB64 = function () {
    return /** @type {string} */ (jspb.Message.bytesAsB64(this.getBlockId()));
};
/**
 * optional bytes block_id = 1;
 * Note that Uint8Array is not supported on all browsers.
 * @see http://caniuse.com/Uint8Array
 * This is a type-conversion wrapper around `getBlockId()`
 * @return {!Uint8Array}
 */
proto.flow.executiondata.SubscribeEventsResponse.prototype.getBlockId_asU8 = function () {
    return /** @type {!Uint8Array} */ (jspb.Message.bytesAsU8(this.getBlockId()));
};
/**
 * @param {!(string|Uint8Array)} value
 * @return {!proto.flow.executiondata.SubscribeEventsResponse} returns this
 */
proto.flow.executiondata.SubscribeEventsResponse.prototype.setBlockId = function (value) {
    return jspb.Message.setProto3BytesField(this, 1, value);
};
/**
 * optional uint64 block_height = 2;
 * @return {number}
 */
proto.flow.executiondata.SubscribeEventsResponse.prototype.getBlockHeight = function () {
    return /** @type {number} */ (jspb.Message.getFieldWithDefault(this, 2, 0));
};
/**
 * @param {number} value
 * @return {!proto.flow.executiondata.SubscribeEventsResponse} returns this
 */
proto.flow.executiondata.SubscribeEventsResponse.prototype.setBlockHeight = function (value) {
    return jspb.Message.setProto3IntField(this, 2, value);
};
/**
 * repeated flow.entities.Event events = 3;
 * @return {!Array<!proto.flow.entities.Event>}
 */
proto.flow.executiondata.SubscribeEventsResponse.prototype.getEventsList = function () {
    return /** @type{!Array<!proto.flow.entities.Event>} */ (jspb.Message.getRepeatedWrapperField(this, flow_entities_event_pb.Event, 3));
};
/**
 * @param {!Array<!proto.flow.entities.Event>} value
 * @return {!proto.flow.executiondata.SubscribeEventsResponse} returns this
*/
proto.flow.executiondata.SubscribeEventsResponse.prototype.setEventsList = function (value) {
    return jspb.Message.setRepeatedWrapperField(this, 3, value);
};
/**
 * @param {!proto.flow.entities.Event=} opt_value
 * @param {number=} opt_index
 * @return {!proto.flow.entities.Event}
 */
proto.flow.executiondata.SubscribeEventsResponse.prototype.addEvents = function (opt_value, opt_index) {
    return jspb.Message.addToRepeatedWrapperField(this, 3, opt_value, proto.flow.entities.Event, opt_index);
};
/**
 * Clears the list making it empty but non-null.
 * @return {!proto.flow.executiondata.SubscribeEventsResponse} returns this
 */
proto.flow.executiondata.SubscribeEventsResponse.prototype.clearEventsList = function () {
    return this.setEventsList([]);
};
/**
 * optional google.protobuf.Timestamp block_timestamp = 4;
 * @return {?proto.google.protobuf.Timestamp}
 */
proto.flow.executiondata.SubscribeEventsResponse.prototype.getBlockTimestamp = function () {
    return /** @type{?proto.google.protobuf.Timestamp} */ (jspb.Message.getWrapperField(this, google_protobuf_timestamp_pb.Timestamp, 4));
};
/**
 * @param {?proto.google.protobuf.Timestamp|undefined} value
 * @return {!proto.flow.executiondata.SubscribeEventsResponse} returns this
*/
proto.flow.executiondata.SubscribeEventsResponse.prototype.setBlockTimestamp = function (value) {
    return jspb.Message.setWrapperField(this, 4, value);
};
/**
 * Clears the message field making it undefined.
 * @return {!proto.flow.executiondata.SubscribeEventsResponse} returns this
 */
proto.flow.executiondata.SubscribeEventsResponse.prototype.clearBlockTimestamp = function () {
    return this.setBlockTimestamp(undefined);
};
/**
 * Returns whether this field is set.
 * @return {boolean}
 */
proto.flow.executiondata.SubscribeEventsResponse.prototype.hasBlockTimestamp = function () {
    return jspb.Message.getField(this, 4) != null;
};
/**
 * List of repeated fields within this message type.
 * @private {!Array<number>}
 * @const
 */
proto.flow.executiondata.EventFilter.repeatedFields_ = [1, 2, 3];
if (jspb.Message.GENERATE_TO_OBJECT) {
    /**
     * Creates an object representation of this proto.
     * Field names that are reserved in JavaScript and will be renamed to pb_name.
     * Optional fields that are not set will be set to undefined.
     * To access a reserved field use, foo.pb_<name>, eg, foo.pb_default.
     * For the list of reserved names please see:
     *     net/proto2/compiler/js/internal/generator.cc#kKeyword.
     * @param {boolean=} opt_includeInstance Deprecated. whether to include the
     *     JSPB instance for transitional soy proto support:
     *     http://goto/soy-param-migration
     * @return {!Object}
     */
    proto.flow.executiondata.EventFilter.prototype.toObject = function (opt_includeInstance) {
        return proto.flow.executiondata.EventFilter.toObject(opt_includeInstance, this);
    };
    /**
     * Static version of the {@see toObject} method.
     * @param {boolean|undefined} includeInstance Deprecated. Whether to include
     *     the JSPB instance for transitional soy proto support:
     *     http://goto/soy-param-migration
     * @param {!proto.flow.executiondata.EventFilter} msg The msg instance to transform.
     * @return {!Object}
     * @suppress {unusedLocalVariables} f is only used for nested messages
     */
    proto.flow.executiondata.EventFilter.toObject = function (includeInstance, msg) {
        var f, obj = {
            eventTypeList: (f = jspb.Message.getRepeatedField(msg, 1)) == null ? undefined : f,
            contractList: (f = jspb.Message.getRepeatedField(msg, 2)) == null ? undefined : f,
            addressList: (f = jspb.Message.getRepeatedField(msg, 3)) == null ? undefined : f
        };
        if (includeInstance) {
            obj.$jspbMessageInstance = msg;
        }
        return obj;
    };
}
/**
 * Deserializes binary data (in protobuf wire format).
 * @param {jspb.ByteSource} bytes The bytes to deserialize.
 * @return {!proto.flow.executiondata.EventFilter}
 */
proto.flow.executiondata.EventFilter.deserializeBinary = function (bytes) {
    var reader = new jspb.BinaryReader(bytes);
    var msg = new proto.flow.executiondata.EventFilter;
    return proto.flow.executiondata.EventFilter.deserializeBinaryFromReader(msg, reader);
};
/**
 * Deserializes binary data (in protobuf wire format) from the
 * given reader into the given message object.
 * @param {!proto.flow.executiondata.EventFilter} msg The message object to deserialize into.
 * @param {!jspb.BinaryReader} reader The BinaryReader to use.
 * @return {!proto.flow.executiondata.EventFilter}
 */
proto.flow.executiondata.EventFilter.deserializeBinaryFromReader = function (msg, reader) {
    while (reader.nextField()) {
        if (reader.isEndGroup()) {
            break;
        }
        var field = reader.getFieldNumber();
        switch (field) {
            case 1:
                var value = /** @type {string} */ (reader.readString());
                msg.addEventType(value);
                break;
            case 2:
                var value = /** @type {string} */ (reader.readString());
                msg.addContract(value);
                break;
            case 3:
                var value = /** @type {string} */ (reader.readString());
                msg.addAddress(value);
                break;
            default:
                reader.skipField();
                break;
        }
    }
    return msg;
};
/**
 * Serializes the message to binary data (in protobuf wire format).
 * @return {!Uint8Array}
 */
proto.flow.executiondata.EventFilter.prototype.serializeBinary = function () {
    var writer = new jspb.BinaryWriter();
    proto.flow.executiondata.EventFilter.serializeBinaryToWriter(this, writer);
    return writer.getResultBuffer();
};
/**
 * Serializes the given message to binary data (in protobuf wire
 * format), writing to the given BinaryWriter.
 * @param {!proto.flow.executiondata.EventFilter} message
 * @param {!jspb.BinaryWriter} writer
 * @suppress {unusedLocalVariables} f is only used for nested messages
 */
proto.flow.executiondata.EventFilter.serializeBinaryToWriter = function (message, writer) {
    var f = undefined;
    f = message.getEventTypeList();
    if (f.length > 0) {
        writer.writeRepeatedString(1, f);
    }
    f = message.getContractList();
    if (f.length > 0) {
        writer.writeRepeatedString(2, f);
    }
    f = message.getAddressList();
    if (f.length > 0) {
        writer.writeRepeatedString(3, f);
    }
};
/**
 * repeated string event_type = 1;
 * @return {!Array<string>}
 */
proto.flow.executiondata.EventFilter.prototype.getEventTypeList = function () {
    return /** @type {!Array<string>} */ (jspb.Message.getRepeatedField(this, 1));
};
/**
 * @param {!Array<string>} value
 * @return {!proto.flow.executiondata.EventFilter} returns this
 */
proto.flow.executiondata.EventFilter.prototype.setEventTypeList = function (value) {
    return jspb.Message.setField(this, 1, value || []);
};
/**
 * @param {string} value
 * @param {number=} opt_index
 * @return {!proto.flow.executiondata.EventFilter} returns this
 */
proto.flow.executiondata.EventFilter.prototype.addEventType = function (value, opt_index) {
    return jspb.Message.addToRepeatedField(this, 1, value, opt_index);
};
/**
 * Clears the list making it empty but non-null.
 * @return {!proto.flow.executiondata.EventFilter} returns this
 */
proto.flow.executiondata.EventFilter.prototype.clearEventTypeList = function () {
    return this.setEventTypeList([]);
};
/**
 * repeated string contract = 2;
 * @return {!Array<string>}
 */
proto.flow.executiondata.EventFilter.prototype.getContractList = function () {
    return /** @type {!Array<string>} */ (jspb.Message.getRepeatedField(this, 2));
};
/**
 * @param {!Array<string>} value
 * @return {!proto.flow.executiondata.EventFilter} returns this
 */
proto.flow.executiondata.EventFilter.prototype.setContractList = function (value) {
    return jspb.Message.setField(this, 2, value || []);
};
/**
 * @param {string} value
 * @param {number=} opt_index
 * @return {!proto.flow.executiondata.EventFilter} returns this
 */
proto.flow.executiondata.EventFilter.prototype.addContract = function (value, opt_index) {
    return jspb.Message.addToRepeatedField(this, 2, value, opt_index);
};
/**
 * Clears the list making it empty but non-null.
 * @return {!proto.flow.executiondata.EventFilter} returns this
 */
proto.flow.executiondata.EventFilter.prototype.clearContractList = function () {
    return this.setContractList([]);
};
/**
 * repeated string address = 3;
 * @return {!Array<string>}
 */
proto.flow.executiondata.EventFilter.prototype.getAddressList = function () {
    return /** @type {!Array<string>} */ (jspb.Message.getRepeatedField(this, 3));
};
/**
 * @param {!Array<string>} value
 * @return {!proto.flow.executiondata.EventFilter} returns this
 */
proto.flow.executiondata.EventFilter.prototype.setAddressList = function (value) {
    return jspb.Message.setField(this, 3, value || []);
};
/**
 * @param {string} value
 * @param {number=} opt_index
 * @return {!proto.flow.executiondata.EventFilter} returns this
 */
proto.flow.executiondata.EventFilter.prototype.addAddress = function (value, opt_index) {
    return jspb.Message.addToRepeatedField(this, 3, value, opt_index);
};
/**
 * Clears the list making it empty but non-null.
 * @return {!proto.flow.executiondata.EventFilter} returns this
 */
proto.flow.executiondata.EventFilter.prototype.clearAddressList = function () {
    return this.setAddressList([]);
};
/**
 * List of repeated fields within this message type.
 * @private {!Array<number>}
 * @const
 */
proto.flow.executiondata.GetRegisterValuesRequest.repeatedFields_ = [2];
if (jspb.Message.GENERATE_TO_OBJECT) {
    /**
     * Creates an object representation of this proto.
     * Field names that are reserved in JavaScript and will be renamed to pb_name.
     * Optional fields that are not set will be set to undefined.
     * To access a reserved field use, foo.pb_<name>, eg, foo.pb_default.
     * For the list of reserved names please see:
     *     net/proto2/compiler/js/internal/generator.cc#kKeyword.
     * @param {boolean=} opt_includeInstance Deprecated. whether to include the
     *     JSPB instance for transitional soy proto support:
     *     http://goto/soy-param-migration
     * @return {!Object}
     */
    proto.flow.executiondata.GetRegisterValuesRequest.prototype.toObject = function (opt_includeInstance) {
        return proto.flow.executiondata.GetRegisterValuesRequest.toObject(opt_includeInstance, this);
    };
    /**
     * Static version of the {@see toObject} method.
     * @param {boolean|undefined} includeInstance Deprecated. Whether to include
     *     the JSPB instance for transitional soy proto support:
     *     http://goto/soy-param-migration
     * @param {!proto.flow.executiondata.GetRegisterValuesRequest} msg The msg instance to transform.
     * @return {!Object}
     * @suppress {unusedLocalVariables} f is only used for nested messages
     */
    proto.flow.executiondata.GetRegisterValuesRequest.toObject = function (includeInstance, msg) {
        var f, obj = {
            blockHeight: jspb.Message.getFieldWithDefault(msg, 1, 0),
            registerIdsList: jspb.Message.toObjectList(msg.getRegisterIdsList(), flow_entities_register_pb.RegisterID.toObject, includeInstance)
        };
        if (includeInstance) {
            obj.$jspbMessageInstance = msg;
        }
        return obj;
    };
}
/**
 * Deserializes binary data (in protobuf wire format).
 * @param {jspb.ByteSource} bytes The bytes to deserialize.
 * @return {!proto.flow.executiondata.GetRegisterValuesRequest}
 */
proto.flow.executiondata.GetRegisterValuesRequest.deserializeBinary = function (bytes) {
    var reader = new jspb.BinaryReader(bytes);
    var msg = new proto.flow.executiondata.GetRegisterValuesRequest;
    return proto.flow.executiondata.GetRegisterValuesRequest.deserializeBinaryFromReader(msg, reader);
};
/**
 * Deserializes binary data (in protobuf wire format) from the
 * given reader into the given message object.
 * @param {!proto.flow.executiondata.GetRegisterValuesRequest} msg The message object to deserialize into.
 * @param {!jspb.BinaryReader} reader The BinaryReader to use.
 * @return {!proto.flow.executiondata.GetRegisterValuesRequest}
 */
proto.flow.executiondata.GetRegisterValuesRequest.deserializeBinaryFromReader = function (msg, reader) {
    while (reader.nextField()) {
        if (reader.isEndGroup()) {
            break;
        }
        var field = reader.getFieldNumber();
        switch (field) {
            case 1:
                var value = /** @type {number} */ (reader.readUint64());
                msg.setBlockHeight(value);
                break;
            case 2:
                var value = new flow_entities_register_pb.RegisterID;
                reader.readMessage(value, flow_entities_register_pb.RegisterID.deserializeBinaryFromReader);
                msg.addRegisterIds(value);
                break;
            default:
                reader.skipField();
                break;
        }
    }
    return msg;
};
/**
 * Serializes the message to binary data (in protobuf wire format).
 * @return {!Uint8Array}
 */
proto.flow.executiondata.GetRegisterValuesRequest.prototype.serializeBinary = function () {
    var writer = new jspb.BinaryWriter();
    proto.flow.executiondata.GetRegisterValuesRequest.serializeBinaryToWriter(this, writer);
    return writer.getResultBuffer();
};
/**
 * Serializes the given message to binary data (in protobuf wire
 * format), writing to the given BinaryWriter.
 * @param {!proto.flow.executiondata.GetRegisterValuesRequest} message
 * @param {!jspb.BinaryWriter} writer
 * @suppress {unusedLocalVariables} f is only used for nested messages
 */
proto.flow.executiondata.GetRegisterValuesRequest.serializeBinaryToWriter = function (message, writer) {
    var f = undefined;
    f = message.getBlockHeight();
    if (f !== 0) {
        writer.writeUint64(1, f);
    }
    f = message.getRegisterIdsList();
    if (f.length > 0) {
        writer.writeRepeatedMessage(2, f, flow_entities_register_pb.RegisterID.serializeBinaryToWriter);
    }
};
/**
 * optional uint64 block_height = 1;
 * @return {number}
 */
proto.flow.executiondata.GetRegisterValuesRequest.prototype.getBlockHeight = function () {
    return /** @type {number} */ (jspb.Message.getFieldWithDefault(this, 1, 0));
};
/**
 * @param {number} value
 * @return {!proto.flow.executiondata.GetRegisterValuesRequest} returns this
 */
proto.flow.executiondata.GetRegisterValuesRequest.prototype.setBlockHeight = function (value) {
    return jspb.Message.setProto3IntField(this, 1, value);
};
/**
 * repeated flow.entities.RegisterID register_ids = 2;
 * @return {!Array<!proto.flow.entities.RegisterID>}
 */
proto.flow.executiondata.GetRegisterValuesRequest.prototype.getRegisterIdsList = function () {
    return /** @type{!Array<!proto.flow.entities.RegisterID>} */ (jspb.Message.getRepeatedWrapperField(this, flow_entities_register_pb.RegisterID, 2));
};
/**
 * @param {!Array<!proto.flow.entities.RegisterID>} value
 * @return {!proto.flow.executiondata.GetRegisterValuesRequest} returns this
*/
proto.flow.executiondata.GetRegisterValuesRequest.prototype.setRegisterIdsList = function (value) {
    return jspb.Message.setRepeatedWrapperField(this, 2, value);
};
/**
 * @param {!proto.flow.entities.RegisterID=} opt_value
 * @param {number=} opt_index
 * @return {!proto.flow.entities.RegisterID}
 */
proto.flow.executiondata.GetRegisterValuesRequest.prototype.addRegisterIds = function (opt_value, opt_index) {
    return jspb.Message.addToRepeatedWrapperField(this, 2, opt_value, proto.flow.entities.RegisterID, opt_index);
};
/**
 * Clears the list making it empty but non-null.
 * @return {!proto.flow.executiondata.GetRegisterValuesRequest} returns this
 */
proto.flow.executiondata.GetRegisterValuesRequest.prototype.clearRegisterIdsList = function () {
    return this.setRegisterIdsList([]);
};
/**
 * List of repeated fields within this message type.
 * @private {!Array<number>}
 * @const
 */
proto.flow.executiondata.GetRegisterValuesResponse.repeatedFields_ = [1];
if (jspb.Message.GENERATE_TO_OBJECT) {
    /**
     * Creates an object representation of this proto.
     * Field names that are reserved in JavaScript and will be renamed to pb_name.
     * Optional fields that are not set will be set to undefined.
     * To access a reserved field use, foo.pb_<name>, eg, foo.pb_default.
     * For the list of reserved names please see:
     *     net/proto2/compiler/js/internal/generator.cc#kKeyword.
     * @param {boolean=} opt_includeInstance Deprecated. whether to include the
     *     JSPB instance for transitional soy proto support:
     *     http://goto/soy-param-migration
     * @return {!Object}
     */
    proto.flow.executiondata.GetRegisterValuesResponse.prototype.toObject = function (opt_includeInstance) {
        return proto.flow.executiondata.GetRegisterValuesResponse.toObject(opt_includeInstance, this);
    };
    /**
     * Static version of the {@see toObject} method.
     * @param {boolean|undefined} includeInstance Deprecated. Whether to include
     *     the JSPB instance for transitional soy proto support:
     *     http://goto/soy-param-migration
     * @param {!proto.flow.executiondata.GetRegisterValuesResponse} msg The msg instance to transform.
     * @return {!Object}
     * @suppress {unusedLocalVariables} f is only used for nested messages
     */
    proto.flow.executiondata.GetRegisterValuesResponse.toObject = function (includeInstance, msg) {
        var f, obj = {
            valuesList: msg.getValuesList_asB64()
        };
        if (includeInstance) {
            obj.$jspbMessageInstance = msg;
        }
        return obj;
    };
}
/**
 * Deserializes binary data (in protobuf wire format).
 * @param {jspb.ByteSource} bytes The bytes to deserialize.
 * @return {!proto.flow.executiondata.GetRegisterValuesResponse}
 */
proto.flow.executiondata.GetRegisterValuesResponse.deserializeBinary = function (bytes) {
    var reader = new jspb.BinaryReader(bytes);
    var msg = new proto.flow.executiondata.GetRegisterValuesResponse;
    return proto.flow.executiondata.GetRegisterValuesResponse.deserializeBinaryFromReader(msg, reader);
};
/**
 * Deserializes binary data (in protobuf wire format) from the
 * given reader into the given message object.
 * @param {!proto.flow.executiondata.GetRegisterValuesResponse} msg The message object to deserialize into.
 * @param {!jspb.BinaryReader} reader The BinaryReader to use.
 * @return {!proto.flow.executiondata.GetRegisterValuesResponse}
 */
proto.flow.executiondata.GetRegisterValuesResponse.deserializeBinaryFromReader = function (msg, reader) {
    while (reader.nextField()) {
        if (reader.isEndGroup()) {
            break;
        }
        var field = reader.getFieldNumber();
        switch (field) {
            case 1:
                var value = /** @type {!Uint8Array} */ (reader.readBytes());
                msg.addValues(value);
                break;
            default:
                reader.skipField();
                break;
        }
    }
    return msg;
};
/**
 * Serializes the message to binary data (in protobuf wire format).
 * @return {!Uint8Array}
 */
proto.flow.executiondata.GetRegisterValuesResponse.prototype.serializeBinary = function () {
    var writer = new jspb.BinaryWriter();
    proto.flow.executiondata.GetRegisterValuesResponse.serializeBinaryToWriter(this, writer);
    return writer.getResultBuffer();
};
/**
 * Serializes the given message to binary data (in protobuf wire
 * format), writing to the given BinaryWriter.
 * @param {!proto.flow.executiondata.GetRegisterValuesResponse} message
 * @param {!jspb.BinaryWriter} writer
 * @suppress {unusedLocalVariables} f is only used for nested messages
 */
proto.flow.executiondata.GetRegisterValuesResponse.serializeBinaryToWriter = function (message, writer) {
    var f = undefined;
    f = message.getValuesList_asU8();
    if (f.length > 0) {
        writer.writeRepeatedBytes(1, f);
    }
};
/**
 * repeated bytes values = 1;
 * @return {!(Array<!Uint8Array>|Array<string>)}
 */
proto.flow.executiondata.GetRegisterValuesResponse.prototype.getValuesList = function () {
    return /** @type {!(Array<!Uint8Array>|Array<string>)} */ (jspb.Message.getRepeatedField(this, 1));
};
/**
 * repeated bytes values = 1;
 * This is a type-conversion wrapper around `getValuesList()`
 * @return {!Array<string>}
 */
proto.flow.executiondata.GetRegisterValuesResponse.prototype.getValuesList_asB64 = function () {
    return /** @type {!Array<string>} */ (jspb.Message.bytesListAsB64(this.getValuesList()));
};
/**
 * repeated bytes values = 1;
 * Note that Uint8Array is not supported on all browsers.
 * @see http://caniuse.com/Uint8Array
 * This is a type-conversion wrapper around `getValuesList()`
 * @return {!Array<!Uint8Array>}
 */
proto.flow.executiondata.GetRegisterValuesResponse.prototype.getValuesList_asU8 = function () {
    return /** @type {!Array<!Uint8Array>} */ (jspb.Message.bytesListAsU8(this.getValuesList()));
};
/**
 * @param {!(Array<!Uint8Array>|Array<string>)} value
 * @return {!proto.flow.executiondata.GetRegisterValuesResponse} returns this
 */
proto.flow.executiondata.GetRegisterValuesResponse.prototype.setValuesList = function (value) {
    return jspb.Message.setField(this, 1, value || []);
};
/**
 * @param {!(string|Uint8Array)} value
 * @param {number=} opt_index
 * @return {!proto.flow.executiondata.GetRegisterValuesResponse} returns this
 */
proto.flow.executiondata.GetRegisterValuesResponse.prototype.addValues = function (value, opt_index) {
    return jspb.Message.addToRepeatedField(this, 1, value, opt_index);
};
/**
 * Clears the list making it empty but non-null.
 * @return {!proto.flow.executiondata.GetRegisterValuesResponse} returns this
 */
proto.flow.executiondata.GetRegisterValuesResponse.prototype.clearValuesList = function () {
    return this.setValuesList([]);
};

export const EventFilter = proto.flow.executiondata.EventFilter;
export const GetExecutionDataByBlockIDRequest = proto.flow.executiondata.GetExecutionDataByBlockIDRequest;
export const GetExecutionDataByBlockIDResponse = proto.flow.executiondata.GetExecutionDataByBlockIDResponse;
export const GetRegisterValuesRequest = proto.flow.executiondata.GetRegisterValuesRequest;
export const GetRegisterValuesResponse = proto.flow.executiondata.GetRegisterValuesResponse;
export const SubscribeEventsRequest = proto.flow.executiondata.SubscribeEventsRequest;
export const SubscribeEventsResponse = proto.flow.executiondata.SubscribeEventsResponse;
export const SubscribeExecutionDataRequest = proto.flow.executiondata.SubscribeExecutionDataRequest;
export const SubscribeExecutionDataResponse = proto.flow.executiondata.SubscribeExecutionDataResponse;
