// source: flow/entities/block.proto
/**
 * @fileoverview
 * @enhanceable
 * @suppress {missingRequire} reports error on implicit type usages.
 * @suppress {messageConventions} JS Compiler reports an error if a variable or
 *     field starts with 'MSG_' and isn't a translatable message.
 * @public
 */
// GENERATED CODE -- DO NOT EDIT!
/* eslint-disable */
// @ts-nocheck
import jspb from "google-protobuf";
const goog = jspb;
var global = (typeof globalThis !== 'undefined' && globalThis) ||
    (typeof window !== 'undefined' && window) ||
    (typeof global !== 'undefined' && global) ||
    (typeof self !== 'undefined' && self) ||
    (function () { return this; }).call(null) ||
    Function('return this')();
import google_protobuf_timestamp_pb from "google-protobuf/google/protobuf/timestamp_pb.js";
import flow_entities_collection_pb from "../../flow/entities/collection_pb.js";
import flow_entities_block_seal_pb from "../../flow/entities/block_seal_pb.js";
import flow_entities_execution_result_pb from "../../flow/entities/execution_result_pb.js";
import flow_entities_block_header_pb from "../../flow/entities/block_header_pb.js";
/**
 * Generated by JsPbCodeGenerator.
 * @param {Array=} opt_data Optional initial data array, typically from a
 * server response, or constructed directly in Javascript. The array is used
 * in place and becomes part of the constructed object. It is not cloned.
 * If no data is provided, the constructed object will be empty, but still
 * valid.
 * @extends {jspb.Message}
 * @constructor
 */
proto.flow.entities.Block = function (opt_data) {
    jspb.Message.initialize(this, opt_data, 0, -1, proto.flow.entities.Block.repeatedFields_, null);
};
if (goog.DEBUG && !COMPILED) {
    /**
     * @public
     * @override
     */
    proto.flow.entities.Block.displayName = 'proto.flow.entities.Block';
}
/**
 * List of repeated fields within this message type.
 * @private {!Array<number>}
 * @const
 */
proto.flow.entities.Block.repeatedFields_ = [5, 6, 7, 8, 9];
if (jspb.Message.GENERATE_TO_OBJECT) {
    /**
     * Creates an object representation of this proto.
     * Field names that are reserved in JavaScript and will be renamed to pb_name.
     * Optional fields that are not set will be set to undefined.
     * To access a reserved field use, foo.pb_<name>, eg, foo.pb_default.
     * For the list of reserved names please see:
     *     net/proto2/compiler/js/internal/generator.cc#kKeyword.
     * @param {boolean=} opt_includeInstance Deprecated. whether to include the
     *     JSPB instance for transitional soy proto support:
     *     http://goto/soy-param-migration
     * @return {!Object}
     */
    proto.flow.entities.Block.prototype.toObject = function (opt_includeInstance) {
        return proto.flow.entities.Block.toObject(opt_includeInstance, this);
    };
    /**
     * Static version of the {@see toObject} method.
     * @param {boolean|undefined} includeInstance Deprecated. Whether to include
     *     the JSPB instance for transitional soy proto support:
     *     http://goto/soy-param-migration
     * @param {!proto.flow.entities.Block} msg The msg instance to transform.
     * @return {!Object}
     * @suppress {unusedLocalVariables} f is only used for nested messages
     */
    proto.flow.entities.Block.toObject = function (includeInstance, msg) {
        var f, obj = {
            id: msg.getId_asB64(),
            parentId: msg.getParentId_asB64(),
            height: jspb.Message.getFieldWithDefault(msg, 3, 0),
            timestamp: (f = msg.getTimestamp()) && google_protobuf_timestamp_pb.Timestamp.toObject(includeInstance, f),
            collectionGuaranteesList: jspb.Message.toObjectList(msg.getCollectionGuaranteesList(), flow_entities_collection_pb.CollectionGuarantee.toObject, includeInstance),
            blockSealsList: jspb.Message.toObjectList(msg.getBlockSealsList(), flow_entities_block_seal_pb.BlockSeal.toObject, includeInstance),
            signaturesList: msg.getSignaturesList_asB64(),
            executionReceiptMetalistList: jspb.Message.toObjectList(msg.getExecutionReceiptMetalistList(), flow_entities_execution_result_pb.ExecutionReceiptMeta.toObject, includeInstance),
            executionResultListList: jspb.Message.toObjectList(msg.getExecutionResultListList(), flow_entities_execution_result_pb.ExecutionResult.toObject, includeInstance),
            blockHeader: (f = msg.getBlockHeader()) && flow_entities_block_header_pb.BlockHeader.toObject(includeInstance, f),
            protocolStateId: msg.getProtocolStateId_asB64()
        };
        if (includeInstance) {
            obj.$jspbMessageInstance = msg;
        }
        return obj;
    };
}
/**
 * Deserializes binary data (in protobuf wire format).
 * @param {jspb.ByteSource} bytes The bytes to deserialize.
 * @return {!proto.flow.entities.Block}
 */
proto.flow.entities.Block.deserializeBinary = function (bytes) {
    var reader = new jspb.BinaryReader(bytes);
    var msg = new proto.flow.entities.Block;
    return proto.flow.entities.Block.deserializeBinaryFromReader(msg, reader);
};
/**
 * Deserializes binary data (in protobuf wire format) from the
 * given reader into the given message object.
 * @param {!proto.flow.entities.Block} msg The message object to deserialize into.
 * @param {!jspb.BinaryReader} reader The BinaryReader to use.
 * @return {!proto.flow.entities.Block}
 */
proto.flow.entities.Block.deserializeBinaryFromReader = function (msg, reader) {
    while (reader.nextField()) {
        if (reader.isEndGroup()) {
            break;
        }
        var field = reader.getFieldNumber();
        switch (field) {
            case 1:
                var value = /** @type {!Uint8Array} */ (reader.readBytes());
                msg.setId(value);
                break;
            case 2:
                var value = /** @type {!Uint8Array} */ (reader.readBytes());
                msg.setParentId(value);
                break;
            case 3:
                var value = /** @type {number} */ (reader.readUint64());
                msg.setHeight(value);
                break;
            case 4:
                var value = new google_protobuf_timestamp_pb.Timestamp;
                reader.readMessage(value, google_protobuf_timestamp_pb.Timestamp.deserializeBinaryFromReader);
                msg.setTimestamp(value);
                break;
            case 5:
                var value = new flow_entities_collection_pb.CollectionGuarantee;
                reader.readMessage(value, flow_entities_collection_pb.CollectionGuarantee.deserializeBinaryFromReader);
                msg.addCollectionGuarantees(value);
                break;
            case 6:
                var value = new flow_entities_block_seal_pb.BlockSeal;
                reader.readMessage(value, flow_entities_block_seal_pb.BlockSeal.deserializeBinaryFromReader);
                msg.addBlockSeals(value);
                break;
            case 7:
                var value = /** @type {!Uint8Array} */ (reader.readBytes());
                msg.addSignatures(value);
                break;
            case 8:
                var value = new flow_entities_execution_result_pb.ExecutionReceiptMeta;
                reader.readMessage(value, flow_entities_execution_result_pb.ExecutionReceiptMeta.deserializeBinaryFromReader);
                msg.addExecutionReceiptMetalist(value);
                break;
            case 9:
                var value = new flow_entities_execution_result_pb.ExecutionResult;
                reader.readMessage(value, flow_entities_execution_result_pb.ExecutionResult.deserializeBinaryFromReader);
                msg.addExecutionResultList(value);
                break;
            case 10:
                var value = new flow_entities_block_header_pb.BlockHeader;
                reader.readMessage(value, flow_entities_block_header_pb.BlockHeader.deserializeBinaryFromReader);
                msg.setBlockHeader(value);
                break;
            case 11:
                var value = /** @type {!Uint8Array} */ (reader.readBytes());
                msg.setProtocolStateId(value);
                break;
            default:
                reader.skipField();
                break;
        }
    }
    return msg;
};
/**
 * Serializes the message to binary data (in protobuf wire format).
 * @return {!Uint8Array}
 */
proto.flow.entities.Block.prototype.serializeBinary = function () {
    var writer = new jspb.BinaryWriter();
    proto.flow.entities.Block.serializeBinaryToWriter(this, writer);
    return writer.getResultBuffer();
};
/**
 * Serializes the given message to binary data (in protobuf wire
 * format), writing to the given BinaryWriter.
 * @param {!proto.flow.entities.Block} message
 * @param {!jspb.BinaryWriter} writer
 * @suppress {unusedLocalVariables} f is only used for nested messages
 */
proto.flow.entities.Block.serializeBinaryToWriter = function (message, writer) {
    var f = undefined;
    f = message.getId_asU8();
    if (f.length > 0) {
        writer.writeBytes(1, f);
    }
    f = message.getParentId_asU8();
    if (f.length > 0) {
        writer.writeBytes(2, f);
    }
    f = message.getHeight();
    if (f !== 0) {
        writer.writeUint64(3, f);
    }
    f = message.getTimestamp();
    if (f != null) {
        writer.writeMessage(4, f, google_protobuf_timestamp_pb.Timestamp.serializeBinaryToWriter);
    }
    f = message.getCollectionGuaranteesList();
    if (f.length > 0) {
        writer.writeRepeatedMessage(5, f, flow_entities_collection_pb.CollectionGuarantee.serializeBinaryToWriter);
    }
    f = message.getBlockSealsList();
    if (f.length > 0) {
        writer.writeRepeatedMessage(6, f, flow_entities_block_seal_pb.BlockSeal.serializeBinaryToWriter);
    }
    f = message.getSignaturesList_asU8();
    if (f.length > 0) {
        writer.writeRepeatedBytes(7, f);
    }
    f = message.getExecutionReceiptMetalistList();
    if (f.length > 0) {
        writer.writeRepeatedMessage(8, f, flow_entities_execution_result_pb.ExecutionReceiptMeta.serializeBinaryToWriter);
    }
    f = message.getExecutionResultListList();
    if (f.length > 0) {
        writer.writeRepeatedMessage(9, f, flow_entities_execution_result_pb.ExecutionResult.serializeBinaryToWriter);
    }
    f = message.getBlockHeader();
    if (f != null) {
        writer.writeMessage(10, f, flow_entities_block_header_pb.BlockHeader.serializeBinaryToWriter);
    }
    f = message.getProtocolStateId_asU8();
    if (f.length > 0) {
        writer.writeBytes(11, f);
    }
};
/**
 * optional bytes id = 1;
 * @return {!(string|Uint8Array)}
 */
proto.flow.entities.Block.prototype.getId = function () {
    return /** @type {!(string|Uint8Array)} */ (jspb.Message.getFieldWithDefault(this, 1, ""));
};
/**
 * optional bytes id = 1;
 * This is a type-conversion wrapper around `getId()`
 * @return {string}
 */
proto.flow.entities.Block.prototype.getId_asB64 = function () {
    return /** @type {string} */ (jspb.Message.bytesAsB64(this.getId()));
};
/**
 * optional bytes id = 1;
 * Note that Uint8Array is not supported on all browsers.
 * @see http://caniuse.com/Uint8Array
 * This is a type-conversion wrapper around `getId()`
 * @return {!Uint8Array}
 */
proto.flow.entities.Block.prototype.getId_asU8 = function () {
    return /** @type {!Uint8Array} */ (jspb.Message.bytesAsU8(this.getId()));
};
/**
 * @param {!(string|Uint8Array)} value
 * @return {!proto.flow.entities.Block} returns this
 */
proto.flow.entities.Block.prototype.setId = function (value) {
    return jspb.Message.setProto3BytesField(this, 1, value);
};
/**
 * optional bytes parent_id = 2;
 * @return {!(string|Uint8Array)}
 */
proto.flow.entities.Block.prototype.getParentId = function () {
    return /** @type {!(string|Uint8Array)} */ (jspb.Message.getFieldWithDefault(this, 2, ""));
};
/**
 * optional bytes parent_id = 2;
 * This is a type-conversion wrapper around `getParentId()`
 * @return {string}
 */
proto.flow.entities.Block.prototype.getParentId_asB64 = function () {
    return /** @type {string} */ (jspb.Message.bytesAsB64(this.getParentId()));
};
/**
 * optional bytes parent_id = 2;
 * Note that Uint8Array is not supported on all browsers.
 * @see http://caniuse.com/Uint8Array
 * This is a type-conversion wrapper around `getParentId()`
 * @return {!Uint8Array}
 */
proto.flow.entities.Block.prototype.getParentId_asU8 = function () {
    return /** @type {!Uint8Array} */ (jspb.Message.bytesAsU8(this.getParentId()));
};
/**
 * @param {!(string|Uint8Array)} value
 * @return {!proto.flow.entities.Block} returns this
 */
proto.flow.entities.Block.prototype.setParentId = function (value) {
    return jspb.Message.setProto3BytesField(this, 2, value);
};
/**
 * optional uint64 height = 3;
 * @return {number}
 */
proto.flow.entities.Block.prototype.getHeight = function () {
    return /** @type {number} */ (jspb.Message.getFieldWithDefault(this, 3, 0));
};
/**
 * @param {number} value
 * @return {!proto.flow.entities.Block} returns this
 */
proto.flow.entities.Block.prototype.setHeight = function (value) {
    return jspb.Message.setProto3IntField(this, 3, value);
};
/**
 * optional google.protobuf.Timestamp timestamp = 4;
 * @return {?proto.google.protobuf.Timestamp}
 */
proto.flow.entities.Block.prototype.getTimestamp = function () {
    return /** @type{?proto.google.protobuf.Timestamp} */ (jspb.Message.getWrapperField(this, google_protobuf_timestamp_pb.Timestamp, 4));
};
/**
 * @param {?proto.google.protobuf.Timestamp|undefined} value
 * @return {!proto.flow.entities.Block} returns this
*/
proto.flow.entities.Block.prototype.setTimestamp = function (value) {
    return jspb.Message.setWrapperField(this, 4, value);
};
/**
 * Clears the message field making it undefined.
 * @return {!proto.flow.entities.Block} returns this
 */
proto.flow.entities.Block.prototype.clearTimestamp = function () {
    return this.setTimestamp(undefined);
};
/**
 * Returns whether this field is set.
 * @return {boolean}
 */
proto.flow.entities.Block.prototype.hasTimestamp = function () {
    return jspb.Message.getField(this, 4) != null;
};
/**
 * repeated CollectionGuarantee collection_guarantees = 5;
 * @return {!Array<!proto.flow.entities.CollectionGuarantee>}
 */
proto.flow.entities.Block.prototype.getCollectionGuaranteesList = function () {
    return /** @type{!Array<!proto.flow.entities.CollectionGuarantee>} */ (jspb.Message.getRepeatedWrapperField(this, flow_entities_collection_pb.CollectionGuarantee, 5));
};
/**
 * @param {!Array<!proto.flow.entities.CollectionGuarantee>} value
 * @return {!proto.flow.entities.Block} returns this
*/
proto.flow.entities.Block.prototype.setCollectionGuaranteesList = function (value) {
    return jspb.Message.setRepeatedWrapperField(this, 5, value);
};
/**
 * @param {!proto.flow.entities.CollectionGuarantee=} opt_value
 * @param {number=} opt_index
 * @return {!proto.flow.entities.CollectionGuarantee}
 */
proto.flow.entities.Block.prototype.addCollectionGuarantees = function (opt_value, opt_index) {
    return jspb.Message.addToRepeatedWrapperField(this, 5, opt_value, proto.flow.entities.CollectionGuarantee, opt_index);
};
/**
 * Clears the list making it empty but non-null.
 * @return {!proto.flow.entities.Block} returns this
 */
proto.flow.entities.Block.prototype.clearCollectionGuaranteesList = function () {
    return this.setCollectionGuaranteesList([]);
};
/**
 * repeated BlockSeal block_seals = 6;
 * @return {!Array<!proto.flow.entities.BlockSeal>}
 */
proto.flow.entities.Block.prototype.getBlockSealsList = function () {
    return /** @type{!Array<!proto.flow.entities.BlockSeal>} */ (jspb.Message.getRepeatedWrapperField(this, flow_entities_block_seal_pb.BlockSeal, 6));
};
/**
 * @param {!Array<!proto.flow.entities.BlockSeal>} value
 * @return {!proto.flow.entities.Block} returns this
*/
proto.flow.entities.Block.prototype.setBlockSealsList = function (value) {
    return jspb.Message.setRepeatedWrapperField(this, 6, value);
};
/**
 * @param {!proto.flow.entities.BlockSeal=} opt_value
 * @param {number=} opt_index
 * @return {!proto.flow.entities.BlockSeal}
 */
proto.flow.entities.Block.prototype.addBlockSeals = function (opt_value, opt_index) {
    return jspb.Message.addToRepeatedWrapperField(this, 6, opt_value, proto.flow.entities.BlockSeal, opt_index);
};
/**
 * Clears the list making it empty but non-null.
 * @return {!proto.flow.entities.Block} returns this
 */
proto.flow.entities.Block.prototype.clearBlockSealsList = function () {
    return this.setBlockSealsList([]);
};
/**
 * repeated bytes signatures = 7;
 * @return {!(Array<!Uint8Array>|Array<string>)}
 */
proto.flow.entities.Block.prototype.getSignaturesList = function () {
    return /** @type {!(Array<!Uint8Array>|Array<string>)} */ (jspb.Message.getRepeatedField(this, 7));
};
/**
 * repeated bytes signatures = 7;
 * This is a type-conversion wrapper around `getSignaturesList()`
 * @return {!Array<string>}
 */
proto.flow.entities.Block.prototype.getSignaturesList_asB64 = function () {
    return /** @type {!Array<string>} */ (jspb.Message.bytesListAsB64(this.getSignaturesList()));
};
/**
 * repeated bytes signatures = 7;
 * Note that Uint8Array is not supported on all browsers.
 * @see http://caniuse.com/Uint8Array
 * This is a type-conversion wrapper around `getSignaturesList()`
 * @return {!Array<!Uint8Array>}
 */
proto.flow.entities.Block.prototype.getSignaturesList_asU8 = function () {
    return /** @type {!Array<!Uint8Array>} */ (jspb.Message.bytesListAsU8(this.getSignaturesList()));
};
/**
 * @param {!(Array<!Uint8Array>|Array<string>)} value
 * @return {!proto.flow.entities.Block} returns this
 */
proto.flow.entities.Block.prototype.setSignaturesList = function (value) {
    return jspb.Message.setField(this, 7, value || []);
};
/**
 * @param {!(string|Uint8Array)} value
 * @param {number=} opt_index
 * @return {!proto.flow.entities.Block} returns this
 */
proto.flow.entities.Block.prototype.addSignatures = function (value, opt_index) {
    return jspb.Message.addToRepeatedField(this, 7, value, opt_index);
};
/**
 * Clears the list making it empty but non-null.
 * @return {!proto.flow.entities.Block} returns this
 */
proto.flow.entities.Block.prototype.clearSignaturesList = function () {
    return this.setSignaturesList([]);
};
/**
 * repeated ExecutionReceiptMeta execution_receipt_metaList = 8;
 * @return {!Array<!proto.flow.entities.ExecutionReceiptMeta>}
 */
proto.flow.entities.Block.prototype.getExecutionReceiptMetalistList = function () {
    return /** @type{!Array<!proto.flow.entities.ExecutionReceiptMeta>} */ (jspb.Message.getRepeatedWrapperField(this, flow_entities_execution_result_pb.ExecutionReceiptMeta, 8));
};
/**
 * @param {!Array<!proto.flow.entities.ExecutionReceiptMeta>} value
 * @return {!proto.flow.entities.Block} returns this
*/
proto.flow.entities.Block.prototype.setExecutionReceiptMetalistList = function (value) {
    return jspb.Message.setRepeatedWrapperField(this, 8, value);
};
/**
 * @param {!proto.flow.entities.ExecutionReceiptMeta=} opt_value
 * @param {number=} opt_index
 * @return {!proto.flow.entities.ExecutionReceiptMeta}
 */
proto.flow.entities.Block.prototype.addExecutionReceiptMetalist = function (opt_value, opt_index) {
    return jspb.Message.addToRepeatedWrapperField(this, 8, opt_value, proto.flow.entities.ExecutionReceiptMeta, opt_index);
};
/**
 * Clears the list making it empty but non-null.
 * @return {!proto.flow.entities.Block} returns this
 */
proto.flow.entities.Block.prototype.clearExecutionReceiptMetalistList = function () {
    return this.setExecutionReceiptMetalistList([]);
};
/**
 * repeated ExecutionResult execution_result_list = 9;
 * @return {!Array<!proto.flow.entities.ExecutionResult>}
 */
proto.flow.entities.Block.prototype.getExecutionResultListList = function () {
    return /** @type{!Array<!proto.flow.entities.ExecutionResult>} */ (jspb.Message.getRepeatedWrapperField(this, flow_entities_execution_result_pb.ExecutionResult, 9));
};
/**
 * @param {!Array<!proto.flow.entities.ExecutionResult>} value
 * @return {!proto.flow.entities.Block} returns this
*/
proto.flow.entities.Block.prototype.setExecutionResultListList = function (value) {
    return jspb.Message.setRepeatedWrapperField(this, 9, value);
};
/**
 * @param {!proto.flow.entities.ExecutionResult=} opt_value
 * @param {number=} opt_index
 * @return {!proto.flow.entities.ExecutionResult}
 */
proto.flow.entities.Block.prototype.addExecutionResultList = function (opt_value, opt_index) {
    return jspb.Message.addToRepeatedWrapperField(this, 9, opt_value, proto.flow.entities.ExecutionResult, opt_index);
};
/**
 * Clears the list making it empty but non-null.
 * @return {!proto.flow.entities.Block} returns this
 */
proto.flow.entities.Block.prototype.clearExecutionResultListList = function () {
    return this.setExecutionResultListList([]);
};
/**
 * optional BlockHeader block_header = 10;
 * @return {?proto.flow.entities.BlockHeader}
 */
proto.flow.entities.Block.prototype.getBlockHeader = function () {
    return /** @type{?proto.flow.entities.BlockHeader} */ (jspb.Message.getWrapperField(this, flow_entities_block_header_pb.BlockHeader, 10));
};
/**
 * @param {?proto.flow.entities.BlockHeader|undefined} value
 * @return {!proto.flow.entities.Block} returns this
*/
proto.flow.entities.Block.prototype.setBlockHeader = function (value) {
    return jspb.Message.setWrapperField(this, 10, value);
};
/**
 * Clears the message field making it undefined.
 * @return {!proto.flow.entities.Block} returns this
 */
proto.flow.entities.Block.prototype.clearBlockHeader = function () {
    return this.setBlockHeader(undefined);
};
/**
 * Returns whether this field is set.
 * @return {boolean}
 */
proto.flow.entities.Block.prototype.hasBlockHeader = function () {
    return jspb.Message.getField(this, 10) != null;
};
/**
 * optional bytes protocol_state_id = 11;
 * @return {!(string|Uint8Array)}
 */
proto.flow.entities.Block.prototype.getProtocolStateId = function () {
    return /** @type {!(string|Uint8Array)} */ (jspb.Message.getFieldWithDefault(this, 11, ""));
};
/**
 * optional bytes protocol_state_id = 11;
 * This is a type-conversion wrapper around `getProtocolStateId()`
 * @return {string}
 */
proto.flow.entities.Block.prototype.getProtocolStateId_asB64 = function () {
    return /** @type {string} */ (jspb.Message.bytesAsB64(this.getProtocolStateId()));
};
/**
 * optional bytes protocol_state_id = 11;
 * Note that Uint8Array is not supported on all browsers.
 * @see http://caniuse.com/Uint8Array
 * This is a type-conversion wrapper around `getProtocolStateId()`
 * @return {!Uint8Array}
 */
proto.flow.entities.Block.prototype.getProtocolStateId_asU8 = function () {
    return /** @type {!Uint8Array} */ (jspb.Message.bytesAsU8(this.getProtocolStateId()));
};
/**
 * @param {!(string|Uint8Array)} value
 * @return {!proto.flow.entities.Block} returns this
 */
proto.flow.entities.Block.prototype.setProtocolStateId = function (value) {
    return jspb.Message.setProto3BytesField(this, 11, value);
};
/**
 * @enum {number}
 */
proto.flow.entities.BlockStatus = {
    BLOCK_UNKNOWN: 0,
    BLOCK_FINALIZED: 1,
    BLOCK_SEALED: 2
};

export const Block = proto.flow.entities.Block;
export const BlockStatus = proto.flow.entities.BlockStatus;
