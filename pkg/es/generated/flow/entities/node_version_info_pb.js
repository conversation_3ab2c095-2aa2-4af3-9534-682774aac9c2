// source: flow/entities/node_version_info.proto
/**
 * @fileoverview
 * @enhanceable
 * @suppress {missingRequire} reports error on implicit type usages.
 * @suppress {messageConventions} JS Compiler reports an error if a variable or
 *     field starts with 'MSG_' and isn't a translatable message.
 * @public
 */
// GENERATED CODE -- DO NOT EDIT!
/* eslint-disable */
// @ts-nocheck
import jspb from "google-protobuf";
const goog = jspb;
var global = (typeof globalThis !== 'undefined' && globalThis) ||
    (typeof window !== 'undefined' && window) ||
    (typeof global !== 'undefined' && global) ||
    (typeof self !== 'undefined' && self) ||
    (function () { return this; }).call(null) ||
    Function('return this')();
/**
 * Generated by JsPbCodeGenerator.
 * @param {Array=} opt_data Optional initial data array, typically from a
 * server response, or constructed directly in Javascript. The array is used
 * in place and becomes part of the constructed object. It is not cloned.
 * If no data is provided, the constructed object will be empty, but still
 * valid.
 * @extends {jspb.Message}
 * @constructor
 */
proto.flow.entities.NodeVersionInfo = function (opt_data) {
    jspb.Message.initialize(this, opt_data, 0, -1, null, null);
};
if (goog.DEBUG && !COMPILED) {
    /**
     * @public
     * @override
     */
    proto.flow.entities.NodeVersionInfo.displayName = 'proto.flow.entities.NodeVersionInfo';
}
if (jspb.Message.GENERATE_TO_OBJECT) {
    /**
     * Creates an object representation of this proto.
     * Field names that are reserved in JavaScript and will be renamed to pb_name.
     * Optional fields that are not set will be set to undefined.
     * To access a reserved field use, foo.pb_<name>, eg, foo.pb_default.
     * For the list of reserved names please see:
     *     net/proto2/compiler/js/internal/generator.cc#kKeyword.
     * @param {boolean=} opt_includeInstance Deprecated. whether to include the
     *     JSPB instance for transitional soy proto support:
     *     http://goto/soy-param-migration
     * @return {!Object}
     */
    proto.flow.entities.NodeVersionInfo.prototype.toObject = function (opt_includeInstance) {
        return proto.flow.entities.NodeVersionInfo.toObject(opt_includeInstance, this);
    };
    /**
     * Static version of the {@see toObject} method.
     * @param {boolean|undefined} includeInstance Deprecated. Whether to include
     *     the JSPB instance for transitional soy proto support:
     *     http://goto/soy-param-migration
     * @param {!proto.flow.entities.NodeVersionInfo} msg The msg instance to transform.
     * @return {!Object}
     * @suppress {unusedLocalVariables} f is only used for nested messages
     */
    proto.flow.entities.NodeVersionInfo.toObject = function (includeInstance, msg) {
        var f, obj = {
            semver: jspb.Message.getFieldWithDefault(msg, 1, ""),
            commit: jspb.Message.getFieldWithDefault(msg, 2, ""),
            sporkId: msg.getSporkId_asB64(),
            protocolVersion: jspb.Message.getFieldWithDefault(msg, 4, 0),
            sporkRootBlockHeight: jspb.Message.getFieldWithDefault(msg, 5, 0),
            nodeRootBlockHeight: jspb.Message.getFieldWithDefault(msg, 6, 0)
        };
        if (includeInstance) {
            obj.$jspbMessageInstance = msg;
        }
        return obj;
    };
}
/**
 * Deserializes binary data (in protobuf wire format).
 * @param {jspb.ByteSource} bytes The bytes to deserialize.
 * @return {!proto.flow.entities.NodeVersionInfo}
 */
proto.flow.entities.NodeVersionInfo.deserializeBinary = function (bytes) {
    var reader = new jspb.BinaryReader(bytes);
    var msg = new proto.flow.entities.NodeVersionInfo;
    return proto.flow.entities.NodeVersionInfo.deserializeBinaryFromReader(msg, reader);
};
/**
 * Deserializes binary data (in protobuf wire format) from the
 * given reader into the given message object.
 * @param {!proto.flow.entities.NodeVersionInfo} msg The message object to deserialize into.
 * @param {!jspb.BinaryReader} reader The BinaryReader to use.
 * @return {!proto.flow.entities.NodeVersionInfo}
 */
proto.flow.entities.NodeVersionInfo.deserializeBinaryFromReader = function (msg, reader) {
    while (reader.nextField()) {
        if (reader.isEndGroup()) {
            break;
        }
        var field = reader.getFieldNumber();
        switch (field) {
            case 1:
                var value = /** @type {string} */ (reader.readString());
                msg.setSemver(value);
                break;
            case 2:
                var value = /** @type {string} */ (reader.readString());
                msg.setCommit(value);
                break;
            case 3:
                var value = /** @type {!Uint8Array} */ (reader.readBytes());
                msg.setSporkId(value);
                break;
            case 4:
                var value = /** @type {number} */ (reader.readUint64());
                msg.setProtocolVersion(value);
                break;
            case 5:
                var value = /** @type {number} */ (reader.readUint64());
                msg.setSporkRootBlockHeight(value);
                break;
            case 6:
                var value = /** @type {number} */ (reader.readUint64());
                msg.setNodeRootBlockHeight(value);
                break;
            default:
                reader.skipField();
                break;
        }
    }
    return msg;
};
/**
 * Serializes the message to binary data (in protobuf wire format).
 * @return {!Uint8Array}
 */
proto.flow.entities.NodeVersionInfo.prototype.serializeBinary = function () {
    var writer = new jspb.BinaryWriter();
    proto.flow.entities.NodeVersionInfo.serializeBinaryToWriter(this, writer);
    return writer.getResultBuffer();
};
/**
 * Serializes the given message to binary data (in protobuf wire
 * format), writing to the given BinaryWriter.
 * @param {!proto.flow.entities.NodeVersionInfo} message
 * @param {!jspb.BinaryWriter} writer
 * @suppress {unusedLocalVariables} f is only used for nested messages
 */
proto.flow.entities.NodeVersionInfo.serializeBinaryToWriter = function (message, writer) {
    var f = undefined;
    f = message.getSemver();
    if (f.length > 0) {
        writer.writeString(1, f);
    }
    f = message.getCommit();
    if (f.length > 0) {
        writer.writeString(2, f);
    }
    f = message.getSporkId_asU8();
    if (f.length > 0) {
        writer.writeBytes(3, f);
    }
    f = message.getProtocolVersion();
    if (f !== 0) {
        writer.writeUint64(4, f);
    }
    f = message.getSporkRootBlockHeight();
    if (f !== 0) {
        writer.writeUint64(5, f);
    }
    f = message.getNodeRootBlockHeight();
    if (f !== 0) {
        writer.writeUint64(6, f);
    }
};
/**
 * optional string semver = 1;
 * @return {string}
 */
proto.flow.entities.NodeVersionInfo.prototype.getSemver = function () {
    return /** @type {string} */ (jspb.Message.getFieldWithDefault(this, 1, ""));
};
/**
 * @param {string} value
 * @return {!proto.flow.entities.NodeVersionInfo} returns this
 */
proto.flow.entities.NodeVersionInfo.prototype.setSemver = function (value) {
    return jspb.Message.setProto3StringField(this, 1, value);
};
/**
 * optional string commit = 2;
 * @return {string}
 */
proto.flow.entities.NodeVersionInfo.prototype.getCommit = function () {
    return /** @type {string} */ (jspb.Message.getFieldWithDefault(this, 2, ""));
};
/**
 * @param {string} value
 * @return {!proto.flow.entities.NodeVersionInfo} returns this
 */
proto.flow.entities.NodeVersionInfo.prototype.setCommit = function (value) {
    return jspb.Message.setProto3StringField(this, 2, value);
};
/**
 * optional bytes spork_id = 3;
 * @return {!(string|Uint8Array)}
 */
proto.flow.entities.NodeVersionInfo.prototype.getSporkId = function () {
    return /** @type {!(string|Uint8Array)} */ (jspb.Message.getFieldWithDefault(this, 3, ""));
};
/**
 * optional bytes spork_id = 3;
 * This is a type-conversion wrapper around `getSporkId()`
 * @return {string}
 */
proto.flow.entities.NodeVersionInfo.prototype.getSporkId_asB64 = function () {
    return /** @type {string} */ (jspb.Message.bytesAsB64(this.getSporkId()));
};
/**
 * optional bytes spork_id = 3;
 * Note that Uint8Array is not supported on all browsers.
 * @see http://caniuse.com/Uint8Array
 * This is a type-conversion wrapper around `getSporkId()`
 * @return {!Uint8Array}
 */
proto.flow.entities.NodeVersionInfo.prototype.getSporkId_asU8 = function () {
    return /** @type {!Uint8Array} */ (jspb.Message.bytesAsU8(this.getSporkId()));
};
/**
 * @param {!(string|Uint8Array)} value
 * @return {!proto.flow.entities.NodeVersionInfo} returns this
 */
proto.flow.entities.NodeVersionInfo.prototype.setSporkId = function (value) {
    return jspb.Message.setProto3BytesField(this, 3, value);
};
/**
 * optional uint64 protocol_version = 4;
 * @return {number}
 */
proto.flow.entities.NodeVersionInfo.prototype.getProtocolVersion = function () {
    return /** @type {number} */ (jspb.Message.getFieldWithDefault(this, 4, 0));
};
/**
 * @param {number} value
 * @return {!proto.flow.entities.NodeVersionInfo} returns this
 */
proto.flow.entities.NodeVersionInfo.prototype.setProtocolVersion = function (value) {
    return jspb.Message.setProto3IntField(this, 4, value);
};
/**
 * optional uint64 spork_root_block_height = 5;
 * @return {number}
 */
proto.flow.entities.NodeVersionInfo.prototype.getSporkRootBlockHeight = function () {
    return /** @type {number} */ (jspb.Message.getFieldWithDefault(this, 5, 0));
};
/**
 * @param {number} value
 * @return {!proto.flow.entities.NodeVersionInfo} returns this
 */
proto.flow.entities.NodeVersionInfo.prototype.setSporkRootBlockHeight = function (value) {
    return jspb.Message.setProto3IntField(this, 5, value);
};
/**
 * optional uint64 node_root_block_height = 6;
 * @return {number}
 */
proto.flow.entities.NodeVersionInfo.prototype.getNodeRootBlockHeight = function () {
    return /** @type {number} */ (jspb.Message.getFieldWithDefault(this, 6, 0));
};
/**
 * @param {number} value
 * @return {!proto.flow.entities.NodeVersionInfo} returns this
 */
proto.flow.entities.NodeVersionInfo.prototype.setNodeRootBlockHeight = function (value) {
    return jspb.Message.setProto3IntField(this, 6, value);
};

export const NodeVersionInfo = proto.flow.entities.NodeVersionInfo;
