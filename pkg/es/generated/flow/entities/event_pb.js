"use strict";
// source: flow/entities/event.proto
/**
 * @fileoverview
 * @enhanceable
 * @suppress {missingRequire} reports error on implicit type usages.
 * @suppress {messageConventions} JS Compiler reports an error if a variable or
 *     field starts with 'MSG_' and isn't a translatable message.
 * @public
 */
// GENERATED CODE -- DO NOT EDIT!
/* eslint-disable */
// @ts-nocheck
var jspb = require('google-protobuf');
var goog = jspb;
var global = (typeof globalThis !== 'undefined' && globalThis) ||
    (typeof window !== 'undefined' && window) ||
    (typeof global !== 'undefined' && global) ||
    (typeof self !== 'undefined' && self) ||
    (function () { return this; }).call(null) ||
    Function('return this')();
goog.exportSymbol('proto.flow.entities.Event', null, global);
goog.exportSymbol('proto.flow.entities.EventEncodingVersion', null, global);
/**
 * Generated by JsPbCodeGenerator.
 * @param {Array=} opt_data Optional initial data array, typically from a
 * server response, or constructed directly in Javascript. The array is used
 * in place and becomes part of the constructed object. It is not cloned.
 * If no data is provided, the constructed object will be empty, but still
 * valid.
 * @extends {jspb.Message}
 * @constructor
 */
proto.flow.entities.Event = function (opt_data) {
    jspb.Message.initialize(this, opt_data, 0, -1, null, null);
};
goog.inherits(proto.flow.entities.Event, jspb.Message);
if (goog.DEBUG && !COMPILED) {
    /**
     * @public
     * @override
     */
    proto.flow.entities.Event.displayName = 'proto.flow.entities.Event';
}
if (jspb.Message.GENERATE_TO_OBJECT) {
    /**
     * Creates an object representation of this proto.
     * Field names that are reserved in JavaScript and will be renamed to pb_name.
     * Optional fields that are not set will be set to undefined.
     * To access a reserved field use, foo.pb_<name>, eg, foo.pb_default.
     * For the list of reserved names please see:
     *     net/proto2/compiler/js/internal/generator.cc#kKeyword.
     * @param {boolean=} opt_includeInstance Deprecated. whether to include the
     *     JSPB instance for transitional soy proto support:
     *     http://goto/soy-param-migration
     * @return {!Object}
     */
    proto.flow.entities.Event.prototype.toObject = function (opt_includeInstance) {
        return proto.flow.entities.Event.toObject(opt_includeInstance, this);
    };
    /**
     * Static version of the {@see toObject} method.
     * @param {boolean|undefined} includeInstance Deprecated. Whether to include
     *     the JSPB instance for transitional soy proto support:
     *     http://goto/soy-param-migration
     * @param {!proto.flow.entities.Event} msg The msg instance to transform.
     * @return {!Object}
     * @suppress {unusedLocalVariables} f is only used for nested messages
     */
    proto.flow.entities.Event.toObject = function (includeInstance, msg) {
        var f, obj = {
            type: jspb.Message.getFieldWithDefault(msg, 1, ""),
            transactionId: msg.getTransactionId_asB64(),
            transactionIndex: jspb.Message.getFieldWithDefault(msg, 3, 0),
            eventIndex: jspb.Message.getFieldWithDefault(msg, 4, 0),
            payload: msg.getPayload_asB64()
        };
        if (includeInstance) {
            obj.$jspbMessageInstance = msg;
        }
        return obj;
    };
}
/**
 * Deserializes binary data (in protobuf wire format).
 * @param {jspb.ByteSource} bytes The bytes to deserialize.
 * @return {!proto.flow.entities.Event}
 */
proto.flow.entities.Event.deserializeBinary = function (bytes) {
    var reader = new jspb.BinaryReader(bytes);
    var msg = new proto.flow.entities.Event;
    return proto.flow.entities.Event.deserializeBinaryFromReader(msg, reader);
};
/**
 * Deserializes binary data (in protobuf wire format) from the
 * given reader into the given message object.
 * @param {!proto.flow.entities.Event} msg The message object to deserialize into.
 * @param {!jspb.BinaryReader} reader The BinaryReader to use.
 * @return {!proto.flow.entities.Event}
 */
proto.flow.entities.Event.deserializeBinaryFromReader = function (msg, reader) {
    while (reader.nextField()) {
        if (reader.isEndGroup()) {
            break;
        }
        var field = reader.getFieldNumber();
        switch (field) {
            case 1:
                var value = /** @type {string} */ (reader.readString());
                msg.setType(value);
                break;
            case 2:
                var value = /** @type {!Uint8Array} */ (reader.readBytes());
                msg.setTransactionId(value);
                break;
            case 3:
                var value = /** @type {number} */ (reader.readUint32());
                msg.setTransactionIndex(value);
                break;
            case 4:
                var value = /** @type {number} */ (reader.readUint32());
                msg.setEventIndex(value);
                break;
            case 5:
                var value = /** @type {!Uint8Array} */ (reader.readBytes());
                msg.setPayload(value);
                break;
            default:
                reader.skipField();
                break;
        }
    }
    return msg;
};
/**
 * Serializes the message to binary data (in protobuf wire format).
 * @return {!Uint8Array}
 */
proto.flow.entities.Event.prototype.serializeBinary = function () {
    var writer = new jspb.BinaryWriter();
    proto.flow.entities.Event.serializeBinaryToWriter(this, writer);
    return writer.getResultBuffer();
};
/**
 * Serializes the given message to binary data (in protobuf wire
 * format), writing to the given BinaryWriter.
 * @param {!proto.flow.entities.Event} message
 * @param {!jspb.BinaryWriter} writer
 * @suppress {unusedLocalVariables} f is only used for nested messages
 */
proto.flow.entities.Event.serializeBinaryToWriter = function (message, writer) {
    var f = undefined;
    f = message.getType();
    if (f.length > 0) {
        writer.writeString(1, f);
    }
    f = message.getTransactionId_asU8();
    if (f.length > 0) {
        writer.writeBytes(2, f);
    }
    f = message.getTransactionIndex();
    if (f !== 0) {
        writer.writeUint32(3, f);
    }
    f = message.getEventIndex();
    if (f !== 0) {
        writer.writeUint32(4, f);
    }
    f = message.getPayload_asU8();
    if (f.length > 0) {
        writer.writeBytes(5, f);
    }
};
/**
 * optional string type = 1;
 * @return {string}
 */
proto.flow.entities.Event.prototype.getType = function () {
    return /** @type {string} */ (jspb.Message.getFieldWithDefault(this, 1, ""));
};
/**
 * @param {string} value
 * @return {!proto.flow.entities.Event} returns this
 */
proto.flow.entities.Event.prototype.setType = function (value) {
    return jspb.Message.setProto3StringField(this, 1, value);
};
/**
 * optional bytes transaction_id = 2;
 * @return {!(string|Uint8Array)}
 */
proto.flow.entities.Event.prototype.getTransactionId = function () {
    return /** @type {!(string|Uint8Array)} */ (jspb.Message.getFieldWithDefault(this, 2, ""));
};
/**
 * optional bytes transaction_id = 2;
 * This is a type-conversion wrapper around `getTransactionId()`
 * @return {string}
 */
proto.flow.entities.Event.prototype.getTransactionId_asB64 = function () {
    return /** @type {string} */ (jspb.Message.bytesAsB64(this.getTransactionId()));
};
/**
 * optional bytes transaction_id = 2;
 * Note that Uint8Array is not supported on all browsers.
 * @see http://caniuse.com/Uint8Array
 * This is a type-conversion wrapper around `getTransactionId()`
 * @return {!Uint8Array}
 */
proto.flow.entities.Event.prototype.getTransactionId_asU8 = function () {
    return /** @type {!Uint8Array} */ (jspb.Message.bytesAsU8(this.getTransactionId()));
};
/**
 * @param {!(string|Uint8Array)} value
 * @return {!proto.flow.entities.Event} returns this
 */
proto.flow.entities.Event.prototype.setTransactionId = function (value) {
    return jspb.Message.setProto3BytesField(this, 2, value);
};
/**
 * optional uint32 transaction_index = 3;
 * @return {number}
 */
proto.flow.entities.Event.prototype.getTransactionIndex = function () {
    return /** @type {number} */ (jspb.Message.getFieldWithDefault(this, 3, 0));
};
/**
 * @param {number} value
 * @return {!proto.flow.entities.Event} returns this
 */
proto.flow.entities.Event.prototype.setTransactionIndex = function (value) {
    return jspb.Message.setProto3IntField(this, 3, value);
};
/**
 * optional uint32 event_index = 4;
 * @return {number}
 */
proto.flow.entities.Event.prototype.getEventIndex = function () {
    return /** @type {number} */ (jspb.Message.getFieldWithDefault(this, 4, 0));
};
/**
 * @param {number} value
 * @return {!proto.flow.entities.Event} returns this
 */
proto.flow.entities.Event.prototype.setEventIndex = function (value) {
    return jspb.Message.setProto3IntField(this, 4, value);
};
/**
 * optional bytes payload = 5;
 * @return {!(string|Uint8Array)}
 */
proto.flow.entities.Event.prototype.getPayload = function () {
    return /** @type {!(string|Uint8Array)} */ (jspb.Message.getFieldWithDefault(this, 5, ""));
};
/**
 * optional bytes payload = 5;
 * This is a type-conversion wrapper around `getPayload()`
 * @return {string}
 */
proto.flow.entities.Event.prototype.getPayload_asB64 = function () {
    return /** @type {string} */ (jspb.Message.bytesAsB64(this.getPayload()));
};
/**
 * optional bytes payload = 5;
 * Note that Uint8Array is not supported on all browsers.
 * @see http://caniuse.com/Uint8Array
 * This is a type-conversion wrapper around `getPayload()`
 * @return {!Uint8Array}
 */
proto.flow.entities.Event.prototype.getPayload_asU8 = function () {
    return /** @type {!Uint8Array} */ (jspb.Message.bytesAsU8(this.getPayload()));
};
/**
 * @param {!(string|Uint8Array)} value
 * @return {!proto.flow.entities.Event} returns this
 */
proto.flow.entities.Event.prototype.setPayload = function (value) {
    return jspb.Message.setProto3BytesField(this, 5, value);
};
/**
 * @enum {number}
 */
proto.flow.entities.EventEncodingVersion = {
    JSON_CDC_V0: 0,
    CCF_V0: 1
};
goog.object.extend(exports, proto.flow.entities);
