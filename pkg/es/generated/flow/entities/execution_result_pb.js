"use strict";
// source: flow/entities/execution_result.proto
/**
 * @fileoverview
 * @enhanceable
 * @suppress {missingRequire} reports error on implicit type usages.
 * @suppress {messageConventions} JS Compiler reports an error if a variable or
 *     field starts with 'MSG_' and isn't a translatable message.
 * @public
 */
// GENERATED CODE -- DO NOT EDIT!
/* eslint-disable */
// @ts-nocheck
var jspb = require('google-protobuf');
var goog = jspb;
var global = (typeof globalThis !== 'undefined' && globalThis) ||
    (typeof window !== 'undefined' && window) ||
    (typeof global !== 'undefined' && global) ||
    (typeof self !== 'undefined' && self) ||
    (function () { return this; }).call(null) ||
    Function('return this')();
goog.exportSymbol('proto.flow.entities.Chunk', null, global);
goog.exportSymbol('proto.flow.entities.ExecutionReceiptMeta', null, global);
goog.exportSymbol('proto.flow.entities.ExecutionResult', null, global);
goog.exportSymbol('proto.flow.entities.ServiceEvent', null, global);
/**
 * Generated by JsPbCodeGenerator.
 * @param {Array=} opt_data Optional initial data array, typically from a
 * server response, or constructed directly in Javascript. The array is used
 * in place and becomes part of the constructed object. It is not cloned.
 * If no data is provided, the constructed object will be empty, but still
 * valid.
 * @extends {jspb.Message}
 * @constructor
 */
proto.flow.entities.ExecutionResult = function (opt_data) {
    jspb.Message.initialize(this, opt_data, 0, -1, proto.flow.entities.ExecutionResult.repeatedFields_, null);
};
goog.inherits(proto.flow.entities.ExecutionResult, jspb.Message);
if (goog.DEBUG && !COMPILED) {
    /**
     * @public
     * @override
     */
    proto.flow.entities.ExecutionResult.displayName = 'proto.flow.entities.ExecutionResult';
}
/**
 * Generated by JsPbCodeGenerator.
 * @param {Array=} opt_data Optional initial data array, typically from a
 * server response, or constructed directly in Javascript. The array is used
 * in place and becomes part of the constructed object. It is not cloned.
 * If no data is provided, the constructed object will be empty, but still
 * valid.
 * @extends {jspb.Message}
 * @constructor
 */
proto.flow.entities.Chunk = function (opt_data) {
    jspb.Message.initialize(this, opt_data, 0, -1, null, null);
};
goog.inherits(proto.flow.entities.Chunk, jspb.Message);
if (goog.DEBUG && !COMPILED) {
    /**
     * @public
     * @override
     */
    proto.flow.entities.Chunk.displayName = 'proto.flow.entities.Chunk';
}
/**
 * Generated by JsPbCodeGenerator.
 * @param {Array=} opt_data Optional initial data array, typically from a
 * server response, or constructed directly in Javascript. The array is used
 * in place and becomes part of the constructed object. It is not cloned.
 * If no data is provided, the constructed object will be empty, but still
 * valid.
 * @extends {jspb.Message}
 * @constructor
 */
proto.flow.entities.ServiceEvent = function (opt_data) {
    jspb.Message.initialize(this, opt_data, 0, -1, null, null);
};
goog.inherits(proto.flow.entities.ServiceEvent, jspb.Message);
if (goog.DEBUG && !COMPILED) {
    /**
     * @public
     * @override
     */
    proto.flow.entities.ServiceEvent.displayName = 'proto.flow.entities.ServiceEvent';
}
/**
 * Generated by JsPbCodeGenerator.
 * @param {Array=} opt_data Optional initial data array, typically from a
 * server response, or constructed directly in Javascript. The array is used
 * in place and becomes part of the constructed object. It is not cloned.
 * If no data is provided, the constructed object will be empty, but still
 * valid.
 * @extends {jspb.Message}
 * @constructor
 */
proto.flow.entities.ExecutionReceiptMeta = function (opt_data) {
    jspb.Message.initialize(this, opt_data, 0, -1, proto.flow.entities.ExecutionReceiptMeta.repeatedFields_, null);
};
goog.inherits(proto.flow.entities.ExecutionReceiptMeta, jspb.Message);
if (goog.DEBUG && !COMPILED) {
    /**
     * @public
     * @override
     */
    proto.flow.entities.ExecutionReceiptMeta.displayName = 'proto.flow.entities.ExecutionReceiptMeta';
}
/**
 * List of repeated fields within this message type.
 * @private {!Array<number>}
 * @const
 */
proto.flow.entities.ExecutionResult.repeatedFields_ = [3, 4];
if (jspb.Message.GENERATE_TO_OBJECT) {
    /**
     * Creates an object representation of this proto.
     * Field names that are reserved in JavaScript and will be renamed to pb_name.
     * Optional fields that are not set will be set to undefined.
     * To access a reserved field use, foo.pb_<name>, eg, foo.pb_default.
     * For the list of reserved names please see:
     *     net/proto2/compiler/js/internal/generator.cc#kKeyword.
     * @param {boolean=} opt_includeInstance Deprecated. whether to include the
     *     JSPB instance for transitional soy proto support:
     *     http://goto/soy-param-migration
     * @return {!Object}
     */
    proto.flow.entities.ExecutionResult.prototype.toObject = function (opt_includeInstance) {
        return proto.flow.entities.ExecutionResult.toObject(opt_includeInstance, this);
    };
    /**
     * Static version of the {@see toObject} method.
     * @param {boolean|undefined} includeInstance Deprecated. Whether to include
     *     the JSPB instance for transitional soy proto support:
     *     http://goto/soy-param-migration
     * @param {!proto.flow.entities.ExecutionResult} msg The msg instance to transform.
     * @return {!Object}
     * @suppress {unusedLocalVariables} f is only used for nested messages
     */
    proto.flow.entities.ExecutionResult.toObject = function (includeInstance, msg) {
        var f, obj = {
            previousResultId: msg.getPreviousResultId_asB64(),
            blockId: msg.getBlockId_asB64(),
            chunksList: jspb.Message.toObjectList(msg.getChunksList(), proto.flow.entities.Chunk.toObject, includeInstance),
            serviceEventsList: jspb.Message.toObjectList(msg.getServiceEventsList(), proto.flow.entities.ServiceEvent.toObject, includeInstance),
            executionDataId: msg.getExecutionDataId_asB64()
        };
        if (includeInstance) {
            obj.$jspbMessageInstance = msg;
        }
        return obj;
    };
}
/**
 * Deserializes binary data (in protobuf wire format).
 * @param {jspb.ByteSource} bytes The bytes to deserialize.
 * @return {!proto.flow.entities.ExecutionResult}
 */
proto.flow.entities.ExecutionResult.deserializeBinary = function (bytes) {
    var reader = new jspb.BinaryReader(bytes);
    var msg = new proto.flow.entities.ExecutionResult;
    return proto.flow.entities.ExecutionResult.deserializeBinaryFromReader(msg, reader);
};
/**
 * Deserializes binary data (in protobuf wire format) from the
 * given reader into the given message object.
 * @param {!proto.flow.entities.ExecutionResult} msg The message object to deserialize into.
 * @param {!jspb.BinaryReader} reader The BinaryReader to use.
 * @return {!proto.flow.entities.ExecutionResult}
 */
proto.flow.entities.ExecutionResult.deserializeBinaryFromReader = function (msg, reader) {
    while (reader.nextField()) {
        if (reader.isEndGroup()) {
            break;
        }
        var field = reader.getFieldNumber();
        switch (field) {
            case 1:
                var value = /** @type {!Uint8Array} */ (reader.readBytes());
                msg.setPreviousResultId(value);
                break;
            case 2:
                var value = /** @type {!Uint8Array} */ (reader.readBytes());
                msg.setBlockId(value);
                break;
            case 3:
                var value = new proto.flow.entities.Chunk;
                reader.readMessage(value, proto.flow.entities.Chunk.deserializeBinaryFromReader);
                msg.addChunks(value);
                break;
            case 4:
                var value = new proto.flow.entities.ServiceEvent;
                reader.readMessage(value, proto.flow.entities.ServiceEvent.deserializeBinaryFromReader);
                msg.addServiceEvents(value);
                break;
            case 5:
                var value = /** @type {!Uint8Array} */ (reader.readBytes());
                msg.setExecutionDataId(value);
                break;
            default:
                reader.skipField();
                break;
        }
    }
    return msg;
};
/**
 * Serializes the message to binary data (in protobuf wire format).
 * @return {!Uint8Array}
 */
proto.flow.entities.ExecutionResult.prototype.serializeBinary = function () {
    var writer = new jspb.BinaryWriter();
    proto.flow.entities.ExecutionResult.serializeBinaryToWriter(this, writer);
    return writer.getResultBuffer();
};
/**
 * Serializes the given message to binary data (in protobuf wire
 * format), writing to the given BinaryWriter.
 * @param {!proto.flow.entities.ExecutionResult} message
 * @param {!jspb.BinaryWriter} writer
 * @suppress {unusedLocalVariables} f is only used for nested messages
 */
proto.flow.entities.ExecutionResult.serializeBinaryToWriter = function (message, writer) {
    var f = undefined;
    f = message.getPreviousResultId_asU8();
    if (f.length > 0) {
        writer.writeBytes(1, f);
    }
    f = message.getBlockId_asU8();
    if (f.length > 0) {
        writer.writeBytes(2, f);
    }
    f = message.getChunksList();
    if (f.length > 0) {
        writer.writeRepeatedMessage(3, f, proto.flow.entities.Chunk.serializeBinaryToWriter);
    }
    f = message.getServiceEventsList();
    if (f.length > 0) {
        writer.writeRepeatedMessage(4, f, proto.flow.entities.ServiceEvent.serializeBinaryToWriter);
    }
    f = message.getExecutionDataId_asU8();
    if (f.length > 0) {
        writer.writeBytes(5, f);
    }
};
/**
 * optional bytes previous_result_id = 1;
 * @return {!(string|Uint8Array)}
 */
proto.flow.entities.ExecutionResult.prototype.getPreviousResultId = function () {
    return /** @type {!(string|Uint8Array)} */ (jspb.Message.getFieldWithDefault(this, 1, ""));
};
/**
 * optional bytes previous_result_id = 1;
 * This is a type-conversion wrapper around `getPreviousResultId()`
 * @return {string}
 */
proto.flow.entities.ExecutionResult.prototype.getPreviousResultId_asB64 = function () {
    return /** @type {string} */ (jspb.Message.bytesAsB64(this.getPreviousResultId()));
};
/**
 * optional bytes previous_result_id = 1;
 * Note that Uint8Array is not supported on all browsers.
 * @see http://caniuse.com/Uint8Array
 * This is a type-conversion wrapper around `getPreviousResultId()`
 * @return {!Uint8Array}
 */
proto.flow.entities.ExecutionResult.prototype.getPreviousResultId_asU8 = function () {
    return /** @type {!Uint8Array} */ (jspb.Message.bytesAsU8(this.getPreviousResultId()));
};
/**
 * @param {!(string|Uint8Array)} value
 * @return {!proto.flow.entities.ExecutionResult} returns this
 */
proto.flow.entities.ExecutionResult.prototype.setPreviousResultId = function (value) {
    return jspb.Message.setProto3BytesField(this, 1, value);
};
/**
 * optional bytes block_id = 2;
 * @return {!(string|Uint8Array)}
 */
proto.flow.entities.ExecutionResult.prototype.getBlockId = function () {
    return /** @type {!(string|Uint8Array)} */ (jspb.Message.getFieldWithDefault(this, 2, ""));
};
/**
 * optional bytes block_id = 2;
 * This is a type-conversion wrapper around `getBlockId()`
 * @return {string}
 */
proto.flow.entities.ExecutionResult.prototype.getBlockId_asB64 = function () {
    return /** @type {string} */ (jspb.Message.bytesAsB64(this.getBlockId()));
};
/**
 * optional bytes block_id = 2;
 * Note that Uint8Array is not supported on all browsers.
 * @see http://caniuse.com/Uint8Array
 * This is a type-conversion wrapper around `getBlockId()`
 * @return {!Uint8Array}
 */
proto.flow.entities.ExecutionResult.prototype.getBlockId_asU8 = function () {
    return /** @type {!Uint8Array} */ (jspb.Message.bytesAsU8(this.getBlockId()));
};
/**
 * @param {!(string|Uint8Array)} value
 * @return {!proto.flow.entities.ExecutionResult} returns this
 */
proto.flow.entities.ExecutionResult.prototype.setBlockId = function (value) {
    return jspb.Message.setProto3BytesField(this, 2, value);
};
/**
 * repeated Chunk chunks = 3;
 * @return {!Array<!proto.flow.entities.Chunk>}
 */
proto.flow.entities.ExecutionResult.prototype.getChunksList = function () {
    return /** @type{!Array<!proto.flow.entities.Chunk>} */ (jspb.Message.getRepeatedWrapperField(this, proto.flow.entities.Chunk, 3));
};
/**
 * @param {!Array<!proto.flow.entities.Chunk>} value
 * @return {!proto.flow.entities.ExecutionResult} returns this
*/
proto.flow.entities.ExecutionResult.prototype.setChunksList = function (value) {
    return jspb.Message.setRepeatedWrapperField(this, 3, value);
};
/**
 * @param {!proto.flow.entities.Chunk=} opt_value
 * @param {number=} opt_index
 * @return {!proto.flow.entities.Chunk}
 */
proto.flow.entities.ExecutionResult.prototype.addChunks = function (opt_value, opt_index) {
    return jspb.Message.addToRepeatedWrapperField(this, 3, opt_value, proto.flow.entities.Chunk, opt_index);
};
/**
 * Clears the list making it empty but non-null.
 * @return {!proto.flow.entities.ExecutionResult} returns this
 */
proto.flow.entities.ExecutionResult.prototype.clearChunksList = function () {
    return this.setChunksList([]);
};
/**
 * repeated ServiceEvent service_events = 4;
 * @return {!Array<!proto.flow.entities.ServiceEvent>}
 */
proto.flow.entities.ExecutionResult.prototype.getServiceEventsList = function () {
    return /** @type{!Array<!proto.flow.entities.ServiceEvent>} */ (jspb.Message.getRepeatedWrapperField(this, proto.flow.entities.ServiceEvent, 4));
};
/**
 * @param {!Array<!proto.flow.entities.ServiceEvent>} value
 * @return {!proto.flow.entities.ExecutionResult} returns this
*/
proto.flow.entities.ExecutionResult.prototype.setServiceEventsList = function (value) {
    return jspb.Message.setRepeatedWrapperField(this, 4, value);
};
/**
 * @param {!proto.flow.entities.ServiceEvent=} opt_value
 * @param {number=} opt_index
 * @return {!proto.flow.entities.ServiceEvent}
 */
proto.flow.entities.ExecutionResult.prototype.addServiceEvents = function (opt_value, opt_index) {
    return jspb.Message.addToRepeatedWrapperField(this, 4, opt_value, proto.flow.entities.ServiceEvent, opt_index);
};
/**
 * Clears the list making it empty but non-null.
 * @return {!proto.flow.entities.ExecutionResult} returns this
 */
proto.flow.entities.ExecutionResult.prototype.clearServiceEventsList = function () {
    return this.setServiceEventsList([]);
};
/**
 * optional bytes execution_data_id = 5;
 * @return {!(string|Uint8Array)}
 */
proto.flow.entities.ExecutionResult.prototype.getExecutionDataId = function () {
    return /** @type {!(string|Uint8Array)} */ (jspb.Message.getFieldWithDefault(this, 5, ""));
};
/**
 * optional bytes execution_data_id = 5;
 * This is a type-conversion wrapper around `getExecutionDataId()`
 * @return {string}
 */
proto.flow.entities.ExecutionResult.prototype.getExecutionDataId_asB64 = function () {
    return /** @type {string} */ (jspb.Message.bytesAsB64(this.getExecutionDataId()));
};
/**
 * optional bytes execution_data_id = 5;
 * Note that Uint8Array is not supported on all browsers.
 * @see http://caniuse.com/Uint8Array
 * This is a type-conversion wrapper around `getExecutionDataId()`
 * @return {!Uint8Array}
 */
proto.flow.entities.ExecutionResult.prototype.getExecutionDataId_asU8 = function () {
    return /** @type {!Uint8Array} */ (jspb.Message.bytesAsU8(this.getExecutionDataId()));
};
/**
 * @param {!(string|Uint8Array)} value
 * @return {!proto.flow.entities.ExecutionResult} returns this
 */
proto.flow.entities.ExecutionResult.prototype.setExecutionDataId = function (value) {
    return jspb.Message.setProto3BytesField(this, 5, value);
};
if (jspb.Message.GENERATE_TO_OBJECT) {
    /**
     * Creates an object representation of this proto.
     * Field names that are reserved in JavaScript and will be renamed to pb_name.
     * Optional fields that are not set will be set to undefined.
     * To access a reserved field use, foo.pb_<name>, eg, foo.pb_default.
     * For the list of reserved names please see:
     *     net/proto2/compiler/js/internal/generator.cc#kKeyword.
     * @param {boolean=} opt_includeInstance Deprecated. whether to include the
     *     JSPB instance for transitional soy proto support:
     *     http://goto/soy-param-migration
     * @return {!Object}
     */
    proto.flow.entities.Chunk.prototype.toObject = function (opt_includeInstance) {
        return proto.flow.entities.Chunk.toObject(opt_includeInstance, this);
    };
    /**
     * Static version of the {@see toObject} method.
     * @param {boolean|undefined} includeInstance Deprecated. Whether to include
     *     the JSPB instance for transitional soy proto support:
     *     http://goto/soy-param-migration
     * @param {!proto.flow.entities.Chunk} msg The msg instance to transform.
     * @return {!Object}
     * @suppress {unusedLocalVariables} f is only used for nested messages
     */
    proto.flow.entities.Chunk.toObject = function (includeInstance, msg) {
        var f, obj = {
            collectionindex: jspb.Message.getFieldWithDefault(msg, 1, 0),
            startState: msg.getStartState_asB64(),
            eventCollection: msg.getEventCollection_asB64(),
            blockId: msg.getBlockId_asB64(),
            totalComputationUsed: jspb.Message.getFieldWithDefault(msg, 5, 0),
            numberOfTransactions: jspb.Message.getFieldWithDefault(msg, 6, 0),
            index: jspb.Message.getFieldWithDefault(msg, 7, 0),
            endState: msg.getEndState_asB64(),
            executionDataId: msg.getExecutionDataId_asB64(),
            stateDeltaCommitment: msg.getStateDeltaCommitment_asB64()
        };
        if (includeInstance) {
            obj.$jspbMessageInstance = msg;
        }
        return obj;
    };
}
/**
 * Deserializes binary data (in protobuf wire format).
 * @param {jspb.ByteSource} bytes The bytes to deserialize.
 * @return {!proto.flow.entities.Chunk}
 */
proto.flow.entities.Chunk.deserializeBinary = function (bytes) {
    var reader = new jspb.BinaryReader(bytes);
    var msg = new proto.flow.entities.Chunk;
    return proto.flow.entities.Chunk.deserializeBinaryFromReader(msg, reader);
};
/**
 * Deserializes binary data (in protobuf wire format) from the
 * given reader into the given message object.
 * @param {!proto.flow.entities.Chunk} msg The message object to deserialize into.
 * @param {!jspb.BinaryReader} reader The BinaryReader to use.
 * @return {!proto.flow.entities.Chunk}
 */
proto.flow.entities.Chunk.deserializeBinaryFromReader = function (msg, reader) {
    while (reader.nextField()) {
        if (reader.isEndGroup()) {
            break;
        }
        var field = reader.getFieldNumber();
        switch (field) {
            case 1:
                var value = /** @type {number} */ (reader.readUint32());
                msg.setCollectionindex(value);
                break;
            case 2:
                var value = /** @type {!Uint8Array} */ (reader.readBytes());
                msg.setStartState(value);
                break;
            case 3:
                var value = /** @type {!Uint8Array} */ (reader.readBytes());
                msg.setEventCollection(value);
                break;
            case 4:
                var value = /** @type {!Uint8Array} */ (reader.readBytes());
                msg.setBlockId(value);
                break;
            case 5:
                var value = /** @type {number} */ (reader.readUint64());
                msg.setTotalComputationUsed(value);
                break;
            case 6:
                var value = /** @type {number} */ (reader.readUint32());
                msg.setNumberOfTransactions(value);
                break;
            case 7:
                var value = /** @type {number} */ (reader.readUint64());
                msg.setIndex(value);
                break;
            case 8:
                var value = /** @type {!Uint8Array} */ (reader.readBytes());
                msg.setEndState(value);
                break;
            case 9:
                var value = /** @type {!Uint8Array} */ (reader.readBytes());
                msg.setExecutionDataId(value);
                break;
            case 10:
                var value = /** @type {!Uint8Array} */ (reader.readBytes());
                msg.setStateDeltaCommitment(value);
                break;
            default:
                reader.skipField();
                break;
        }
    }
    return msg;
};
/**
 * Serializes the message to binary data (in protobuf wire format).
 * @return {!Uint8Array}
 */
proto.flow.entities.Chunk.prototype.serializeBinary = function () {
    var writer = new jspb.BinaryWriter();
    proto.flow.entities.Chunk.serializeBinaryToWriter(this, writer);
    return writer.getResultBuffer();
};
/**
 * Serializes the given message to binary data (in protobuf wire
 * format), writing to the given BinaryWriter.
 * @param {!proto.flow.entities.Chunk} message
 * @param {!jspb.BinaryWriter} writer
 * @suppress {unusedLocalVariables} f is only used for nested messages
 */
proto.flow.entities.Chunk.serializeBinaryToWriter = function (message, writer) {
    var f = undefined;
    f = message.getCollectionindex();
    if (f !== 0) {
        writer.writeUint32(1, f);
    }
    f = message.getStartState_asU8();
    if (f.length > 0) {
        writer.writeBytes(2, f);
    }
    f = message.getEventCollection_asU8();
    if (f.length > 0) {
        writer.writeBytes(3, f);
    }
    f = message.getBlockId_asU8();
    if (f.length > 0) {
        writer.writeBytes(4, f);
    }
    f = message.getTotalComputationUsed();
    if (f !== 0) {
        writer.writeUint64(5, f);
    }
    f = message.getNumberOfTransactions();
    if (f !== 0) {
        writer.writeUint32(6, f);
    }
    f = message.getIndex();
    if (f !== 0) {
        writer.writeUint64(7, f);
    }
    f = message.getEndState_asU8();
    if (f.length > 0) {
        writer.writeBytes(8, f);
    }
    f = message.getExecutionDataId_asU8();
    if (f.length > 0) {
        writer.writeBytes(9, f);
    }
    f = message.getStateDeltaCommitment_asU8();
    if (f.length > 0) {
        writer.writeBytes(10, f);
    }
};
/**
 * optional uint32 CollectionIndex = 1;
 * @return {number}
 */
proto.flow.entities.Chunk.prototype.getCollectionindex = function () {
    return /** @type {number} */ (jspb.Message.getFieldWithDefault(this, 1, 0));
};
/**
 * @param {number} value
 * @return {!proto.flow.entities.Chunk} returns this
 */
proto.flow.entities.Chunk.prototype.setCollectionindex = function (value) {
    return jspb.Message.setProto3IntField(this, 1, value);
};
/**
 * optional bytes start_state = 2;
 * @return {!(string|Uint8Array)}
 */
proto.flow.entities.Chunk.prototype.getStartState = function () {
    return /** @type {!(string|Uint8Array)} */ (jspb.Message.getFieldWithDefault(this, 2, ""));
};
/**
 * optional bytes start_state = 2;
 * This is a type-conversion wrapper around `getStartState()`
 * @return {string}
 */
proto.flow.entities.Chunk.prototype.getStartState_asB64 = function () {
    return /** @type {string} */ (jspb.Message.bytesAsB64(this.getStartState()));
};
/**
 * optional bytes start_state = 2;
 * Note that Uint8Array is not supported on all browsers.
 * @see http://caniuse.com/Uint8Array
 * This is a type-conversion wrapper around `getStartState()`
 * @return {!Uint8Array}
 */
proto.flow.entities.Chunk.prototype.getStartState_asU8 = function () {
    return /** @type {!Uint8Array} */ (jspb.Message.bytesAsU8(this.getStartState()));
};
/**
 * @param {!(string|Uint8Array)} value
 * @return {!proto.flow.entities.Chunk} returns this
 */
proto.flow.entities.Chunk.prototype.setStartState = function (value) {
    return jspb.Message.setProto3BytesField(this, 2, value);
};
/**
 * optional bytes event_collection = 3;
 * @return {!(string|Uint8Array)}
 */
proto.flow.entities.Chunk.prototype.getEventCollection = function () {
    return /** @type {!(string|Uint8Array)} */ (jspb.Message.getFieldWithDefault(this, 3, ""));
};
/**
 * optional bytes event_collection = 3;
 * This is a type-conversion wrapper around `getEventCollection()`
 * @return {string}
 */
proto.flow.entities.Chunk.prototype.getEventCollection_asB64 = function () {
    return /** @type {string} */ (jspb.Message.bytesAsB64(this.getEventCollection()));
};
/**
 * optional bytes event_collection = 3;
 * Note that Uint8Array is not supported on all browsers.
 * @see http://caniuse.com/Uint8Array
 * This is a type-conversion wrapper around `getEventCollection()`
 * @return {!Uint8Array}
 */
proto.flow.entities.Chunk.prototype.getEventCollection_asU8 = function () {
    return /** @type {!Uint8Array} */ (jspb.Message.bytesAsU8(this.getEventCollection()));
};
/**
 * @param {!(string|Uint8Array)} value
 * @return {!proto.flow.entities.Chunk} returns this
 */
proto.flow.entities.Chunk.prototype.setEventCollection = function (value) {
    return jspb.Message.setProto3BytesField(this, 3, value);
};
/**
 * optional bytes block_id = 4;
 * @return {!(string|Uint8Array)}
 */
proto.flow.entities.Chunk.prototype.getBlockId = function () {
    return /** @type {!(string|Uint8Array)} */ (jspb.Message.getFieldWithDefault(this, 4, ""));
};
/**
 * optional bytes block_id = 4;
 * This is a type-conversion wrapper around `getBlockId()`
 * @return {string}
 */
proto.flow.entities.Chunk.prototype.getBlockId_asB64 = function () {
    return /** @type {string} */ (jspb.Message.bytesAsB64(this.getBlockId()));
};
/**
 * optional bytes block_id = 4;
 * Note that Uint8Array is not supported on all browsers.
 * @see http://caniuse.com/Uint8Array
 * This is a type-conversion wrapper around `getBlockId()`
 * @return {!Uint8Array}
 */
proto.flow.entities.Chunk.prototype.getBlockId_asU8 = function () {
    return /** @type {!Uint8Array} */ (jspb.Message.bytesAsU8(this.getBlockId()));
};
/**
 * @param {!(string|Uint8Array)} value
 * @return {!proto.flow.entities.Chunk} returns this
 */
proto.flow.entities.Chunk.prototype.setBlockId = function (value) {
    return jspb.Message.setProto3BytesField(this, 4, value);
};
/**
 * optional uint64 total_computation_used = 5;
 * @return {number}
 */
proto.flow.entities.Chunk.prototype.getTotalComputationUsed = function () {
    return /** @type {number} */ (jspb.Message.getFieldWithDefault(this, 5, 0));
};
/**
 * @param {number} value
 * @return {!proto.flow.entities.Chunk} returns this
 */
proto.flow.entities.Chunk.prototype.setTotalComputationUsed = function (value) {
    return jspb.Message.setProto3IntField(this, 5, value);
};
/**
 * optional uint32 number_of_transactions = 6;
 * @return {number}
 */
proto.flow.entities.Chunk.prototype.getNumberOfTransactions = function () {
    return /** @type {number} */ (jspb.Message.getFieldWithDefault(this, 6, 0));
};
/**
 * @param {number} value
 * @return {!proto.flow.entities.Chunk} returns this
 */
proto.flow.entities.Chunk.prototype.setNumberOfTransactions = function (value) {
    return jspb.Message.setProto3IntField(this, 6, value);
};
/**
 * optional uint64 index = 7;
 * @return {number}
 */
proto.flow.entities.Chunk.prototype.getIndex = function () {
    return /** @type {number} */ (jspb.Message.getFieldWithDefault(this, 7, 0));
};
/**
 * @param {number} value
 * @return {!proto.flow.entities.Chunk} returns this
 */
proto.flow.entities.Chunk.prototype.setIndex = function (value) {
    return jspb.Message.setProto3IntField(this, 7, value);
};
/**
 * optional bytes end_state = 8;
 * @return {!(string|Uint8Array)}
 */
proto.flow.entities.Chunk.prototype.getEndState = function () {
    return /** @type {!(string|Uint8Array)} */ (jspb.Message.getFieldWithDefault(this, 8, ""));
};
/**
 * optional bytes end_state = 8;
 * This is a type-conversion wrapper around `getEndState()`
 * @return {string}
 */
proto.flow.entities.Chunk.prototype.getEndState_asB64 = function () {
    return /** @type {string} */ (jspb.Message.bytesAsB64(this.getEndState()));
};
/**
 * optional bytes end_state = 8;
 * Note that Uint8Array is not supported on all browsers.
 * @see http://caniuse.com/Uint8Array
 * This is a type-conversion wrapper around `getEndState()`
 * @return {!Uint8Array}
 */
proto.flow.entities.Chunk.prototype.getEndState_asU8 = function () {
    return /** @type {!Uint8Array} */ (jspb.Message.bytesAsU8(this.getEndState()));
};
/**
 * @param {!(string|Uint8Array)} value
 * @return {!proto.flow.entities.Chunk} returns this
 */
proto.flow.entities.Chunk.prototype.setEndState = function (value) {
    return jspb.Message.setProto3BytesField(this, 8, value);
};
/**
 * optional bytes execution_data_id = 9;
 * @return {!(string|Uint8Array)}
 */
proto.flow.entities.Chunk.prototype.getExecutionDataId = function () {
    return /** @type {!(string|Uint8Array)} */ (jspb.Message.getFieldWithDefault(this, 9, ""));
};
/**
 * optional bytes execution_data_id = 9;
 * This is a type-conversion wrapper around `getExecutionDataId()`
 * @return {string}
 */
proto.flow.entities.Chunk.prototype.getExecutionDataId_asB64 = function () {
    return /** @type {string} */ (jspb.Message.bytesAsB64(this.getExecutionDataId()));
};
/**
 * optional bytes execution_data_id = 9;
 * Note that Uint8Array is not supported on all browsers.
 * @see http://caniuse.com/Uint8Array
 * This is a type-conversion wrapper around `getExecutionDataId()`
 * @return {!Uint8Array}
 */
proto.flow.entities.Chunk.prototype.getExecutionDataId_asU8 = function () {
    return /** @type {!Uint8Array} */ (jspb.Message.bytesAsU8(this.getExecutionDataId()));
};
/**
 * @param {!(string|Uint8Array)} value
 * @return {!proto.flow.entities.Chunk} returns this
 */
proto.flow.entities.Chunk.prototype.setExecutionDataId = function (value) {
    return jspb.Message.setProto3BytesField(this, 9, value);
};
/**
 * optional bytes state_delta_commitment = 10;
 * @return {!(string|Uint8Array)}
 */
proto.flow.entities.Chunk.prototype.getStateDeltaCommitment = function () {
    return /** @type {!(string|Uint8Array)} */ (jspb.Message.getFieldWithDefault(this, 10, ""));
};
/**
 * optional bytes state_delta_commitment = 10;
 * This is a type-conversion wrapper around `getStateDeltaCommitment()`
 * @return {string}
 */
proto.flow.entities.Chunk.prototype.getStateDeltaCommitment_asB64 = function () {
    return /** @type {string} */ (jspb.Message.bytesAsB64(this.getStateDeltaCommitment()));
};
/**
 * optional bytes state_delta_commitment = 10;
 * Note that Uint8Array is not supported on all browsers.
 * @see http://caniuse.com/Uint8Array
 * This is a type-conversion wrapper around `getStateDeltaCommitment()`
 * @return {!Uint8Array}
 */
proto.flow.entities.Chunk.prototype.getStateDeltaCommitment_asU8 = function () {
    return /** @type {!Uint8Array} */ (jspb.Message.bytesAsU8(this.getStateDeltaCommitment()));
};
/**
 * @param {!(string|Uint8Array)} value
 * @return {!proto.flow.entities.Chunk} returns this
 */
proto.flow.entities.Chunk.prototype.setStateDeltaCommitment = function (value) {
    return jspb.Message.setProto3BytesField(this, 10, value);
};
if (jspb.Message.GENERATE_TO_OBJECT) {
    /**
     * Creates an object representation of this proto.
     * Field names that are reserved in JavaScript and will be renamed to pb_name.
     * Optional fields that are not set will be set to undefined.
     * To access a reserved field use, foo.pb_<name>, eg, foo.pb_default.
     * For the list of reserved names please see:
     *     net/proto2/compiler/js/internal/generator.cc#kKeyword.
     * @param {boolean=} opt_includeInstance Deprecated. whether to include the
     *     JSPB instance for transitional soy proto support:
     *     http://goto/soy-param-migration
     * @return {!Object}
     */
    proto.flow.entities.ServiceEvent.prototype.toObject = function (opt_includeInstance) {
        return proto.flow.entities.ServiceEvent.toObject(opt_includeInstance, this);
    };
    /**
     * Static version of the {@see toObject} method.
     * @param {boolean|undefined} includeInstance Deprecated. Whether to include
     *     the JSPB instance for transitional soy proto support:
     *     http://goto/soy-param-migration
     * @param {!proto.flow.entities.ServiceEvent} msg The msg instance to transform.
     * @return {!Object}
     * @suppress {unusedLocalVariables} f is only used for nested messages
     */
    proto.flow.entities.ServiceEvent.toObject = function (includeInstance, msg) {
        var f, obj = {
            type: jspb.Message.getFieldWithDefault(msg, 1, ""),
            payload: msg.getPayload_asB64()
        };
        if (includeInstance) {
            obj.$jspbMessageInstance = msg;
        }
        return obj;
    };
}
/**
 * Deserializes binary data (in protobuf wire format).
 * @param {jspb.ByteSource} bytes The bytes to deserialize.
 * @return {!proto.flow.entities.ServiceEvent}
 */
proto.flow.entities.ServiceEvent.deserializeBinary = function (bytes) {
    var reader = new jspb.BinaryReader(bytes);
    var msg = new proto.flow.entities.ServiceEvent;
    return proto.flow.entities.ServiceEvent.deserializeBinaryFromReader(msg, reader);
};
/**
 * Deserializes binary data (in protobuf wire format) from the
 * given reader into the given message object.
 * @param {!proto.flow.entities.ServiceEvent} msg The message object to deserialize into.
 * @param {!jspb.BinaryReader} reader The BinaryReader to use.
 * @return {!proto.flow.entities.ServiceEvent}
 */
proto.flow.entities.ServiceEvent.deserializeBinaryFromReader = function (msg, reader) {
    while (reader.nextField()) {
        if (reader.isEndGroup()) {
            break;
        }
        var field = reader.getFieldNumber();
        switch (field) {
            case 1:
                var value = /** @type {string} */ (reader.readString());
                msg.setType(value);
                break;
            case 2:
                var value = /** @type {!Uint8Array} */ (reader.readBytes());
                msg.setPayload(value);
                break;
            default:
                reader.skipField();
                break;
        }
    }
    return msg;
};
/**
 * Serializes the message to binary data (in protobuf wire format).
 * @return {!Uint8Array}
 */
proto.flow.entities.ServiceEvent.prototype.serializeBinary = function () {
    var writer = new jspb.BinaryWriter();
    proto.flow.entities.ServiceEvent.serializeBinaryToWriter(this, writer);
    return writer.getResultBuffer();
};
/**
 * Serializes the given message to binary data (in protobuf wire
 * format), writing to the given BinaryWriter.
 * @param {!proto.flow.entities.ServiceEvent} message
 * @param {!jspb.BinaryWriter} writer
 * @suppress {unusedLocalVariables} f is only used for nested messages
 */
proto.flow.entities.ServiceEvent.serializeBinaryToWriter = function (message, writer) {
    var f = undefined;
    f = message.getType();
    if (f.length > 0) {
        writer.writeString(1, f);
    }
    f = message.getPayload_asU8();
    if (f.length > 0) {
        writer.writeBytes(2, f);
    }
};
/**
 * optional string type = 1;
 * @return {string}
 */
proto.flow.entities.ServiceEvent.prototype.getType = function () {
    return /** @type {string} */ (jspb.Message.getFieldWithDefault(this, 1, ""));
};
/**
 * @param {string} value
 * @return {!proto.flow.entities.ServiceEvent} returns this
 */
proto.flow.entities.ServiceEvent.prototype.setType = function (value) {
    return jspb.Message.setProto3StringField(this, 1, value);
};
/**
 * optional bytes payload = 2;
 * @return {!(string|Uint8Array)}
 */
proto.flow.entities.ServiceEvent.prototype.getPayload = function () {
    return /** @type {!(string|Uint8Array)} */ (jspb.Message.getFieldWithDefault(this, 2, ""));
};
/**
 * optional bytes payload = 2;
 * This is a type-conversion wrapper around `getPayload()`
 * @return {string}
 */
proto.flow.entities.ServiceEvent.prototype.getPayload_asB64 = function () {
    return /** @type {string} */ (jspb.Message.bytesAsB64(this.getPayload()));
};
/**
 * optional bytes payload = 2;
 * Note that Uint8Array is not supported on all browsers.
 * @see http://caniuse.com/Uint8Array
 * This is a type-conversion wrapper around `getPayload()`
 * @return {!Uint8Array}
 */
proto.flow.entities.ServiceEvent.prototype.getPayload_asU8 = function () {
    return /** @type {!Uint8Array} */ (jspb.Message.bytesAsU8(this.getPayload()));
};
/**
 * @param {!(string|Uint8Array)} value
 * @return {!proto.flow.entities.ServiceEvent} returns this
 */
proto.flow.entities.ServiceEvent.prototype.setPayload = function (value) {
    return jspb.Message.setProto3BytesField(this, 2, value);
};
/**
 * List of repeated fields within this message type.
 * @private {!Array<number>}
 * @const
 */
proto.flow.entities.ExecutionReceiptMeta.repeatedFields_ = [3];
if (jspb.Message.GENERATE_TO_OBJECT) {
    /**
     * Creates an object representation of this proto.
     * Field names that are reserved in JavaScript and will be renamed to pb_name.
     * Optional fields that are not set will be set to undefined.
     * To access a reserved field use, foo.pb_<name>, eg, foo.pb_default.
     * For the list of reserved names please see:
     *     net/proto2/compiler/js/internal/generator.cc#kKeyword.
     * @param {boolean=} opt_includeInstance Deprecated. whether to include the
     *     JSPB instance for transitional soy proto support:
     *     http://goto/soy-param-migration
     * @return {!Object}
     */
    proto.flow.entities.ExecutionReceiptMeta.prototype.toObject = function (opt_includeInstance) {
        return proto.flow.entities.ExecutionReceiptMeta.toObject(opt_includeInstance, this);
    };
    /**
     * Static version of the {@see toObject} method.
     * @param {boolean|undefined} includeInstance Deprecated. Whether to include
     *     the JSPB instance for transitional soy proto support:
     *     http://goto/soy-param-migration
     * @param {!proto.flow.entities.ExecutionReceiptMeta} msg The msg instance to transform.
     * @return {!Object}
     * @suppress {unusedLocalVariables} f is only used for nested messages
     */
    proto.flow.entities.ExecutionReceiptMeta.toObject = function (includeInstance, msg) {
        var f, obj = {
            executorId: msg.getExecutorId_asB64(),
            resultId: msg.getResultId_asB64(),
            spocksList: msg.getSpocksList_asB64(),
            executorSignature: msg.getExecutorSignature_asB64()
        };
        if (includeInstance) {
            obj.$jspbMessageInstance = msg;
        }
        return obj;
    };
}
/**
 * Deserializes binary data (in protobuf wire format).
 * @param {jspb.ByteSource} bytes The bytes to deserialize.
 * @return {!proto.flow.entities.ExecutionReceiptMeta}
 */
proto.flow.entities.ExecutionReceiptMeta.deserializeBinary = function (bytes) {
    var reader = new jspb.BinaryReader(bytes);
    var msg = new proto.flow.entities.ExecutionReceiptMeta;
    return proto.flow.entities.ExecutionReceiptMeta.deserializeBinaryFromReader(msg, reader);
};
/**
 * Deserializes binary data (in protobuf wire format) from the
 * given reader into the given message object.
 * @param {!proto.flow.entities.ExecutionReceiptMeta} msg The message object to deserialize into.
 * @param {!jspb.BinaryReader} reader The BinaryReader to use.
 * @return {!proto.flow.entities.ExecutionReceiptMeta}
 */
proto.flow.entities.ExecutionReceiptMeta.deserializeBinaryFromReader = function (msg, reader) {
    while (reader.nextField()) {
        if (reader.isEndGroup()) {
            break;
        }
        var field = reader.getFieldNumber();
        switch (field) {
            case 1:
                var value = /** @type {!Uint8Array} */ (reader.readBytes());
                msg.setExecutorId(value);
                break;
            case 2:
                var value = /** @type {!Uint8Array} */ (reader.readBytes());
                msg.setResultId(value);
                break;
            case 3:
                var value = /** @type {!Uint8Array} */ (reader.readBytes());
                msg.addSpocks(value);
                break;
            case 4:
                var value = /** @type {!Uint8Array} */ (reader.readBytes());
                msg.setExecutorSignature(value);
                break;
            default:
                reader.skipField();
                break;
        }
    }
    return msg;
};
/**
 * Serializes the message to binary data (in protobuf wire format).
 * @return {!Uint8Array}
 */
proto.flow.entities.ExecutionReceiptMeta.prototype.serializeBinary = function () {
    var writer = new jspb.BinaryWriter();
    proto.flow.entities.ExecutionReceiptMeta.serializeBinaryToWriter(this, writer);
    return writer.getResultBuffer();
};
/**
 * Serializes the given message to binary data (in protobuf wire
 * format), writing to the given BinaryWriter.
 * @param {!proto.flow.entities.ExecutionReceiptMeta} message
 * @param {!jspb.BinaryWriter} writer
 * @suppress {unusedLocalVariables} f is only used for nested messages
 */
proto.flow.entities.ExecutionReceiptMeta.serializeBinaryToWriter = function (message, writer) {
    var f = undefined;
    f = message.getExecutorId_asU8();
    if (f.length > 0) {
        writer.writeBytes(1, f);
    }
    f = message.getResultId_asU8();
    if (f.length > 0) {
        writer.writeBytes(2, f);
    }
    f = message.getSpocksList_asU8();
    if (f.length > 0) {
        writer.writeRepeatedBytes(3, f);
    }
    f = message.getExecutorSignature_asU8();
    if (f.length > 0) {
        writer.writeBytes(4, f);
    }
};
/**
 * optional bytes executor_id = 1;
 * @return {!(string|Uint8Array)}
 */
proto.flow.entities.ExecutionReceiptMeta.prototype.getExecutorId = function () {
    return /** @type {!(string|Uint8Array)} */ (jspb.Message.getFieldWithDefault(this, 1, ""));
};
/**
 * optional bytes executor_id = 1;
 * This is a type-conversion wrapper around `getExecutorId()`
 * @return {string}
 */
proto.flow.entities.ExecutionReceiptMeta.prototype.getExecutorId_asB64 = function () {
    return /** @type {string} */ (jspb.Message.bytesAsB64(this.getExecutorId()));
};
/**
 * optional bytes executor_id = 1;
 * Note that Uint8Array is not supported on all browsers.
 * @see http://caniuse.com/Uint8Array
 * This is a type-conversion wrapper around `getExecutorId()`
 * @return {!Uint8Array}
 */
proto.flow.entities.ExecutionReceiptMeta.prototype.getExecutorId_asU8 = function () {
    return /** @type {!Uint8Array} */ (jspb.Message.bytesAsU8(this.getExecutorId()));
};
/**
 * @param {!(string|Uint8Array)} value
 * @return {!proto.flow.entities.ExecutionReceiptMeta} returns this
 */
proto.flow.entities.ExecutionReceiptMeta.prototype.setExecutorId = function (value) {
    return jspb.Message.setProto3BytesField(this, 1, value);
};
/**
 * optional bytes result_id = 2;
 * @return {!(string|Uint8Array)}
 */
proto.flow.entities.ExecutionReceiptMeta.prototype.getResultId = function () {
    return /** @type {!(string|Uint8Array)} */ (jspb.Message.getFieldWithDefault(this, 2, ""));
};
/**
 * optional bytes result_id = 2;
 * This is a type-conversion wrapper around `getResultId()`
 * @return {string}
 */
proto.flow.entities.ExecutionReceiptMeta.prototype.getResultId_asB64 = function () {
    return /** @type {string} */ (jspb.Message.bytesAsB64(this.getResultId()));
};
/**
 * optional bytes result_id = 2;
 * Note that Uint8Array is not supported on all browsers.
 * @see http://caniuse.com/Uint8Array
 * This is a type-conversion wrapper around `getResultId()`
 * @return {!Uint8Array}
 */
proto.flow.entities.ExecutionReceiptMeta.prototype.getResultId_asU8 = function () {
    return /** @type {!Uint8Array} */ (jspb.Message.bytesAsU8(this.getResultId()));
};
/**
 * @param {!(string|Uint8Array)} value
 * @return {!proto.flow.entities.ExecutionReceiptMeta} returns this
 */
proto.flow.entities.ExecutionReceiptMeta.prototype.setResultId = function (value) {
    return jspb.Message.setProto3BytesField(this, 2, value);
};
/**
 * repeated bytes spocks = 3;
 * @return {!(Array<!Uint8Array>|Array<string>)}
 */
proto.flow.entities.ExecutionReceiptMeta.prototype.getSpocksList = function () {
    return /** @type {!(Array<!Uint8Array>|Array<string>)} */ (jspb.Message.getRepeatedField(this, 3));
};
/**
 * repeated bytes spocks = 3;
 * This is a type-conversion wrapper around `getSpocksList()`
 * @return {!Array<string>}
 */
proto.flow.entities.ExecutionReceiptMeta.prototype.getSpocksList_asB64 = function () {
    return /** @type {!Array<string>} */ (jspb.Message.bytesListAsB64(this.getSpocksList()));
};
/**
 * repeated bytes spocks = 3;
 * Note that Uint8Array is not supported on all browsers.
 * @see http://caniuse.com/Uint8Array
 * This is a type-conversion wrapper around `getSpocksList()`
 * @return {!Array<!Uint8Array>}
 */
proto.flow.entities.ExecutionReceiptMeta.prototype.getSpocksList_asU8 = function () {
    return /** @type {!Array<!Uint8Array>} */ (jspb.Message.bytesListAsU8(this.getSpocksList()));
};
/**
 * @param {!(Array<!Uint8Array>|Array<string>)} value
 * @return {!proto.flow.entities.ExecutionReceiptMeta} returns this
 */
proto.flow.entities.ExecutionReceiptMeta.prototype.setSpocksList = function (value) {
    return jspb.Message.setField(this, 3, value || []);
};
/**
 * @param {!(string|Uint8Array)} value
 * @param {number=} opt_index
 * @return {!proto.flow.entities.ExecutionReceiptMeta} returns this
 */
proto.flow.entities.ExecutionReceiptMeta.prototype.addSpocks = function (value, opt_index) {
    return jspb.Message.addToRepeatedField(this, 3, value, opt_index);
};
/**
 * Clears the list making it empty but non-null.
 * @return {!proto.flow.entities.ExecutionReceiptMeta} returns this
 */
proto.flow.entities.ExecutionReceiptMeta.prototype.clearSpocksList = function () {
    return this.setSpocksList([]);
};
/**
 * optional bytes executor_signature = 4;
 * @return {!(string|Uint8Array)}
 */
proto.flow.entities.ExecutionReceiptMeta.prototype.getExecutorSignature = function () {
    return /** @type {!(string|Uint8Array)} */ (jspb.Message.getFieldWithDefault(this, 4, ""));
};
/**
 * optional bytes executor_signature = 4;
 * This is a type-conversion wrapper around `getExecutorSignature()`
 * @return {string}
 */
proto.flow.entities.ExecutionReceiptMeta.prototype.getExecutorSignature_asB64 = function () {
    return /** @type {string} */ (jspb.Message.bytesAsB64(this.getExecutorSignature()));
};
/**
 * optional bytes executor_signature = 4;
 * Note that Uint8Array is not supported on all browsers.
 * @see http://caniuse.com/Uint8Array
 * This is a type-conversion wrapper around `getExecutorSignature()`
 * @return {!Uint8Array}
 */
proto.flow.entities.ExecutionReceiptMeta.prototype.getExecutorSignature_asU8 = function () {
    return /** @type {!Uint8Array} */ (jspb.Message.bytesAsU8(this.getExecutorSignature()));
};
/**
 * @param {!(string|Uint8Array)} value
 * @return {!proto.flow.entities.ExecutionReceiptMeta} returns this
 */
proto.flow.entities.ExecutionReceiptMeta.prototype.setExecutorSignature = function (value) {
    return jspb.Message.setProto3BytesField(this, 4, value);
};
goog.object.extend(exports, proto.flow.entities);
