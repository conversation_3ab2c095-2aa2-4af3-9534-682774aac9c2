{"version": 3, "file": "bundle.js", "sources": ["../node_modules/uuid/dist/esm-browser/native.js", "../packages/typedefs/dist/typedefs.module.js", "../packages/util-logger/dist/util-logger.module.js", "../packages/util-invariant/dist/util-invariant.module.js", "../node_modules/uuid/dist/esm-browser/rng.js", "../node_modules/uuid/dist/esm-browser/stringify.js", "../node_modules/uuid/dist/esm-browser/v4.js", "../packages/util-actor/dist/actor.module.js", "../packages/config/dist/config.module.js", "../packages/rlp/dist/rlp.module.js", "../packages/util-address/dist/util-address.module.js", "../packages/transport-http/dist/index.module.js", "../packages/sdk/dist/sdk.module.js"], "sourcesContent": ["const randomUUID = typeof crypto !== 'undefined' && crypto.randomUUID && crypto.randomUUID.bind(crypto);\nexport default {\n  randomUUID\n};", "var InteractionTag = /*#__PURE__*/function (InteractionTag) {\n  InteractionTag[\"UNKNOWN\"] = \"UNKNOWN\";\n  InteractionTag[\"SCRIPT\"] = \"SCRIPT\";\n  InteractionTag[\"TRANSACTION\"] = \"TRANSACTION\";\n  InteractionTag[\"GET_TRANSACTION_STATUS\"] = \"GET_TRANSACTION_STATUS\";\n  InteractionTag[\"GET_ACCOUNT\"] = \"GET_ACCOUNT\";\n  InteractionTag[\"GET_EVENTS\"] = \"GET_EVENTS\";\n  InteractionTag[\"PING\"] = \"PING\";\n  InteractionTag[\"GET_TRANSACTION\"] = \"GET_TRANSACTION\";\n  InteractionTag[\"GET_BLOCK\"] = \"GET_BLOCK\";\n  InteractionTag[\"GET_BLOCK_HEADER\"] = \"GET_BLOCK_HEADER\";\n  InteractionTag[\"GET_COLLECTION\"] = \"GET_COLLECTION\";\n  InteractionTag[\"GET_NETWORK_PARAMETERS\"] = \"GET_NETWORK_PARAMETERS\";\n  InteractionTag[\"SUBSCRIBE_EVENTS\"] = \"SUBSCRIBE_EVENTS\";\n  InteractionTag[\"GET_NODE_VERSION_INFO\"] = \"GET_NODE_VERSION_INFO\";\n  return InteractionTag;\n}({});\nvar InteractionStatus = /*#__PURE__*/function (InteractionStatus) {\n  InteractionStatus[\"BAD\"] = \"BAD\";\n  InteractionStatus[\"OK\"] = \"OK\";\n  return InteractionStatus;\n}({});\nvar TransactionRole = /*#__PURE__*/function (TransactionRole) {\n  TransactionRole[\"AUTHORIZER\"] = \"authorizer\";\n  TransactionRole[\"PAYER\"] = \"payer\";\n  TransactionRole[\"PROPOSER\"] = \"proposer\";\n  return TransactionRole;\n}({});\nvar InteractionResolverKind = /*#__PURE__*/function (InteractionResolverKind) {\n  InteractionResolverKind[\"ARGUMENT\"] = \"ARGUMENT\";\n  InteractionResolverKind[\"ACCOUNT\"] = \"ACCOUNT\";\n  return InteractionResolverKind;\n}({});\n\nvar FvmErrorCode = /*#__PURE__*/function (FvmErrorCode) {\n  FvmErrorCode[FvmErrorCode[\"UNKNOWN_ERROR\"] = -1] = \"UNKNOWN_ERROR\";\n  FvmErrorCode[FvmErrorCode[\"TX_VALIDATION_ERROR\"] = 1000] = \"TX_VALIDATION_ERROR\";\n  FvmErrorCode[FvmErrorCode[\"INVALID_TX_BYTE_SIZE_ERROR\"] = 1001] = \"INVALID_TX_BYTE_SIZE_ERROR\";\n  FvmErrorCode[FvmErrorCode[\"INVALID_REFERENCE_BLOCK_ERROR\"] = 1002] = \"INVALID_REFERENCE_BLOCK_ERROR\";\n  FvmErrorCode[FvmErrorCode[\"EXPIRED_TRANSACTION_ERROR\"] = 1003] = \"EXPIRED_TRANSACTION_ERROR\";\n  FvmErrorCode[FvmErrorCode[\"INVALID_SCRIPT_ERROR\"] = 1004] = \"INVALID_SCRIPT_ERROR\";\n  FvmErrorCode[FvmErrorCode[\"INVALID_GAS_LIMIT_ERROR\"] = 1005] = \"INVALID_GAS_LIMIT_ERROR\";\n  FvmErrorCode[FvmErrorCode[\"INVALID_PROPOSAL_SIGNATURE_ERROR\"] = 1006] = \"INVALID_PROPOSAL_SIGNATURE_ERROR\";\n  FvmErrorCode[FvmErrorCode[\"INVALID_PROPOSAL_SEQ_NUMBER_ERROR\"] = 1007] = \"INVALID_PROPOSAL_SEQ_NUMBER_ERROR\";\n  FvmErrorCode[FvmErrorCode[\"INVALID_PAYLOAD_SIGNATURE_ERROR\"] = 1008] = \"INVALID_PAYLOAD_SIGNATURE_ERROR\";\n  FvmErrorCode[FvmErrorCode[\"INVALID_ENVELOPE_SIGNATURE_ERROR\"] = 1009] = \"INVALID_ENVELOPE_SIGNATURE_ERROR\";\n  FvmErrorCode[FvmErrorCode[\"FVM_INTERNAL_ERROR\"] = 1050] = \"FVM_INTERNAL_ERROR\";\n  FvmErrorCode[FvmErrorCode[\"VALUE_ERROR\"] = 1051] = \"VALUE_ERROR\";\n  FvmErrorCode[FvmErrorCode[\"INVALID_ARGUMENT_ERROR\"] = 1052] = \"INVALID_ARGUMENT_ERROR\";\n  FvmErrorCode[FvmErrorCode[\"INVALID_ADDRESS_ERROR\"] = 1053] = \"INVALID_ADDRESS_ERROR\";\n  FvmErrorCode[FvmErrorCode[\"INVALID_LOCATION_ERROR\"] = 1054] = \"INVALID_LOCATION_ERROR\";\n  FvmErrorCode[FvmErrorCode[\"ACCOUNT_AUTHORIZATION_ERROR\"] = 1055] = \"ACCOUNT_AUTHORIZATION_ERROR\";\n  FvmErrorCode[FvmErrorCode[\"OPERATION_AUTHORIZATION_ERROR\"] = 1056] = \"OPERATION_AUTHORIZATION_ERROR\";\n  FvmErrorCode[FvmErrorCode[\"OPERATION_NOT_SUPPORTED_ERROR\"] = 1057] = \"OPERATION_NOT_SUPPORTED_ERROR\";\n  FvmErrorCode[FvmErrorCode[\"BLOCK_HEIGHT_OUT_OF_RANGE_ERROR\"] = 1058] = \"BLOCK_HEIGHT_OUT_OF_RANGE_ERROR\";\n  FvmErrorCode[FvmErrorCode[\"EXECUTION_ERROR\"] = 1100] = \"EXECUTION_ERROR\";\n  FvmErrorCode[FvmErrorCode[\"CADENCE_RUNTIME_ERROR\"] = 1101] = \"CADENCE_RUNTIME_ERROR\";\n  FvmErrorCode[FvmErrorCode[\"ENCODING_UNSUPPORTED_VALUE\"] = 1102] = \"ENCODING_UNSUPPORTED_VALUE\";\n  FvmErrorCode[FvmErrorCode[\"STORAGE_CAPACITY_EXCEEDED\"] = 1103] = \"STORAGE_CAPACITY_EXCEEDED\";\n  FvmErrorCode[FvmErrorCode[\"GAS_LIMIT_EXCEEDED_ERROR\"] = 1104] = \"GAS_LIMIT_EXCEEDED_ERROR\";\n  FvmErrorCode[FvmErrorCode[\"EVENT_LIMIT_EXCEEDED_ERROR\"] = 1105] = \"EVENT_LIMIT_EXCEEDED_ERROR\";\n  FvmErrorCode[FvmErrorCode[\"LEDGER_INTERACTION_LIMIT_EXCEEDED_ERROR\"] = 1106] = \"LEDGER_INTERACTION_LIMIT_EXCEEDED_ERROR\";\n  FvmErrorCode[FvmErrorCode[\"STATE_KEY_SIZE_LIMIT_ERROR\"] = 1107] = \"STATE_KEY_SIZE_LIMIT_ERROR\";\n  FvmErrorCode[FvmErrorCode[\"STATE_VALUE_SIZE_LIMIT_ERROR\"] = 1108] = \"STATE_VALUE_SIZE_LIMIT_ERROR\";\n  FvmErrorCode[FvmErrorCode[\"TRANSACTION_FEE_DEDUCTION_FAILED_ERROR\"] = 1109] = \"TRANSACTION_FEE_DEDUCTION_FAILED_ERROR\";\n  FvmErrorCode[FvmErrorCode[\"COMPUTATION_LIMIT_EXCEEDED_ERROR\"] = 1110] = \"COMPUTATION_LIMIT_EXCEEDED_ERROR\";\n  FvmErrorCode[FvmErrorCode[\"MEMORY_LIMIT_EXCEEDED_ERROR\"] = 1111] = \"MEMORY_LIMIT_EXCEEDED_ERROR\";\n  FvmErrorCode[FvmErrorCode[\"COULD_NOT_DECODE_EXECUTION_PARAMETER_FROM_STATE\"] = 1112] = \"COULD_NOT_DECODE_EXECUTION_PARAMETER_FROM_STATE\";\n  FvmErrorCode[FvmErrorCode[\"SCRIPT_EXECUTION_TIMED_OUT_ERROR\"] = 1113] = \"SCRIPT_EXECUTION_TIMED_OUT_ERROR\";\n  FvmErrorCode[FvmErrorCode[\"SCRIPT_EXECUTION_CANCELLED_ERROR\"] = 1114] = \"SCRIPT_EXECUTION_CANCELLED_ERROR\";\n  FvmErrorCode[FvmErrorCode[\"EVENT_ENCODING_ERROR\"] = 1115] = \"EVENT_ENCODING_ERROR\";\n  FvmErrorCode[FvmErrorCode[\"INVALID_INTERNAL_STATE_ACCESS_ERROR\"] = 1116] = \"INVALID_INTERNAL_STATE_ACCESS_ERROR\";\n  FvmErrorCode[FvmErrorCode[\"INSUFFICIENT_PAYER_BALANCE\"] = 1118] = \"INSUFFICIENT_PAYER_BALANCE\";\n  FvmErrorCode[FvmErrorCode[\"ACCOUNT_ERROR\"] = 1200] = \"ACCOUNT_ERROR\";\n  FvmErrorCode[FvmErrorCode[\"ACCOUNT_NOT_FOUND_ERROR\"] = 1201] = \"ACCOUNT_NOT_FOUND_ERROR\";\n  FvmErrorCode[FvmErrorCode[\"ACCOUNT_PUBLIC_KEY_NOT_FOUND_ERROR\"] = 1202] = \"ACCOUNT_PUBLIC_KEY_NOT_FOUND_ERROR\";\n  FvmErrorCode[FvmErrorCode[\"ACCOUNT_ALREADY_EXISTS_ERROR\"] = 1203] = \"ACCOUNT_ALREADY_EXISTS_ERROR\";\n  FvmErrorCode[FvmErrorCode[\"FROZEN_ACCOUNT_ERROR\"] = 1204] = \"FROZEN_ACCOUNT_ERROR\";\n  FvmErrorCode[FvmErrorCode[\"ACCOUNT_STORAGE_NOT_INITIALIZED_ERROR\"] = 1205] = \"ACCOUNT_STORAGE_NOT_INITIALIZED_ERROR\";\n  FvmErrorCode[FvmErrorCode[\"ACCOUNT_PUBLIC_KEY_LIMIT_ERROR\"] = 1206] = \"ACCOUNT_PUBLIC_KEY_LIMIT_ERROR\";\n  FvmErrorCode[FvmErrorCode[\"CONTRACT_ERROR\"] = 1250] = \"CONTRACT_ERROR\";\n  FvmErrorCode[FvmErrorCode[\"CONTRACT_NOT_FOUND_ERROR\"] = 1251] = \"CONTRACT_NOT_FOUND_ERROR\";\n  FvmErrorCode[FvmErrorCode[\"CONTRACT_NAMES_NOT_FOUND_ERROR\"] = 1252] = \"CONTRACT_NAMES_NOT_FOUND_ERROR\";\n  FvmErrorCode[FvmErrorCode[\"EVM_EXECUTION_ERROR\"] = 1300] = \"EVM_EXECUTION_ERROR\";\n  return FvmErrorCode;\n}({});\n\nvar SubscriptionTopic = /*#__PURE__*/function (SubscriptionTopic) {\n  SubscriptionTopic[\"BLOCKS\"] = \"blocks\";\n  SubscriptionTopic[\"BLOCK_HEADERS\"] = \"block_headers\";\n  SubscriptionTopic[\"BLOCK_DIGESTS\"] = \"block_digests\";\n  SubscriptionTopic[\"ACCOUNT_STATUSES\"] = \"account_statuses\";\n  SubscriptionTopic[\"TRANSACTION_STATUSES\"] = \"transaction_statuses\";\n  SubscriptionTopic[\"EVENTS\"] = \"events\";\n  return SubscriptionTopic;\n}({});\n\nvar SignatureAlgorithm = /*#__PURE__*/function (SignatureAlgorithm) {\n  SignatureAlgorithm[SignatureAlgorithm[\"ECDSA_P256\"] = 1] = \"ECDSA_P256\";\n  SignatureAlgorithm[SignatureAlgorithm[\"ECDSA_secp256k1\"] = 2] = \"ECDSA_secp256k1\";\n  SignatureAlgorithm[SignatureAlgorithm[\"BLS_BLS12_381\"] = 3] = \"BLS_BLS12_381\";\n  return SignatureAlgorithm;\n}({});\nvar HashAlgorithm = /*#__PURE__*/function (HashAlgorithm) {\n  HashAlgorithm[HashAlgorithm[\"SHA2_256\"] = 1] = \"SHA2_256\";\n  HashAlgorithm[HashAlgorithm[\"SHA2_384\"] = 2] = \"SHA2_384\";\n  HashAlgorithm[HashAlgorithm[\"SHA3_256\"] = 3] = \"SHA3_256\";\n  HashAlgorithm[HashAlgorithm[\"SHA3_384\"] = 4] = \"SHA3_384\";\n  HashAlgorithm[HashAlgorithm[\"KMAC128_BLS_BLS12_381\"] = 5] = \"KMAC128_BLS_BLS12_381\";\n  return HashAlgorithm;\n}({});\n\n/**\n * BlockDigest holds lightweight block information which includes only block id, block height and block timestamp.\n */\n\n/**\n * Header contains all meta-data for a block, as well as a hash representing\n * the combined payload of the entire block. It is what consensus nodes agree\n * on after validating the contents against the payload hash.\n */\n\n/**\n * The execution status of the transaction.\n */\nvar TransactionExecutionStatus = /*#__PURE__*/function (TransactionExecutionStatus) {\n  TransactionExecutionStatus[TransactionExecutionStatus[\"UNKNOWN\"] = 0] = \"UNKNOWN\";\n  TransactionExecutionStatus[TransactionExecutionStatus[\"PENDING\"] = 1] = \"PENDING\";\n  TransactionExecutionStatus[TransactionExecutionStatus[\"FINALIZED\"] = 2] = \"FINALIZED\";\n  TransactionExecutionStatus[TransactionExecutionStatus[\"EXECUTED\"] = 3] = \"EXECUTED\";\n  TransactionExecutionStatus[TransactionExecutionStatus[\"SEALED\"] = 4] = \"SEALED\";\n  TransactionExecutionStatus[TransactionExecutionStatus[\"EXPIRED\"] = 5] = \"EXPIRED\";\n  return TransactionExecutionStatus;\n}({});\n\nexport { FvmErrorCode, HashAlgorithm, InteractionResolverKind, InteractionStatus, InteractionTag, SignatureAlgorithm, SubscriptionTopic, TransactionExecutionStatus, TransactionRole };\n//# sourceMappingURL=typedefs.module.js.map\n", "// Config dependency injected into logger to break circular dependency\nlet config = null;\nconst setConfig = _config => {\n  config = _config;\n};\n\n/**\n * The levels of the logger\n */\nlet LEVELS = /*#__PURE__*/function (LEVELS) {\n  LEVELS[LEVELS[\"debug\"] = 5] = \"debug\";\n  LEVELS[LEVELS[\"info\"] = 4] = \"info\";\n  LEVELS[LEVELS[\"log\"] = 3] = \"log\";\n  LEVELS[LEVELS[\"warn\"] = 2] = \"warn\";\n  LEVELS[LEVELS[\"error\"] = 1] = \"error\";\n  return LEVELS;\n}({});\n\n/**\n * Builds a message formatted for the logger\n * @param options - The options for the log\n * @param options.title - The title of the log\n * @param options.message - The message of the log\n * @returns The message formatted for the logger\n * @example\n * buildLoggerMessageArgs({ title: \"My Title\", message: \"My Message\" })\n */\nconst buildLoggerMessageArgs = options => {\n  const {\n    title,\n    message\n  } = options;\n  return [`\n    %c${title}\n    ============================\n\n    ${message}\n\n    ============================\n    `.replace(/\\n[^\\S\\r\\n]+/g, \"\\n\").trim(), \"font-weight:bold;font-family:monospace;\"];\n};\n\n/**\n * Logs messages based on the level of the message and the level set in the config\n * @param options - The options for the log\n * @param options.title - The title of the log\n * @param options.message - The message of the log\n * @param options.level - The level of the log\n * @param options.always - Whether to always show the log\n * @example\n * log({ title: \"My Title\", message: \"My Message\", level: LEVELS.warn, always: false })\n */\nconst log = async options => {\n  const {\n    title,\n    message,\n    level,\n    always\n  } = options;\n  const configLoggerLevel = (await config?.()?.get(\"logger.level\")) ?? LEVELS.warn;\n\n  // If config level is below message level then don't show it\n  if (!always && configLoggerLevel < level) return;\n  const loggerMessageArgs = buildLoggerMessageArgs({\n    title,\n    message\n  });\n  switch (level) {\n    case LEVELS.debug:\n      console.debug(...loggerMessageArgs);\n      break;\n    case LEVELS.info:\n      console.info(...loggerMessageArgs);\n      break;\n    case LEVELS.warn:\n      console.warn(...loggerMessageArgs);\n      break;\n    case LEVELS.error:\n      console.error(...loggerMessageArgs);\n      break;\n    default:\n      console.log(...loggerMessageArgs);\n  }\n};\n\n/**\n * Logs a deprecation notice.  If a callback is provided this function returns a function that will call the callback and log the deprecation notice, otherwise it just logs the deprecation notice.\n * @param options - The options for the log\n * @param options.pkg - The package that is being deprecated\n * @param options.subject - The subject of the deprecation\n * @param options.transition - The transition path for the deprecation\n * @param options.level - The level of the log\n * @param options.message - The message of the log\n * @param options.callback - A callback to run after the log\n * @returns A function that will call the callback and log the deprecation notice if the callback is provided\n * @example\n * // Logs a deprecation notice\n * log.deprecate({ pkg: \"@onflow/fcl\", subject: \"Some item\", transition: \"https://github.com/onflow/flow-js-sdk\", message: \"Descriptive message\", level: LEVELS.warn, callback: () => {} })\n * @example\n * function someFunction() { ... }\n * const deprecatedFunction = log.deprecate({ pkg: \"@onflow/fcl\", subject: \"Some item\", transition: \"https://github.com/foo/bar/TRANSITIONS.md\", message: \"Descriptive message\", level: LEVELS.warn, callback: someFunction })\n * deprecatedFunction() // Calls someFunction and logs the deprecation notice\n */\nlog.deprecate = options => {\n  const {\n    pkg,\n    subject,\n    transition,\n    level = LEVELS.warn,\n    message = \"\",\n    callback = null\n  } = options;\n  const capitalizeFirstLetter = str => {\n    return str.charAt(0).toUpperCase() + str.slice(1);\n  };\n  const logMessage = () => log({\n    title: `${pkg ? pkg + \" \" : \"\"}Deprecation Notice`,\n    message: `\n      ${subject ? `${capitalizeFirstLetter(subject)} is deprecated and will cease to work in future releases${pkg ? \" of \" + pkg : \"\"}.` : \"\"}${message ? \"\\n\" + message : \"\"}${transition ? `\\nYou can learn more (including a guide on common transition paths) here: ${transition}` : \"\"}\n    `.trim(),\n    level\n  });\n  if (typeof callback === \"function\") {\n    return async function () {\n      await logMessage();\n      return await callback(...arguments);\n    };\n  }\n  return logMessage();\n};\n\nexport { LEVELS, log, setConfig };\n//# sourceMappingURL=util-logger.module.js.map\n", "/**\n * Asserts fact is true, otherwise throw an error with invariant message\n * @param fact\n * @param msg\n * @param rest\n */\nfunction invariant(fact, msg) {\n  if (!fact) {\n    const error = new Error(`INVARIANT ${msg}`);\n    error.stack = error.stack?.split(\"\\n\")?.filter(d => !/at invariant/.test(d))?.join(\"\\n\");\n    for (var _len = arguments.length, rest = new Array(_len > 2 ? _len - 2 : 0), _key = 2; _key < _len; _key++) {\n      rest[_key - 2] = arguments[_key];\n    }\n    console.error(\"\\n\\n---\\n\\n\", error, \"\\n\\n\", ...rest, \"\\n\\n---\\n\\n\");\n    throw error;\n  }\n}\n\nexport { invariant };\n//# sourceMappingURL=util-invariant.module.js.map\n", "// Unique ID creation requires a high quality random # generator. In the browser we therefore\n// require the crypto API and do not support built-in fallback to lower quality random number\n// generators (like Math.random()).\nlet getRandomValues;\nconst rnds8 = new Uint8Array(16);\nexport default function rng() {\n  // lazy load so that environments that need to polyfill have a chance to do so\n  if (!getRandomValues) {\n    // getRandomValues needs to be invoked in a context where \"this\" is a Crypto implementation.\n    getRandomValues = typeof crypto !== 'undefined' && crypto.getRandomValues && crypto.getRandomValues.bind(crypto);\n\n    if (!getRandomValues) {\n      throw new Error('crypto.getRandomValues() not supported. See https://github.com/uuidjs/uuid#getrandomvalues-not-supported');\n    }\n  }\n\n  return getRandomValues(rnds8);\n}", "import validate from './validate.js';\n/**\n * Convert array of 16 byte values to UUID string format of the form:\n * XXXXXXXX-XXXX-XXXX-XXXX-XXXXXXXXXXXX\n */\n\nconst byteToHex = [];\n\nfor (let i = 0; i < 256; ++i) {\n  byteToHex.push((i + 0x100).toString(16).slice(1));\n}\n\nexport function unsafeStringify(arr, offset = 0) {\n  // Note: Be careful editing this code!  It's been tuned for performance\n  // and works in ways you may not expect. See https://github.com/uuidjs/uuid/pull/434\n  return byteToHex[arr[offset + 0]] + byteToHex[arr[offset + 1]] + byteToHex[arr[offset + 2]] + byteToHex[arr[offset + 3]] + '-' + byteToHex[arr[offset + 4]] + byteToHex[arr[offset + 5]] + '-' + byteToHex[arr[offset + 6]] + byteToHex[arr[offset + 7]] + '-' + byteToHex[arr[offset + 8]] + byteToHex[arr[offset + 9]] + '-' + byteToHex[arr[offset + 10]] + byteToHex[arr[offset + 11]] + byteToHex[arr[offset + 12]] + byteToHex[arr[offset + 13]] + byteToHex[arr[offset + 14]] + byteToHex[arr[offset + 15]];\n}\n\nfunction stringify(arr, offset = 0) {\n  const uuid = unsafeStringify(arr, offset); // Consistency check for valid UUID.  If this throws, it's likely due to one\n  // of the following:\n  // - One or more input array values don't map to a hex octet (leading to\n  // \"undefined\" in the uuid)\n  // - Invalid input values for the RFC `version` or `variant` fields\n\n  if (!validate(uuid)) {\n    throw TypeError('Stringified UUID is invalid');\n  }\n\n  return uuid;\n}\n\nexport default stringify;", "import native from './native.js';\nimport rng from './rng.js';\nimport { unsafeStringify } from './stringify.js';\n\nfunction v4(options, buf, offset) {\n  if (native.randomUUID && !buf && !options) {\n    return native.randomUUID();\n  }\n\n  options = options || {};\n  const rnds = options.random || (options.rng || rng)(); // Per 4.4, set bits for version and `clock_seq_hi_and_reserved`\n\n  rnds[6] = rnds[6] & 0x0f | 0x40;\n  rnds[8] = rnds[8] & 0x3f | 0x80; // Copy bytes to buffer, if provided\n\n  if (buf) {\n    offset = offset || 0;\n\n    for (let i = 0; i < 16; ++i) {\n      buf[offset + i] = rnds[i];\n    }\n\n    return buf;\n  }\n\n  return unsafeStringify(rnds);\n}\n\nexport default v4;", "const mailbox = () => {\n  const queue = [];\n  let next;\n  return {\n    async deliver(msg) {\n      queue.push(msg);\n      if (next) {\n        next(queue.shift());\n        next = undefined;\n      }\n    },\n    receive() {\n      return new Promise(function innerReceive(resolve) {\n        const msg = queue.shift();\n        if (msg) return resolve(msg);\n        next = resolve;\n      });\n    }\n  };\n};\n\nlet promise;\nconst _queueMicrotask = cb => (promise || (promise = Promise.resolve())).then(cb).catch(err => setTimeout(() => {\n  throw err;\n}, 0));\nconst INIT = \"INIT\";\nconst SUBSCRIBE = \"SUBSCRIBE\";\nconst UNSUBSCRIBE = \"UNSUBSCRIBE\";\nconst UPDATED = \"UPDATED\";\nconst SNAPSHOT = \"SNAPSHOT\";\nconst EXIT = \"EXIT\";\nconst TERMINATE = \"TERMINATE\";\nconst root = typeof self === \"object\" && self.self === self && self || typeof global === \"object\" && global.global === global && global || typeof window === \"object\" && window.window === window && window || {\n  FCL_REGISTRY: null\n};\nroot.FCL_REGISTRY = root.FCL_REGISTRY == null ? {} : root.FCL_REGISTRY;\nconst FCL_REGISTRY = root.FCL_REGISTRY;\nlet pid = 0b0;\nconst DEFAULT_TIMEOUT = 5000;\nfunction send(addr, tag, data) {\n  let opts = arguments.length > 3 && arguments[3] !== undefined ? arguments[3] : {\n    expectReply: false\n  };\n  return new Promise((resolve, reject) => {\n    const expectReply = opts.expectReply || false;\n    const timeout = opts.timeout != null ? opts.timeout : DEFAULT_TIMEOUT;\n    if (expectReply && timeout) {\n      setTimeout(() => reject(new Error(`Timeout: ${timeout}ms passed without a response.`)), timeout);\n    }\n    const payload = {\n      to: addr,\n      from: opts.from,\n      tag,\n      data,\n      timeout,\n      reply: resolve,\n      reject\n    };\n    try {\n      if (FCL_REGISTRY[addr]) {\n        FCL_REGISTRY[addr].mailbox.deliver(payload);\n      }\n      if (!expectReply) {\n        resolve(true);\n      }\n    } catch (error) {\n      console.error(\"FCL.Actor -- Could Not Deliver Message\", payload, FCL_REGISTRY[addr], error);\n      reject(error);\n    }\n  });\n}\nconst kill = addr => {\n  delete FCL_REGISTRY[addr];\n};\nconst fromHandlers = handlers => async ctx => {\n  if (typeof handlers[INIT] === \"function\") await handlers[INIT](ctx);\n  __loop: while (1) {\n    const letter = await ctx.receive();\n    try {\n      if (letter.tag === EXIT) {\n        if (typeof handlers[TERMINATE] === \"function\") {\n          await handlers[TERMINATE](ctx, letter, letter.data || {});\n        }\n        break __loop;\n      }\n      await handlers[letter.tag]?.(ctx, letter, letter.data || {});\n    } catch (error) {\n      console.error(`${ctx.self()} Error`, letter, error);\n    } finally {\n      continue __loop;\n    }\n  }\n};\nconst parseAddr = addr => {\n  if (addr == null) {\n    while (FCL_REGISTRY[String(pid)]) {\n      pid++;\n    }\n    return String(pid);\n  }\n  return String(addr);\n};\nconst spawn = function (fnOrHandlers) {\n  let rawAddr = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : null;\n  const addr = parseAddr(rawAddr);\n  if (FCL_REGISTRY[addr] != null) return addr;\n  FCL_REGISTRY[addr] = {\n    addr,\n    mailbox: mailbox(),\n    subs: new Set(),\n    kvs: {},\n    error: null\n  };\n  const ctx = createCtx(addr);\n  let fn;\n  if (typeof fnOrHandlers === \"object\") fn = fromHandlers(fnOrHandlers);else fn = fnOrHandlers;\n  _queueMicrotask(async () => {\n    await fn(ctx);\n    kill(addr);\n  });\n  return addr;\n};\nconst createCtx = addr => ({\n  self: () => addr,\n  receive: () => FCL_REGISTRY[addr].mailbox.receive(),\n  send: function (to, tag, data) {\n    let opts = arguments.length > 3 && arguments[3] !== undefined ? arguments[3] : {};\n    if (to == null) return;\n    opts.from = addr;\n    return send(to, tag, data, opts);\n  },\n  sendSelf: function (tag, data) {\n    let opts = arguments.length > 2 && arguments[2] !== undefined ? arguments[2] : {};\n    if (FCL_REGISTRY[addr]) send(addr, tag, data, opts);\n  },\n  broadcast: function (tag, data) {\n    let opts = arguments.length > 2 && arguments[2] !== undefined ? arguments[2] : {};\n    opts.from = addr;\n    for (const to of FCL_REGISTRY[addr].subs) send(to, tag, data, opts);\n  },\n  subscribe: sub => sub != null && FCL_REGISTRY[addr].subs.add(sub),\n  unsubscribe: sub => sub != null && FCL_REGISTRY[addr].subs.delete(sub),\n  subscriberCount: () => FCL_REGISTRY[addr].subs.size,\n  hasSubs: () => !!FCL_REGISTRY[addr].subs.size,\n  put: (key, value) => {\n    if (key != null) FCL_REGISTRY[addr].kvs[key] = value;\n  },\n  get: function (key) {\n    let fallback = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : undefined;\n    const value = FCL_REGISTRY[addr].kvs[key];\n    return value == null ? fallback : value;\n  },\n  delete: key => {\n    delete FCL_REGISTRY[addr].kvs[key];\n  },\n  update: (key, fn) => {\n    if (key != null) FCL_REGISTRY[addr].kvs[key] = fn(FCL_REGISTRY[addr].kvs[key]);\n  },\n  keys: () => {\n    return Object.keys(FCL_REGISTRY[addr].kvs);\n  },\n  all: () => {\n    return FCL_REGISTRY[addr].kvs;\n  },\n  where: pattern => {\n    return Object.keys(FCL_REGISTRY[addr].kvs).reduce((acc, key) => {\n      return pattern.test(key) ? {\n        ...acc,\n        [key]: FCL_REGISTRY[addr].kvs[key]\n      } : acc;\n    }, {});\n  },\n  merge: function () {\n    let data = arguments.length > 0 && arguments[0] !== undefined ? arguments[0] : {};\n    Object.keys(data).forEach(key => FCL_REGISTRY[addr].kvs[key] = data[key]);\n  },\n  fatalError: error => {\n    FCL_REGISTRY[addr].error = error;\n    for (const to of FCL_REGISTRY[addr].subs) send(to, UPDATED);\n  }\n});\n\n// Returns an unsubscribe function\n// A SUBSCRIBE handler will need to be created to handle the subscription event\n//\n//  [SUBSCRIBE]: (ctx, letter) => {\n//    ctx.subscribe(letter.from)\n//    ctx.send(letter.from, UPDATED, ctx.all())\n//  }\n//\nfunction subscriber(address, spawnFn, callback) {\n  spawnFn(address);\n  const self = spawn(async ctx => {\n    ctx.send(address, SUBSCRIBE);\n    while (1) {\n      const letter = await ctx.receive();\n      const error = FCL_REGISTRY[address].error;\n      if (letter.tag === EXIT) {\n        ctx.send(address, UNSUBSCRIBE);\n        return;\n      }\n      if (error) {\n        callback(null, error);\n        ctx.send(address, UNSUBSCRIBE);\n        return;\n      }\n      callback(letter.data, null);\n    }\n  });\n  return () => send(self, EXIT);\n}\n\n// Returns a promise that returns a result\n// A SNAPSHOT handler will need to be created to handle the snapshot event\n//\n//  [SNAPSHOT]: (ctx, letter) => {\n//    letter.reply(ctx.all())\n//  }\n//\nfunction snapshoter(address, spawnFn) {\n  spawnFn(address);\n  return send(address, SNAPSHOT, null, {\n    expectReply: true,\n    timeout: 0\n  });\n}\n\nexport { EXIT, INIT, SNAPSHOT, SUBSCRIBE, TERMINATE, UNSUBSCRIBE, UPDATED, kill, send, snapshoter, spawn, subscriber };\n//# sourceMappingURL=actor.module.js.map\n", "import { spawn, SUBSCRIBE, UNSUBSCRIBE, send, subscriber } from '@onflow/util-actor';\nimport * as logger from '@onflow/util-logger';\nimport { invariant } from '@onflow/util-invariant';\n\nconst pipe = function () {\n  for (var _len = arguments.length, funcs = new Array(_len), _key = 0; _key < _len; _key++) {\n    funcs[_key] = arguments[_key];\n  }\n  return v => {\n    return funcs.reduce((res, func) => {\n      return func(res);\n    }, v);\n  };\n};\n\n/***\n * Merge multiple functions returning objects into one object.\n * @param funcs - Functions to merge\n * @return Merged object\n */\nconst mergePipe = function () {\n  for (var _len2 = arguments.length, funcs = new Array(_len2), _key2 = 0; _key2 < _len2; _key2++) {\n    funcs[_key2] = arguments[_key2];\n  }\n  return v => {\n    return funcs.reduce((res, func) => {\n      return {\n        ...res,\n        ...func(v)\n      };\n    }, {});\n  };\n};\n\n/**\n * @description Object check\n * @param value - Value to check\n * @returns Is object status\n */\nconst isObject = value => value && typeof value === \"object\" && !Array.isArray(value);\n\n/**\n * @description Deep merge multiple objects.\n * @param target - Target object\n * @param sources - Source objects\n * @returns Merged object\n */\nconst mergeDeep = function (target) {\n  for (var _len3 = arguments.length, sources = new Array(_len3 > 1 ? _len3 - 1 : 0), _key3 = 1; _key3 < _len3; _key3++) {\n    sources[_key3 - 1] = arguments[_key3];\n  }\n  if (!sources.length) return target;\n  const source = sources.shift();\n  if (isObject(target) && isObject(source)) {\n    for (const key in source) {\n      if (isObject(source[key])) {\n        if (!target[key]) Object.assign(target, {\n          [key]: {}\n        });\n        mergeDeep(target[key], source[key]);\n      } else {\n        Object.assign(target, {\n          [key]: source[key]\n        });\n      }\n    }\n  }\n  return mergeDeep(target, ...sources);\n};\n\n/**\n * @description Deep merge multiple Flow JSON.\n * @param value - Flow JSON or array of Flow JSONs\n * @returns Merged Flow JSON\n */\nconst mergeFlowJSONs = value => Array.isArray(value) ? mergeDeep({}, ...value) : value;\n\n/**\n * @description Filter out contracts section of flow.json.\n * @param obj - Flow JSON\n * @returns Contracts section of Flow JSON\n */\nconst filterContracts = obj => obj.contracts ? obj.contracts : {};\n\n/**\n * @description Filter out dependencies section of flow.json.\n * @param obj - Flow JSON\n * @returns Dependencies section of Flow JSON\n */\nconst filterDependencies = obj => obj.dependencies ? obj.dependencies : {};\n\n/**\n * @description Gathers contract addresses by network\n * @param network - Network to gather addresses for\n * @returns Contract names by addresses mapping e.g { \"HelloWorld\": \"0x123\" }\n */\nconst mapContractAliasesToNetworkAddress = network => contracts => {\n  return Object.entries(contracts).reduce((c, _ref) => {\n    let [key, value] = _ref;\n    const networkContractAlias = value?.aliases?.[network];\n    if (networkContractAlias) {\n      c[key] = networkContractAlias;\n    }\n    return c;\n  }, {});\n};\n\n/**\n * @description Gathers dependency addresses by network\n * @param network - Network to gather addresses for\n * @returns Dependency names by addresses mapping e.g { \"HelloWorld\": \"0x123\" }\n */\nconst mapDependencyAliasesToNetworkAddress = network => dependencies => {\n  return Object.entries(dependencies).reduce((c, _ref2) => {\n    let [key, value] = _ref2;\n    const networkDependencyAlias = value?.aliases?.[network];\n    if (networkDependencyAlias) {\n      c[key] = networkDependencyAlias;\n    }\n    return c;\n  }, {});\n};\nconst mapDeploymentsToNetworkAddress = network => _ref3 => {\n  let {\n    deployments = {},\n    accounts = {}\n  } = _ref3;\n  const networkDeployment = deployments?.[network];\n  if (!networkDeployment) return {};\n  return Object.entries(networkDeployment).reduce((c, _ref4) => {\n    let [key, value] = _ref4;\n    // Resolve account address\n    const accountAddress = accounts[key]?.address;\n    if (!accountAddress) return c;\n\n    // Create an object assigning the address to the contract name.\n    return value.reduce((c, contract) => {\n      return {\n        ...c,\n        [contract]: accountAddress\n      };\n    }, {});\n  }, {});\n};\n\n/**\n * @description Take in flow.json files and return contract to address mapping by network\n * @param jsons - Flow JSON or array of Flow JSONs\n * @param network - Network to gather addresses for\n * @returns Contract names by addresses mapping e.g { \"HelloWorld\": \"0x123\" }\n */\nconst getContracts = (jsons, network) => {\n  return pipe(mergeFlowJSONs, mergePipe(mapDeploymentsToNetworkAddress(network), pipe(filterContracts, mapContractAliasesToNetworkAddress(network)), pipe(filterDependencies, mapDependencyAliasesToNetworkAddress(network))))(jsons);\n};\n\n/**\n * @description Checks if string is hexidecimal\n * @param str - String to check\n * @returns Is hexidecimal status\n */\nconst isHexidecimal = str => {\n  // Check that it is a string\n  if (typeof str !== \"string\") return false;\n  return /^[0-9A-Fa-f]+$/.test(str);\n};\n\n/**\n * @description Checks flow.json file for private keys\n * @param flowJSON - Flow JSON\n * @returns Has private keys status\n */\nconst hasPrivateKeys = flowJSON => {\n  return Object.entries(flowJSON?.accounts ?? []).reduce((hasPrivateKey, _ref5) => {\n    let [, value] = _ref5;\n    if (hasPrivateKey) return true;\n    return value && Object.prototype.hasOwnProperty.call(value, \"key\") && isHexidecimal(value?.key);\n  }, false);\n};\n\n/**\n * @description Take in flow.json or array of flow.json files and checks for private keys\n * @param value - Flow JSON or array of Flow JSONs\n * @returns Has private keys status\n */\nconst anyHasPrivateKeys = value => {\n  if (Array.isArray(value)) return value.some(hasPrivateKeys);\n  return hasPrivateKeys(value);\n};\n\n/**\n * @description Format network to always be 'emulator', 'testnet', 'previewnet' or 'mainnet'\n * @param network - Network to format\n * @returns Formatted network name (either 'emulator', 'testnet', 'previewnet' or 'mainnet')\n */\nconst cleanNetwork = network => {\n  const cleanedNetwork = network?.toLowerCase() === \"local\" ? \"emulator\" : network?.toLowerCase();\n  if (cleanedNetwork === \"emulator\" || cleanedNetwork === \"testnet\" || cleanedNetwork === \"mainnet\" || cleanedNetwork === \"previewnet\") return cleanedNetwork;\n  throw new Error(`Invalid network \"${network}\". Must be one of \"emulator\", \"local\", \"testnet\", or \"mainnet\"`);\n};\n\n// Inject config into logger to break circular dependency\nlogger.setConfig(config);\nconst NAME = \"config\";\nconst PUT = \"PUT_CONFIG\";\nconst GET = \"GET_CONFIG\";\nconst GET_ALL = \"GET_ALL_CONFIG\";\nconst UPDATE = \"UPDATE_CONFIG\";\nconst DELETE = \"DELETE_CONFIG\";\nconst CLEAR = \"CLEAR_CONFIG\";\nconst WHERE = \"WHERE_CONFIG\";\nconst UPDATED = \"CONFIG/UPDATED\";\nconst identity = v => v;\nconst HANDLERS = {\n  [PUT]: (ctx, _letter, _ref) => {\n    let {\n      key,\n      value\n    } = _ref;\n    if (key == null) throw new Error(\"Missing 'key' for config/put.\");\n    ctx.put(key, value);\n    ctx.broadcast(UPDATED, {\n      ...ctx.all()\n    });\n  },\n  [GET]: (ctx, letter, _ref2) => {\n    let {\n      key,\n      fallback\n    } = _ref2;\n    if (key == null) throw new Error(\"Missing 'key' for config/get\");\n    letter.reply(ctx.get(key, fallback));\n  },\n  [GET_ALL]: (ctx, letter) => {\n    letter.reply({\n      ...ctx.all()\n    });\n  },\n  [UPDATE]: (ctx, letter, _ref3) => {\n    let {\n      key,\n      fn\n    } = _ref3;\n    if (key == null) throw new Error(\"Missing 'key' for config/update\");\n    ctx.update(key, fn || identity);\n    ctx.broadcast(UPDATED, {\n      ...ctx.all()\n    });\n  },\n  [DELETE]: (ctx, letter, _ref4) => {\n    let {\n      key\n    } = _ref4;\n    if (key == null) throw new Error(\"Missing 'key' for config/delete\");\n    ctx.delete(key);\n    ctx.broadcast(UPDATED, {\n      ...ctx.all()\n    });\n  },\n  [CLEAR]: ctx => {\n    const keys = Object.keys(ctx.all());\n    for (const key of keys) ctx.delete(key);\n    ctx.broadcast(UPDATED, {\n      ...ctx.all()\n    });\n  },\n  [WHERE]: (ctx, letter, _ref5) => {\n    let {\n      pattern\n    } = _ref5;\n    if (pattern == null) throw new Error(\"Missing 'pattern' for config/where\");\n    letter.reply(ctx.where(pattern));\n  },\n  [SUBSCRIBE]: (ctx, letter) => {\n    ctx.subscribe(letter.from);\n    ctx.send(letter.from, UPDATED, {\n      ...ctx.all()\n    });\n  },\n  [UNSUBSCRIBE]: (ctx, letter) => {\n    ctx.unsubscribe(letter.from);\n  }\n};\nspawn(HANDLERS, NAME);\n\n/**\n * @description Adds a key-value pair to the config\n * @param key - The key to add\n * @param value - The value to add\n * @returns The config object\n */\nfunction put(key, value) {\n  send(NAME, PUT, {\n    key,\n    value\n  });\n  return config();\n}\n\n/**\n * @description Gets a key-value pair with a fallback from the config\n * @param key - The key to add\n * @param fallback - The fallback value to return if key is not found\n * @returns The value found at key or fallback\n */\nfunction get(key, fallback) {\n  return send(NAME, GET, {\n    key,\n    fallback\n  }, {\n    expectReply: true,\n    timeout: 10\n  });\n}\n\n/**\n * @description Returns the first non null config value or the fallback\n * @param wants - The keys to search for\n * @param fallback - The fallback value to return if key is not found\n * @returns The value found at key or fallback\n */\nasync function first() {\n  let wants = arguments.length > 0 && arguments[0] !== undefined ? arguments[0] : [];\n  let fallback = arguments.length > 1 ? arguments[1] : undefined;\n  if (!wants.length) return fallback;\n  const [head, ...rest] = wants;\n  const ret = await get(head);\n  if (ret == null) return first(rest, fallback);\n  return ret;\n}\n\n/**\n * @description Returns the current config\n * @returns The config object\n */\nfunction all() {\n  return send(NAME, GET_ALL, null, {\n    expectReply: true,\n    timeout: 10\n  });\n}\n\n/**\n * @description Updates a key-value pair in the config\n * @param key - The key to update\n * @param fn - The function to update the value with\n * @returns The config object\n */\nfunction update(key) {\n  let fn = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : identity;\n  send(NAME, UPDATE, {\n    key,\n    fn\n  });\n  return config();\n}\n\n/**\n * @description Deletes a key-value pair from the config\n * @param key - The key to delete\n * @returns The config object\n */\nfunction _delete(key) {\n  send(NAME, DELETE, {\n    key\n  });\n  return config();\n}\n\n/**\n * @description Returns a subset of the config based on a pattern\n * @param pattern - The pattern to match keys against\n * @returns The subset of the config\n */\nfunction where(pattern) {\n  return send(NAME, WHERE, {\n    pattern\n  }, {\n    expectReply: true,\n    timeout: 10\n  });\n}\n\n/**\n * @description Subscribes to config updates\n * @param callback - The callback to call when config is updated\n * @returns The unsubscribe function\n */\nfunction subscribe(callback) {\n  return subscriber(NAME, () => spawn(HANDLERS, NAME), callback);\n}\n\n/**\n * @description Clears the config\n */\nasync function clearConfig() {\n  await send(NAME, CLEAR);\n}\n\n/**\n * @description Resets the config to a previous state\n * @param oldConfig - The previous config state\n * @returns The config object\n */\nasync function resetConfig(oldConfig) {\n  return clearConfig().then(() => config(oldConfig));\n}\n\n/**\n * @description Takes in flow.json or array of flow.json files and creates contract placeholders\n * @param data - The data to load\n * @param data.flowJSON - The flow.json or array of flow.json files\n * @param options - override flag\n * @param options.ignoreConflicts - ignore conflicts and override config\n */\nasync function load(data) {\n  let {\n    ignoreConflicts = false\n  } = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : {};\n  const network = await get(\"flow.network\");\n  const cleanedNetwork = cleanNetwork(network);\n  const {\n    flowJSON\n  } = data;\n  invariant(Boolean(flowJSON), \"config.load -- 'flowJSON' must be defined\");\n  invariant(!!cleanedNetwork, `Flow Network Required -- In order for FCL to load your contracts please define \"flow.network\" to \"emulator\", \"local\", \"testnet\", or \"mainnet\" in your config. See more here: https://developers.flow.com/tools/fcl-js/reference/configure-fcl`);\n  if (anyHasPrivateKeys(flowJSON)) {\n    const isEmulator = cleanedNetwork === \"emulator\";\n    logger.log({\n      title: \"Private Keys Detected\",\n      message: `Private keys should be stored in a separate flow.json file for security. See more here: https://developers.flow.com/tools/flow-cli/security`,\n      level: isEmulator ? logger.LEVELS.warn : logger.LEVELS.error\n    });\n    invariant(isEmulator, `Private keys should be stored in a separate flow.json file for security. See more here: https://developers.flow.com/tools/flow-cli/security`);\n  }\n  for (const [key, value] of Object.entries(getContracts(flowJSON, cleanedNetwork))) {\n    const contractConfigKey = `0x${key}`;\n    const existingContractConfigKey = await get(contractConfigKey);\n    if (existingContractConfigKey && existingContractConfigKey !== value && !ignoreConflicts) {\n      logger.log({\n        title: \"Contract Placeholder Conflict Detected\",\n        message: `A generated contract placeholder from config.load conflicts with a placeholder you've set manually in config have the same name.`,\n        level: logger.LEVELS.warn\n      });\n    } else {\n      put(contractConfigKey, value);\n    }\n    const systemContractConfigKey = `system.contracts.${key}`;\n    const systemExistingContractConfigKeyValue = await get(systemContractConfigKey);\n    if (systemExistingContractConfigKeyValue && systemExistingContractConfigKeyValue !== value && !ignoreConflicts) {\n      logger.log({\n        title: \"Contract Placeholder Conflict Detected\",\n        message: `A generated contract placeholder from config.load conflicts with a placeholder you've set manually in config have the same name.`,\n        level: logger.LEVELS.warn\n      });\n    } else {\n      put(systemContractConfigKey, value);\n    }\n  }\n}\n\n/**\n * @description Sets the config\n * @param values - The values to set\n * @returns The config object\n */\nfunction config(values) {\n  if (values != null && typeof values === \"object\") {\n    Object.keys(values).map(d => put(d, values[d]));\n  }\n  return {\n    put,\n    get,\n    all,\n    first,\n    update,\n    delete: _delete,\n    where,\n    subscribe,\n    overload,\n    load\n  };\n}\nconfig.put = put;\nconfig.get = get;\nconfig.all = all;\nconfig.first = first;\nconfig.update = update;\nconfig.delete = _delete;\nconfig.where = where;\nconfig.subscribe = subscribe;\nconfig.overload = overload;\nconfig.load = load;\n\n/**\n * @description Temporarily overloads the config with the given values and calls the callback\n * @param values - The values to overload the config with\n * @param callback - The callback to call with the overloaded config\n * @returns The result of the callback\n */\nasync function overload(values, callback) {\n  const oldConfig = await all();\n  try {\n    config(values);\n    const result = await callback(await all());\n    return result;\n  } finally {\n    await resetConfig(oldConfig);\n  }\n}\n\nexport { clearConfig, config };\n//# sourceMappingURL=config.module.js.map\n", "import { <PERSON><PERSON><PERSON> } from 'buffer';\nexport { <PERSON>uff<PERSON> } from 'buffer';\n\n/**\n * Built on top of rlp library, removing the BN dependency for the flow.\n * Package : https://github.com/ethereumjs/rlp\n * RLP License : https://github.com/ethereumjs/rlp/blob/master/LICENSE\n *\n * ethereumjs/rlp is licensed under the\n * Mozilla Public License 2.0\n * Permissions of this weak copyleft license are conditioned on making available source code of licensed files and modifications of those files under the same license (or in certain cases, one of the GNU licenses). Copyright and license notices must be preserved. Contributors provide an express grant of patent rights. However, a larger work using the licensed work may be distributed under different terms and without source code for files added in the larger work.\n */\n\n/**\n * @param input - will be converted to buffer\n * @returns returns buffer of encoded data\n */\nfunction encode(input) {\n  if (Array.isArray(input)) {\n    const output = [];\n    for (let i = 0; i < input.length; i++) {\n      output.push(encode(input[i]));\n    }\n    const buf = Buffer.concat(output);\n    return Buffer.concat([encodeLength(buf.length, 192), buf]);\n  } else {\n    const inputBuf = toBuffer(input);\n    return inputBuf.length === 1 && inputBuf[0] < 128 ? inputBuf : Buffer.concat([encodeLength(inputBuf.length, 128), inputBuf]);\n  }\n}\n\n/**\n * Parse integers. Check if there is no leading zeros\n * @param v The value to parse\n * @param base The base to parse the integer into\n */\nfunction safeParseInt(v, base) {\n  if (v.slice(0, 2) === \"00\") {\n    throw new Error(\"invalid RLP: extra zeros\");\n  }\n  return parseInt(v, base);\n}\nfunction encodeLength(len, offset) {\n  if (len < 56) {\n    return Buffer.from([len + offset]);\n  } else {\n    const hexLength = intToHex(len);\n    const lLength = hexLength.length / 2;\n    const firstByte = intToHex(offset + 55 + lLength);\n    return Buffer.from(firstByte + hexLength, \"hex\");\n  }\n}\n\n/**\n * Built on top of rlp library, removing the BN dependency for the flow.\n * Package : https://github.com/ethereumjs/rlp\n * RLP License : https://github.com/ethereumjs/rlp/blob/master/LICENSE\n *\n * ethereumjs/rlp is licensed under the\n * Mozilla Public License 2.0\n * Permissions of this weak copyleft license are conditioned on making available source code of licensed files and modifications of those files under the same license (or in certain cases, one of the GNU licenses). Copyright and license notices must be preserved. Contributors provide an express grant of patent rights. However, a larger work using the licensed work may be distributed under different terms and without source code for files added in the larger work.\n */\n\n/**\n * @param input - will be converted to buffer\n * @param stream Is the input a stream (false by default)\n * @returns returns buffer of encoded data\n */\nfunction decode(input, stream) {\n  if (stream === void 0) {\n    stream = false;\n  }\n  if (!input || input.length === 0) {\n    return Buffer.from([]);\n  }\n  const inputBuffer = toBuffer(input);\n  const decoded = _decode(inputBuffer);\n  if (stream) {\n    return decoded;\n  }\n  if (decoded.remainder.length !== 0) {\n    throw new Error(\"invalid remainder\");\n  }\n  return decoded.data;\n}\n\n/**\n * Get the length of the RLP input\n * @param input\n * @returns The length of the input or an empty Buffer if no input\n */\nfunction getLength(input) {\n  const inputBuffer = toBuffer(input);\n  if (inputBuffer.length === 0) {\n    return 0;\n  }\n  const firstByte = inputBuffer[0];\n  if (firstByte <= 0x7f) {\n    return inputBuffer.length;\n  } else if (firstByte <= 0xb7) {\n    return firstByte - 0x7f;\n  } else if (firstByte <= 0xbf) {\n    return firstByte - 0xb6;\n  } else if (firstByte <= 0xf7) {\n    // a list between  0-55 bytes long\n    return firstByte - 0xbf;\n  } else {\n    // a list  over 55 bytes long\n    const llength = firstByte - 0xf6;\n    const length = safeParseInt(inputBuffer.slice(1, llength).toString(\"hex\"), 16);\n    return llength + length;\n  }\n}\n\n/** Decode an input with RLP */\nfunction _decode(input) {\n  let length, llength, data, innerRemainder, d;\n  const decoded = [];\n  const firstByte = input[0];\n  if (firstByte <= 0x7f) {\n    // a single byte whose value is in the [0x00, 0x7f] range, that byte is its own RLP encoding.\n    return {\n      data: input.slice(0, 1),\n      remainder: input.slice(1)\n    };\n  } else if (firstByte <= 0xb7) {\n    // string is 0-55 bytes long. A single byte with value 0x80 plus the length of the string followed by the string\n    // The range of the first byte is [0x80, 0xb7]\n    length = firstByte - 0x7f;\n    // set 0x80 null to 0\n    if (firstByte === 0x80) {\n      data = Buffer.from([]);\n    } else {\n      data = input.slice(1, length);\n    }\n    if (length === 2 && data[0] < 0x80) {\n      throw new Error(\"invalid rlp encoding: byte must be less 0x80\");\n    }\n    return {\n      data: data,\n      remainder: input.slice(length)\n    };\n  } else if (firstByte <= 0xbf) {\n    llength = firstByte - 0xb6;\n    length = safeParseInt(input.slice(1, llength).toString(\"hex\"), 16);\n    data = input.slice(llength, length + llength);\n    if (data.length < length) {\n      throw new Error(\"invalid RLP\");\n    }\n    return {\n      data: data,\n      remainder: input.slice(length + llength)\n    };\n  } else if (firstByte <= 0xf7) {\n    // a list between  0-55 bytes long\n    length = firstByte - 0xbf;\n    innerRemainder = input.slice(1, length);\n    while (innerRemainder.length) {\n      d = _decode(innerRemainder);\n      decoded.push(d.data);\n      innerRemainder = d.remainder;\n    }\n    return {\n      data: decoded,\n      remainder: input.slice(length)\n    };\n  } else {\n    // a list  over 55 bytes long\n    llength = firstByte - 0xf6;\n    length = safeParseInt(input.slice(1, llength).toString(\"hex\"), 16);\n    const totalLength = llength + length;\n    if (totalLength > input.length) {\n      throw new Error(\"invalid rlp: total length is larger than the data\");\n    }\n    innerRemainder = input.slice(llength, totalLength);\n    if (innerRemainder.length === 0) {\n      throw new Error(\"invalid rlp, List has a invalid length\");\n    }\n    while (innerRemainder.length) {\n      d = _decode(innerRemainder);\n      decoded.push(d.data);\n      innerRemainder = d.remainder;\n    }\n    return {\n      data: decoded,\n      remainder: input.slice(totalLength)\n    };\n  }\n}\n/** Check if a string is prefixed by 0x */\nfunction isHexPrefixed(str) {\n  return str.slice(0, 2) === \"0x\";\n}\n/** Removes 0x from a given String */\nfunction stripHexPrefix(str) {\n  if (typeof str !== \"string\") {\n    return str;\n  }\n  return isHexPrefixed(str) ? str.slice(2) : str;\n}\n/** Transform an integer into its hexadecimal value */\nfunction intToHex(integer) {\n  if (integer < 0) {\n    throw new Error(\"Invalid integer as argument, must be unsigned!\");\n  }\n  const hex = integer.toString(16);\n  return hex.length % 2 ? \"0\" + hex : hex;\n}\n/** Pad a string to be even */\nfunction padToEven(a) {\n  return a.length % 2 ? \"0\" + a : a;\n}\n/** Transform an integer into a Buffer */\nfunction intToBuffer(integer) {\n  const hex = intToHex(integer);\n  return Buffer.from(hex, \"hex\");\n}\n\n/** Transform anything into a Buffer */\nfunction toBuffer(v) {\n  if (!Buffer.isBuffer(v)) {\n    if (typeof v === \"string\") {\n      if (isHexPrefixed(v)) {\n        return Buffer.from(padToEven(stripHexPrefix(v)), \"hex\");\n      } else {\n        return Buffer.from(v);\n      }\n    } else if (typeof v === \"number\") {\n      if (!v) {\n        return Buffer.from([]);\n      } else {\n        return intToBuffer(v);\n      }\n    } else if (v === null || v === undefined) {\n      return Buffer.from([]);\n    } else if (v instanceof Uint8Array) {\n      return Buffer.from(v);\n    } else {\n      throw new Error(\"invalid type\");\n    }\n  }\n  return v;\n}\n\nexport { decode, encode, getLength, toBuffer };\n//# sourceMappingURL=rlp.module.js.map\n", "/**\n * @description Removes 0x from address if present\n * @param address - Flow address\n * @returns Flow address without 0x prefix\n */\nfunction sansPrefix(address) {\n  if (address == null) return null;\n  return address.replace(/^0x/, \"\").replace(/^Fx/, \"\");\n}\n/**\n * @description Adds 0x to address if not already present\n * @param address - Flow address\n * @returns Flow address with 0x prefix\n */\nfunction withPrefix(address) {\n  if (address == null) return null;\n  return \"0x\" + sansPrefix(address);\n}\n\n/**\n * @description Adds 0x to address if not already present\n * @param address - Flow address\n * @returns Flow address with 0x prefix\n */\nfunction display(address) {\n  return withPrefix(address);\n}\n\nexport { display, sansPrefix, withPrefix };\n//# sourceMappingURL=util-address.module.js.map\n", "import { invariant } from '@onflow/util-invariant';\nimport '@onflow/rlp';\nimport * as logger from '@onflow/util-logger';\nimport fetchTransport from 'cross-fetch';\nimport { sansPrefix } from '@onflow/util-address';\nimport { EventEmitter } from 'events';\nimport _WebSocket from 'isomorphic-ws';\nimport { SubscriptionTopic } from '@onflow/typedefs';\nimport { Buffer as Buffer$1 } from 'buffer';\n\nfunction safeParseJSON(data) {\n  try {\n    return JSON.parse(data);\n  } catch {\n    return null;\n  }\n}\n\n/**\n * This file was taken from the Axios project and modified to work with TypeScript/ES6 modules.\n *\n * The original file can be found on GitHub:\n * https://github.com/axios/axios/blob/fe7d09bb08fa1c0e414956b7fc760c80459b0a43/lib/helpers/combineURLs.js\n *\n * The original license is included below:\n *\n * Copyright (c) 2014 <PERSON>\n *\n * Permission is hereby granted, free of charge, to any person obtaining a copy\n * of this software and associated documentation files (the \"Software\"), to deal\n * in the Software without restriction, including without limitation the rights\n * to use, copy, modify, merge, publish, distribute, sublicense, and/or sell\n * copies of the Software, and to permit persons to whom the Software is\n * furnished to do so, subject to the following conditions:\n *\n * The above copyright notice and this permission notice shall be included in\n * all copies or substantial portions of the Software.\n *\n * THE SOFTWARE IS PROVIDED \"AS IS\", WITHOUT WARRANTY OF ANY KIND, EXPRESS OR\n * IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF MERCHANTABILITY,\n * FITNESS FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT. IN NO EVENT SHALL THE\n * AUTHORS OR COPYRIGHT HOLDERS BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER\n * LIABILITY, WHETHER IN AN ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING FROM,\n * OUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN\n * THE SOFTWARE.\n */\n\n/**\n * Creates a new URL by combining the specified URLs\n *\n * @param baseURL The base URL\n * @param relativeURL The relative URL\n * @returns The combined URL\n */\nfunction combineURLs(baseURL, relativeURL) {\n  return relativeURL ? baseURL.replace(/\\/+$/, \"\") + \"/\" + relativeURL.replace(/^\\/+/, \"\") : baseURL;\n}\n\nconst AbortController = globalThis.AbortController || require(\"abort-controller\");\nclass HTTPRequestError extends Error {\n  constructor(_ref) {\n    let {\n      error,\n      hostname,\n      path,\n      method,\n      requestBody,\n      responseBody,\n      responseStatusText,\n      statusCode\n    } = _ref;\n    const msg = `\n      HTTP Request Error: An error occurred when interacting with the Access API.\n      ${error ? `error=${error}` : \"\"}\n      ${hostname ? `hostname=${hostname}` : \"\"}\n      ${path ? `path=${path}` : \"\"}\n      ${method ? `method=${method}` : \"\"}\n      ${requestBody ? `requestBody=${requestBody}` : \"\"}\n      ${responseBody ? `responseBody=${responseBody}` : \"\"}\n      ${responseStatusText ? `responseStatusText=${responseStatusText}` : \"\"}\n      ${statusCode ? `statusCode=${statusCode}` : \"\"}\n    `;\n    super(msg);\n    this.name = \"HTTP Request Error\";\n    this.statusCode = statusCode;\n    this.errorMessage = error;\n  }\n}\n\n/**\n * Creates an HTTP Request to be sent to a REST Access API via Fetch API.\n *\n * @param {object} options - Options for the HTTP Request\n * @param {String} options.hostname - Access API Hostname\n * @param {String} options.path - Path to the resource on the Access API\n * @param {String} options.method - HTTP Method\n * @param {object} options.body - HTTP Request Body\n * @param {object} [options.headers] - HTTP Request Headers\n * @param {boolean} [options.enableRequestLogging=true] - Enable/Disable request logging\n * @param {number} [options.retryLimit=5] - Number of times to retry request\n * @param {number} [options.retryIntervalMs=1000] - Time in milliseconds to wait before retrying request\n * @param {number} [options.timeoutLimit=30000] - Time in milliseconds to wait before timing out request\n *\n * @returns JSON object response from Access API.\n */\nasync function httpRequest(_ref2) {\n  let {\n    hostname,\n    path,\n    method,\n    body,\n    headers,\n    retryLimit = 5,\n    retryIntervalMs = 1000,\n    timeoutLimit = 30000,\n    enableRequestLogging = true\n  } = _ref2;\n  const bodyJSON = body ? JSON.stringify(body) : null;\n  function makeRequest() {\n    const controller = new AbortController();\n    const fetchTimeout = setTimeout(() => {\n      controller.abort();\n    }, timeoutLimit);\n    return fetchTransport(combineURLs(hostname, path).toString(), {\n      method: method,\n      body: bodyJSON,\n      headers,\n      signal: controller.signal\n    }).then(async res => {\n      if (res.ok) {\n        return res.json();\n      }\n      const responseText = await res.text().catch(() => null);\n      const response = safeParseJSON(responseText);\n      throw new HTTPRequestError({\n        error: response?.message,\n        hostname,\n        path,\n        method,\n        requestBody: bodyJSON,\n        responseBody: responseText,\n        responseStatusText: res.statusText,\n        statusCode: res.status\n      });\n    }).catch(async e => {\n      if (e instanceof HTTPRequestError) {\n        throw e;\n      }\n      if (e.name === \"AbortError\") {\n        throw e;\n      }\n\n      // Show AN error for all network errors\n      if (enableRequestLogging) {\n        await logger.log({\n          title: \"Access Node Error\",\n          message: `The provided access node ${hostname} does not appear to be a valid REST/HTTP access node.\n  Please verify that you are not unintentionally using a GRPC access node.\n  See more here: https://docs.onflow.org/fcl/reference/sdk-guidelines/#connect`,\n          level: logger.LEVELS.error\n        });\n      }\n      throw new HTTPRequestError({\n        error: e?.message,\n        hostname,\n        path,\n        method,\n        requestBody: bodyJSON\n      });\n    }).finally(() => {\n      clearTimeout(fetchTimeout);\n    });\n  }\n  async function requestLoop() {\n    let retryAttempt = arguments.length > 0 && arguments[0] !== undefined ? arguments[0] : 0;\n    try {\n      const resp = await makeRequest();\n      return resp;\n    } catch (error) {\n      const retryStatusCodes = [408, 429, 500, 502, 503, 504];\n      if (error.name === \"AbortError\" || retryStatusCodes.includes(error.statusCode)) {\n        return await new Promise((resolve, reject) => {\n          if (retryAttempt < retryLimit) {\n            if (enableRequestLogging) {\n              console.warn(`Access node unavailable, retrying in ${retryIntervalMs} ms...`);\n            }\n            setTimeout(() => {\n              resolve(requestLoop(retryAttempt + 1));\n            }, retryIntervalMs);\n          } else {\n            reject(error);\n          }\n        });\n      } else {\n        throw error;\n      }\n    }\n  }\n\n  // Keep retrying request until server available or max attempts exceeded\n  return await requestLoop();\n}\n\nasync function sendExecuteScriptAtBlockIDRequest(ix, context, opts) {\n  const httpRequest$1 = opts.httpRequest || httpRequest;\n  const res = await httpRequest$1({\n    hostname: opts.node,\n    path: `/v1/scripts?block_id=${ix.block.id}`,\n    method: \"POST\",\n    body: {\n      script: context.Buffer.from(ix.message.cadence).toString(\"base64\"),\n      arguments: ix.message.arguments.map(arg => context.Buffer.from(JSON.stringify(ix.arguments[arg].asArgument)).toString(\"base64\"))\n    }\n  });\n  return constructResponse$5(ix, context, res);\n}\nasync function sendExecuteScriptAtBlockHeightRequest(ix, context, opts) {\n  const httpRequest$1 = opts.httpRequest || httpRequest;\n  const res = await httpRequest$1({\n    hostname: opts.node,\n    path: `/v1/scripts?block_height=${ix.block.height}`,\n    method: \"POST\",\n    body: {\n      script: context.Buffer.from(ix.message.cadence).toString(\"base64\"),\n      arguments: ix.message.arguments.map(arg => context.Buffer.from(JSON.stringify(ix.arguments[arg].asArgument)).toString(\"base64\"))\n    }\n  });\n  return constructResponse$5(ix, context, res);\n}\nasync function sendExecuteScriptAtLatestBlockRequest(ix, context, opts) {\n  const httpRequest$1 = opts.httpRequest || httpRequest;\n  const res = await httpRequest$1({\n    hostname: opts.node,\n    path: `/v1/scripts?block_height=${ix.block.isSealed ? \"sealed\" : \"final\"}`,\n    method: \"POST\",\n    body: {\n      script: context.Buffer.from(ix.message.cadence).toString(\"base64\"),\n      arguments: ix.message.arguments.map(arg => context.Buffer.from(JSON.stringify(ix.arguments[arg].asArgument)).toString(\"base64\"))\n    }\n  });\n  return constructResponse$5(ix, context, res);\n}\nfunction constructResponse$5(ix, context, res) {\n  let ret = context.response();\n  ret.tag = ix.tag;\n  ret.encodedData = JSON.parse(context.Buffer.from(res, \"base64\").toString());\n  return ret;\n}\nasync function sendExecuteScript(ix) {\n  let context = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : {};\n  let opts = arguments.length > 2 && arguments[2] !== undefined ? arguments[2] : {};\n  invariant(opts.node, `SDK Send Execute Script Error: opts.node must be defined.`);\n  invariant(context.response, `SDK Send Execute Script Error: context.response must be defined.`);\n  invariant(context.Buffer, `SDK Send Execute Script Error: context.Buffer must be defined.`);\n  ix = await ix;\n  if (ix.block.id) {\n    return await sendExecuteScriptAtBlockIDRequest(ix, context, opts);\n  } else if (ix.block.height) {\n    return await sendExecuteScriptAtBlockHeightRequest(ix, context, opts);\n  } else {\n    return await sendExecuteScriptAtLatestBlockRequest(ix, context, opts);\n  }\n}\n\nconst HashAlgorithmIDs = {\n  SHA2_256: 1,\n  SHA2_384: 2,\n  SHA3_256: 3,\n  SHA3_384: 4,\n  KMAC128_BLS_BLS12_381: 5\n};\nconst SignatureAlgorithmIDs = {\n  ECDSA_P256: 1,\n  ECDSA_secp256k1: 2,\n  BLS_BLS12_381: 3\n};\nasync function sendGetAccountAtBlockHeightRequest(ix, context, opts) {\n  const httpRequest$1 = opts.httpRequest || httpRequest;\n  const res = await httpRequest$1({\n    hostname: opts.node,\n    path: `/v1/accounts/${ix.account.addr}?block_height=${ix.block.height}&expand=contracts,keys`,\n    method: \"GET\",\n    body: null\n  });\n  return constructResponse$4(ix, context, res);\n}\nasync function sendGetAccountAtLatestBlockRequest(ix, context, opts) {\n  const httpRequest$1 = opts.httpRequest || httpRequest;\n  const res = await httpRequest$1({\n    hostname: opts.node,\n    path: `/v1/accounts/${ix.account.addr}?block_height=${ix.block.isSealed ? \"sealed\" : \"final\"}&expand=contracts,keys`,\n    method: \"GET\",\n    body: null\n  });\n  return constructResponse$4(ix, context, res);\n}\nfunction constructResponse$4(ix, context, res) {\n  let ret = context.response();\n  ret.tag = ix.tag;\n  const unwrapContracts = contracts => {\n    const c = {};\n    if (!contracts) return c;\n    for (let key of Object.keys(contracts)) {\n      c[key] = context.Buffer.from(contracts[key], \"base64\").toString();\n    }\n    return c;\n  };\n  ret.account = {\n    address: res.address,\n    balance: Number(res.balance),\n    code: \"\",\n    contracts: unwrapContracts(res.contracts),\n    keys: res.keys?.map(key => ({\n      index: Number(key.index),\n      publicKey: key.public_key.replace(/^0x/, \"\"),\n      signAlgo: SignatureAlgorithmIDs[key.signing_algorithm],\n      signAlgoString: key.signing_algorithm,\n      hashAlgo: HashAlgorithmIDs[key.hashing_algorithm],\n      hashAlgoString: key.hashing_algorithm,\n      sequenceNumber: Number(key.sequence_number),\n      weight: Number(key.weight),\n      revoked: key.revoked\n    })) ?? []\n  };\n  return ret;\n}\nasync function sendGetAccount(ix) {\n  let context = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : {};\n  let opts = arguments.length > 2 && arguments[2] !== undefined ? arguments[2] : {};\n  invariant(opts.node, `SDK Send Get Account Error: opts.node must be defined.`);\n  invariant(context.response, `SDK Send Get Account Error: context.response must be defined.`);\n  invariant(context.Buffer, `SDK Send Get Account Error: context.Buffer must be defined.`);\n  ix = await ix;\n  if (ix.block.height !== null) {\n    return await sendGetAccountAtBlockHeightRequest(ix, context, opts);\n  } else {\n    return await sendGetAccountAtLatestBlockRequest(ix, context, opts);\n  }\n}\n\nasync function sendGetBlockHeaderByIDRequest(ix, context, opts) {\n  const httpRequest$1 = opts.httpRequest || httpRequest;\n  const res = await httpRequest$1({\n    hostname: opts.node,\n    path: `/v1/blocks/${ix.block.id}`,\n    method: \"GET\",\n    body: null\n  });\n  return constructResponse$3(ix, context, res);\n}\nasync function sendGetBlockHeaderByHeightRequest(ix, context, opts) {\n  const httpRequest$1 = opts.httpRequest || httpRequest;\n  const res = await httpRequest$1({\n    hostname: opts.node,\n    path: `/v1/blocks?height=${ix.block.height}`,\n    method: \"GET\",\n    body: null\n  });\n  return constructResponse$3(ix, context, res);\n}\nasync function sendGetLatestBlockHeaderRequest(ix, context, opts) {\n  const httpRequest$1 = opts.httpRequest || httpRequest;\n  const height = ix.block?.isSealed ? \"sealed\" : \"final\";\n  const res = await httpRequest$1({\n    hostname: opts.node,\n    path: `/v1/blocks?height=${height}`,\n    method: \"GET\",\n    body: null\n  });\n  return constructResponse$3(ix, context, res);\n}\nfunction constructResponse$3(ix, context, res) {\n  const block = res.length ? res[0] : null;\n  const ret = context.response();\n  ret.tag = ix.tag;\n  ret.blockHeader = {\n    id: block.header.id,\n    parentId: block.header.parent_id,\n    height: Number(block.header.height),\n    timestamp: block.header.timestamp\n  };\n  return ret;\n}\nasync function sendGetBlockHeader(ix) {\n  let context = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : {};\n  let opts = arguments.length > 2 && arguments[2] !== undefined ? arguments[2] : {};\n  invariant(opts.node, `SDK Send Get Block Header Error: opts.node must be defined.`);\n  invariant(context.response, `SDK Send Get Block Header Error: context.response must be defined.`);\n  ix = await ix;\n  const interactionHasBlockID = ix.block.id !== null;\n  const interactionHasBlockHeight = ix.block.height !== null;\n  if (interactionHasBlockID) {\n    return await sendGetBlockHeaderByIDRequest(ix, context, opts);\n  } else if (interactionHasBlockHeight) {\n    return await sendGetBlockHeaderByHeightRequest(ix, context, opts);\n  } else {\n    return await sendGetLatestBlockHeaderRequest(ix, context, opts);\n  }\n}\n\nasync function sendGetBlockByIDRequest(ix, context, opts) {\n  const httpRequest$1 = opts.httpRequest || httpRequest;\n  const res = await httpRequest$1({\n    hostname: opts.node,\n    path: `/v1/blocks/${ix.block.id}?expand=payload`,\n    method: \"GET\",\n    body: null\n  });\n  return constructResponse$2(ix, context, res);\n}\nasync function sendGetBlockByHeightRequest(ix, context, opts) {\n  const httpRequest$1 = opts.httpRequest || httpRequest;\n  const res = await httpRequest$1({\n    hostname: opts.node,\n    path: `/v1/blocks?height=${ix.block.height}&expand=payload`,\n    method: \"GET\",\n    body: null\n  });\n  return constructResponse$2(ix, context, res);\n}\nasync function sendGetBlockRequest(ix, context, opts) {\n  const httpRequest$1 = opts.httpRequest || httpRequest;\n  const height = ix.block?.isSealed ? \"sealed\" : \"final\";\n  const res = await httpRequest$1({\n    hostname: opts.node,\n    path: `/v1/blocks?height=${height}&expand=payload`,\n    method: \"GET\",\n    body: null\n  });\n  return constructResponse$2(ix, context, res);\n}\nfunction constructResponse$2(ix, context, res) {\n  const block = res.length ? res[0] : null;\n  const ret = context.response();\n  ret.tag = ix.tag;\n  ret.block = {\n    id: block.header.id,\n    parentId: block.header.parent_id,\n    height: Number(block.header.height),\n    timestamp: block.header.timestamp,\n    parentVoterSignature: block.header.parent_voter_signature,\n    collectionGuarantees: block.payload.collection_guarantees.map(collectionGuarantee => ({\n      collectionId: collectionGuarantee.collection_id,\n      signerIds: collectionGuarantee.signer_ids\n    })),\n    blockSeals: block.payload.block_seals.map(blockSeal => ({\n      blockId: blockSeal.block_id,\n      executionReceiptId: blockSeal.result_id\n    }))\n  };\n  return ret;\n}\nasync function sendGetBlock(ix) {\n  let context = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : {};\n  let opts = arguments.length > 2 && arguments[2] !== undefined ? arguments[2] : {};\n  invariant(opts.node, `SDK Send Get Block Error: opts.node must be defined.`);\n  invariant(context.response, `SDK Send Get Block Error: context.response must be defined.`);\n  ix = await ix;\n  const interactionHasBlockID = ix.block.id !== null;\n  const interactionHasBlockHeight = ix.block.height !== null;\n  if (interactionHasBlockID) {\n    return await sendGetBlockByIDRequest(ix, context, opts);\n  } else if (interactionHasBlockHeight) {\n    return await sendGetBlockByHeightRequest(ix, context, opts);\n  } else {\n    return await sendGetBlockRequest(ix, context, opts);\n  }\n}\n\nasync function sendGetCollection(ix) {\n  let context = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : {};\n  let opts = arguments.length > 2 && arguments[2] !== undefined ? arguments[2] : {};\n  invariant(opts.node, `SDK Send Get Collection Error: opts.node must be defined.`);\n  invariant(context.response, `SDK Send Get Collection Error: context.response must be defined.`);\n  const httpRequest$1 = opts.httpRequest || httpRequest;\n  const res = await httpRequest$1({\n    hostname: opts.node,\n    path: `/v1/collections/${ix.collection.id}?expand=transactions`,\n    method: \"GET\",\n    body: null\n  });\n  const ret = context.response();\n  ret.tag = ix.tag;\n  ret.collection = {\n    id: res.id,\n    transactionIds: res.transactions.map(transaction => transaction.id)\n  };\n  return ret;\n}\n\nasync function sendGetEventsForHeightRangeRequest(ix, context, opts) {\n  const httpRequest$1 = opts.httpRequest || httpRequest;\n  const res = await httpRequest$1({\n    hostname: opts.node,\n    path: `/v1/events?type=${ix.events.eventType}&start_height=${ix.events.start}&end_height=${ix.events.end}`,\n    method: \"GET\",\n    body: null\n  });\n  return constructResponse$1(ix, context, res);\n}\nasync function sendGetEventsForBlockIDsRequest(ix, context, opts) {\n  const httpRequest$1 = opts.httpRequest || httpRequest;\n  const res = await httpRequest$1({\n    hostname: opts.node,\n    path: `/v1/events?type=${ix.events.eventType}&block_ids=${ix.events.blockIds.join(\",\")}`,\n    method: \"GET\",\n    body: null\n  });\n  return constructResponse$1(ix, context, res);\n}\nfunction constructResponse$1(ix, context, res) {\n  let ret = context.response();\n  ret.tag = ix.tag;\n  ret.events = [];\n  res.forEach(block => block.events ? block.events.forEach(event => ret.events.push({\n    blockId: block.block_id,\n    blockHeight: Number(block.block_height),\n    blockTimestamp: block.block_timestamp,\n    type: event.type,\n    transactionId: event.transaction_id,\n    transactionIndex: Number(event.transaction_index),\n    eventIndex: Number(event.event_index),\n    payload: JSON.parse(context.Buffer.from(event.payload, \"base64\").toString())\n  })) : null);\n  return ret;\n}\nasync function sendGetEvents(ix) {\n  let context = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : {};\n  let opts = arguments.length > 2 && arguments[2] !== undefined ? arguments[2] : {};\n  invariant(opts.node, `SDK Send Get Events Error: opts.node must be defined.`);\n  invariant(context.response, `SDK Send Get Events Error: context.response must be defined.`);\n  invariant(context.Buffer, `SDK Send Get Events Error: context.Buffer must be defined.`);\n  ix = await ix;\n  const interactionContainsBlockHeightRange = ix.events.start !== null;\n  const interactionContainsBlockIDsList = Array.isArray(ix.events.blockIds) && ix.events.blockIds.length > 0;\n  invariant(interactionContainsBlockHeightRange || interactionContainsBlockIDsList, \"SendGetEventsError: Unable to determine which get events request to send. Either a block height range, or block IDs must be specified.\");\n  if (interactionContainsBlockHeightRange) {\n    return await sendGetEventsForHeightRangeRequest(ix, context, opts);\n  } else {\n    return await sendGetEventsForBlockIDsRequest(ix, context, opts);\n  }\n}\n\nasync function sendGetTransaction(ix) {\n  let context = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : {};\n  let opts = arguments.length > 2 && arguments[2] !== undefined ? arguments[2] : {};\n  invariant(opts.node, `SDK Send Get Transaction Error: opts.node must be defined.`);\n  invariant(context.response, `SDK Send Get Transaction Error: context.response must be defined.`);\n  invariant(context.Buffer, `SDK Send Get Transaction Error: context.Buffer must be defined.`);\n  const httpRequest$1 = opts.httpRequest || httpRequest;\n  ix = await ix;\n  const res = await httpRequest$1({\n    hostname: opts.node,\n    path: `/v1/transactions/${ix.transaction.id}`,\n    method: \"GET\",\n    body: null\n  });\n  const unwrapKey = key => ({\n    address: key.address,\n    keyId: Number(key.key_index),\n    sequenceNumber: Number(key.sequence_number)\n  });\n  const unwrapSignature = sig => ({\n    address: sig.address,\n    keyId: Number(sig.key_index),\n    signature: sig.signature\n  });\n  const unwrapArg = arg => JSON.parse(context.Buffer.from(arg, \"base64\").toString());\n  let ret = context.response();\n  ret.tag = ix.tag;\n  ret.transaction = {\n    script: context.Buffer.from(res.script, \"base64\").toString(),\n    args: [...res.arguments.map(unwrapArg)],\n    referenceBlockId: res.reference_block_id,\n    gasLimit: Number(res.gas_limit),\n    payer: res.payer,\n    proposalKey: res.proposal_key ? unwrapKey(res.proposal_key) : res.proposal_key,\n    authorizers: res.authorizers,\n    payloadSignatures: [...res.payload_signatures.map(unwrapSignature)],\n    envelopeSignatures: [...res.envelope_signatures.map(unwrapSignature)]\n  };\n  return ret;\n}\n\nconst STATUS_MAP$1 = {\n  UNKNOWN: 0,\n  PENDING: 1,\n  FINALIZED: 2,\n  EXECUTED: 3,\n  SEALED: 4,\n  EXPIRED: 5\n};\nasync function sendGetTransactionStatus(ix) {\n  let context = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : {};\n  let opts = arguments.length > 2 && arguments[2] !== undefined ? arguments[2] : {};\n  invariant(opts.node, `SDK Send Get Transaction Status Error: opts.node must be defined.`);\n  invariant(context.response, `SDK Send Get Transaction Status Error: context.response must be defined.`);\n  invariant(context.Buffer, `SDK Send Get Transaction Status Error: context.Buffer must be defined.`);\n  const httpRequest$1 = opts.httpRequest || httpRequest;\n  ix = await ix;\n  const res = await httpRequest$1({\n    hostname: opts.node,\n    path: `/v1/transaction_results/${ix.transaction.id}`,\n    method: \"GET\",\n    body: null\n  });\n  let ret = context.response();\n  ret.tag = ix.tag;\n  ret.transactionStatus = {\n    blockId: res.block_id,\n    status: STATUS_MAP$1[res.status.toUpperCase()] || \"\",\n    statusString: res.status.toUpperCase(),\n    statusCode: res.status_code,\n    errorMessage: res.error_message,\n    events: res.events.map(event => ({\n      type: event.type,\n      transactionId: event.transaction_id,\n      transactionIndex: Number(event.transaction_index),\n      eventIndex: Number(event.event_index),\n      payload: JSON.parse(context.Buffer.from(event.payload, \"base64\").toString())\n    }))\n  };\n  return ret;\n}\n\nasync function sendPing(ix) {\n  let context = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : {};\n  let opts = arguments.length > 2 && arguments[2] !== undefined ? arguments[2] : {};\n  invariant(Boolean(opts.node), `SDK Send Ping Error: opts.node must be defined.`);\n  invariant(Boolean(context.response), `SDK Send Ping Error: context.response must be defined.`);\n  const httpRequest$1 = opts.httpRequest || httpRequest;\n  await httpRequest$1({\n    hostname: opts.node,\n    path: \"/v1/blocks?height=sealed\",\n    method: \"GET\",\n    body: null\n  });\n  let ret = typeof context?.response === \"function\" ? context.response() : {};\n  ret.tag = ix.tag;\n  return ret;\n}\n\nconst idof = acct => `${withPrefix(acct.addr)}-${acct.keyId}`;\nasync function sendTransaction(ix) {\n  let context = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : {};\n  let opts = arguments.length > 2 && arguments[2] !== undefined ? arguments[2] : {};\n  invariant(opts.node, `SDK Send Transaction Error: opts.node must be defined.`);\n  invariant(context.response, `SDK Send Transaction Error: context.response must be defined.`);\n  invariant(context.Buffer, `SDK Send Transaction Error: context.Buffer must be defined.`);\n  const httpRequest$1 = opts.httpRequest || httpRequest;\n  ix = await ix;\n\n  // Apply Non Payer Signatures to Payload Signatures\n  let payloadSignatures = [];\n  for (let acct of Object.values(ix.accounts)) {\n    try {\n      if (!acct.role.payer && acct.signature != null) {\n        const signature = {\n          address: sansPrefix(acct.addr),\n          key_index: String(acct.keyId),\n          signature: context.Buffer.from(acct.signature, \"hex\").toString(\"base64\")\n        };\n        if (!payloadSignatures.find(existingSignature => existingSignature.address === signature.address && existingSignature.key_index === signature.key_index && existingSignature.signature === signature.signature)) {\n          payloadSignatures.push(signature);\n        }\n      }\n    } catch (error) {\n      console.error(\"SDK HTTP Send Error: Trouble applying payload signature\", {\n        acct,\n        ix\n      });\n      throw error;\n    }\n  }\n\n  // Apply Payer Signatures to Envelope Signatures\n  let envelopeSignatures = {};\n  for (let acct of Object.values(ix.accounts)) {\n    try {\n      if (acct.role.payer && acct.signature != null) {\n        let id = acct.tempId || idof(acct);\n        envelopeSignatures[id] = envelopeSignatures[id] || {\n          address: sansPrefix(acct.addr),\n          key_index: String(acct.keyId),\n          signature: context.Buffer.from(acct.signature, \"hex\").toString(\"base64\")\n        };\n      }\n    } catch (error) {\n      console.error(\"SDK HTTP Send Error: Trouble applying envelope signature\", {\n        acct,\n        ix\n      });\n      throw error;\n    }\n  }\n  envelopeSignatures = Object.values(envelopeSignatures);\n  var t1 = Date.now();\n  const res = await httpRequest$1({\n    hostname: opts.node,\n    path: `/v1/transactions`,\n    method: \"POST\",\n    body: {\n      script: context.Buffer.from(ix.message.cadence).toString(\"base64\"),\n      arguments: [...ix.message.arguments.map(arg => context.Buffer.from(JSON.stringify(ix.arguments[arg].asArgument)).toString(\"base64\"))],\n      reference_block_id: ix.message.refBlock ? ix.message.refBlock : null,\n      gas_limit: String(ix.message.computeLimit),\n      payer: sansPrefix(ix.accounts[Array.isArray(ix.payer) ? ix.payer[0] : ix.payer].addr),\n      proposal_key: {\n        address: sansPrefix(ix.accounts[ix.proposer].addr),\n        key_index: String(ix.accounts[ix.proposer].keyId),\n        sequence_number: String(ix.accounts[ix.proposer].sequenceNum)\n      },\n      authorizers: ix.authorizations.map(tempId => ix.accounts[tempId].addr).reduce((prev, current) => {\n        return prev.find(item => item === current) ? prev : [...prev, current];\n      }, []).map(sansPrefix),\n      payload_signatures: payloadSignatures,\n      envelope_signatures: envelopeSignatures\n    }\n  });\n  var t2 = Date.now();\n  let ret = context.response();\n  ret.tag = ix.tag;\n  ret.transactionId = res.id;\n  if (typeof window !== \"undefined\" && typeof CustomEvent !== \"undefined\") {\n    window.dispatchEvent(new CustomEvent(\"FLOW::TX\", {\n      detail: {\n        txId: ret.transactionId,\n        delta: t2 - t1\n      }\n    }));\n  }\n  return ret;\n}\n\nasync function sendGetNetworkParameters(ix) {\n  let context = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : {};\n  let opts = arguments.length > 2 && arguments[2] !== undefined ? arguments[2] : {};\n  invariant(opts.node, `SDK Send Get Network Parameters Error: opts.node must be defined.`);\n  invariant(context.response, `SDK Send Get Network Parameters Error: context.response must be defined.`);\n  const httpRequest$1 = opts.httpRequest || httpRequest;\n  ix = await ix;\n  const res = await httpRequest$1({\n    hostname: opts.node,\n    path: `/v1/network/parameters`,\n    method: \"GET\",\n    body: null,\n    enableRequestLogging: opts.enableRequestLogging ?? true\n  });\n  let ret = context.response();\n  ret.tag = ix.tag;\n  ret.networkParameters = {\n    chainId: res.chain_id\n  };\n  return ret;\n}\n\nasync function sendGetNodeVersionInfo(ix) {\n  let context = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : {};\n  let opts = arguments.length > 2 && arguments[2] !== undefined ? arguments[2] : {};\n  invariant(opts.node, `SDK Send Get Node Version Info Error: opts.node must be defined.`);\n  invariant(context.response, `SDK Send Get Node Verison Info Error: context.response must be defined.`);\n  const httpRequest$1 = opts.httpRequest || httpRequest;\n  ix = await ix;\n  const res = await httpRequest$1({\n    hostname: opts.node,\n    path: `/v1/node_version_info`,\n    method: \"GET\"\n  });\n  let ret = context.response();\n  ret.tag = ix.tag;\n  ret.nodeVersionInfo = {\n    semver: res.semver,\n    commit: res.commit,\n    sporkId: res.spork_id,\n    protocolVersion: parseInt(res.protocol_version),\n    sporkRootBlockHeight: parseInt(res.spork_root_block_height),\n    nodeRootBlockHeight: parseInt(res.node_root_block_height)\n  };\n  return ret;\n}\n\nconst WebSocket = _WebSocket;\n\nclass WebsocketError extends Error {\n  constructor(_ref) {\n    let {\n      code,\n      reason,\n      message,\n      wasClean\n    } = _ref;\n    const msg = `\n      connectWs: connection closed with error${message ? `: ${message}` : \"\"}\n      ${code ? `code: ${code}` : \"\"}\n      ${reason ? `reason: ${reason}` : \"\"}\n      ${wasClean ? `wasClean: ${wasClean}` : \"\"}\n    `;\n    super(msg);\n    this.name = \"WebsocketError\";\n    this.code = code;\n    this.reason = reason;\n    this.wasClean = false;\n  }\n}\nfunction connectWs(_ref2) {\n  let {\n    hostname,\n    path,\n    params,\n    getParams,\n    retryLimit = 5,\n    retryIntervalMs = 1000\n  } = _ref2;\n  if (getParams && params) {\n    throw new Error(\"connectWs: cannot specify both params and getParams\");\n  }\n  let outputEmitter = new EventEmitter();\n  let retryCount = 0;\n  const resolveParams = getParams || (() => params);\n  let close = () => {};\n  (function connect() {\n    let userClosed = false;\n    let hasOpened = false;\n\n    // Build a websocket connection with correct protocol & params\n    const url = buildConnectionUrl(hostname, path, resolveParams());\n    const ws = new WebSocket(url);\n    ws.onmessage = function (e) {\n      const data = safeParseJSON(e.data);\n      if (data) {\n        outputEmitter.emit(\"data\", data);\n      } else {\n        outputEmitter.emit(\"error\", new WebsocketError({\n          message: \"invalid JSON data\"\n        }));\n        this.close();\n      }\n    };\n    ws.onclose = function (e) {\n      if (userClosed) {\n        outputEmitter.emit(\"close\");\n        outputEmitter.removeAllListeners();\n        return;\n      }\n      if (!hasOpened) {\n        if (retryCount < retryLimit) {\n          retryCount++;\n          setTimeout(connect, retryIntervalMs);\n        } else {\n          outputEmitter.emit(\"error\", new WebsocketError({\n            wasClean: e.wasClean,\n            code: e.code,\n            reason: e.reason,\n            message: \"failed to connect\"\n          }));\n\n          // Emit close event on next tick so that the error event is emitted first\n          setTimeout(() => {\n            outputEmitter.emit(\"close\");\n            outputEmitter.removeAllListeners();\n          });\n        }\n      } else {\n        // If the connection was established before closing, attempt to reconnect\n        setTimeout(connect, retryIntervalMs);\n      }\n    };\n    ws.onopen = function () {\n      hasOpened = true;\n      retryCount = 0;\n    };\n    close = () => {\n      userClosed = true;\n      ws.close();\n    };\n  })();\n  return {\n    on(event, listener) {\n      outputEmitter.on(event, listener);\n      return this;\n    },\n    off(event, listener) {\n      outputEmitter.off(event, listener);\n      return this;\n    },\n    close() {\n      close();\n    }\n  };\n}\nfunction buildConnectionUrl(hostname, path, params) {\n  const url = new URL(path || \"\", hostname);\n  if (url.protocol === \"https:\") {\n    url.protocol = \"wss:\";\n  } else if (url.protocol === \"http:\") {\n    url.protocol = \"ws:\";\n  }\n  Object.entries(params || {}).forEach(_ref3 => {\n    let [key, value] = _ref3;\n    if (value) {\n      let formattedValue;\n      if (Array.isArray(value)) {\n        formattedValue = value.join(\",\");\n      } else {\n        formattedValue = value.toString();\n      }\n      url.searchParams.append(key, formattedValue);\n    }\n  });\n  return url.toString();\n}\n\nfunction constructData(ix, context, data) {\n  const response = context.response();\n  response.tag = ix.tag;\n  response.events = data.Events?.length > 0 ? data.Events.map(event => ({\n    blockId: data.BlockID,\n    blockHeight: Number(data.Height),\n    blockTimestamp: data.BlockTimestamp,\n    type: event.Type,\n    transactionId: event.TransactionID,\n    transactionIndex: Number(event.TransactionIndex),\n    eventIndex: Number(event.EventIndex),\n    payload: JSON.parse(context.Buffer.from(event.Payload, \"base64\").toString())\n  })) : null;\n  response.heartbeat = {\n    blockId: data.BlockID,\n    blockHeight: Number(data.Height),\n    blockTimestamp: data.BlockTimestamp\n  };\n  return response;\n}\nfunction constructResponse(ix, context, stream) {\n  const response = context.response();\n  response.tag = ix.tag;\n  response.streamConnection = stream;\n  return response;\n}\nasync function connectSubscribeEvents(ix) {\n  let context = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : {};\n  let opts = arguments.length > 2 && arguments[2] !== undefined ? arguments[2] : {};\n  invariant(opts.node, `SDK Send Get Events Error: opts.node must be defined.`);\n  invariant(context.response, `SDK Send Get Events Error: context.response must be defined.`);\n  invariant(context.Buffer, `SDK Send Get Events Error: context.Buffer must be defined.`);\n  const resolvedIx = await ix;\n  const connectWs$1 = opts.connectWs || connectWs;\n  const outputEmitter = new EventEmitter();\n  let lastBlockHeight = null;\n\n  // Connect to the websocket & provide reconnection parameters\n  const connection = connectWs$1({\n    hostname: opts.node,\n    path: `/v1/subscribe_events`,\n    getParams: () => {\n      const params = {\n        event_types: resolvedIx.subscribeEvents?.eventTypes,\n        addresses: resolvedIx.subscribeEvents?.addresses,\n        contracts: resolvedIx.subscribeEvents?.contracts,\n        heartbeat_interval: resolvedIx.subscribeEvents?.heartbeatInterval\n      };\n\n      // If the lastBlockId is set, use it to resume the stream\n      if (lastBlockHeight) {\n        params.start_height = lastBlockHeight + 1;\n      } else {\n        params.start_block_id = resolvedIx.subscribeEvents?.startBlockId;\n        params.start_height = resolvedIx.subscribeEvents?.startHeight;\n      }\n      return params;\n    }\n  });\n\n  // Map the connection to a formatted response stream\n  connection.on(\"data\", data => {\n    const responseData = constructData(resolvedIx, context, data);\n    lastBlockHeight = responseData.heartbeat.blockHeight;\n    outputEmitter.emit(\"data\", responseData);\n  });\n  connection.on(\"error\", error => {\n    outputEmitter.emit(\"error\", error);\n  });\n  connection.on(\"close\", () => {\n    outputEmitter.emit(\"close\");\n  });\n  const responseStream = {\n    on(event, listener) {\n      outputEmitter.on(event, listener);\n      return this;\n    },\n    off(event, listener) {\n      outputEmitter.off(event, listener);\n      return this;\n    },\n    close() {\n      connection.close();\n    }\n  };\n  return constructResponse(resolvedIx, context, responseStream);\n}\n\nconst send = async function (ix, context) {\n  let opts = arguments.length > 2 && arguments[2] !== undefined ? arguments[2] : {};\n  invariant(Boolean(opts?.node), `SDK Send Error: Either opts.node or \"accessNode.api\" in config must be defined.`);\n  invariant(Boolean(context.ix), `SDK Send Error: context.ix must be defined.`);\n  ix = await ix;\n\n  // prettier-ignore\n  switch (true) {\n    case context.ix.isTransaction(ix):\n      return opts.sendTransaction ? opts.sendTransaction(ix, context, opts) : sendTransaction(ix, context, opts);\n    case context.ix.isGetTransactionStatus(ix):\n      return opts.sendGetTransactionStatus ? opts.sendGetTransactionStatus(ix, context, opts) : sendGetTransactionStatus(ix, context, opts);\n    case context.ix.isGetTransaction(ix):\n      return opts.sendGetTransaction ? opts.sendGetTransaction(ix, context, opts) : sendGetTransaction(ix, context, opts);\n    case context.ix.isScript(ix):\n      return opts.sendExecuteScript ? opts.sendExecuteScript(ix, context, opts) : sendExecuteScript(ix, context, opts);\n    case context.ix.isGetAccount(ix):\n      return opts.sendGetAccount ? opts.sendGetAccount(ix, context, opts) : sendGetAccount(ix, context, opts);\n    case context.ix.isGetEvents(ix):\n      return opts.sendGetEvents ? opts.sendGetEvents(ix, context, opts) : sendGetEvents(ix, context, opts);\n    case context.ix.isSubscribeEvents?.(ix):\n      return opts.connectSubscribeEvents ? opts.connectSubscribeEvents(ix, context, opts) : connectSubscribeEvents(ix, context, opts);\n    case context.ix.isGetBlock(ix):\n      return opts.sendGetBlock ? opts.sendGetBlock(ix, context, opts) : sendGetBlock(ix, context, opts);\n    case context.ix.isGetBlockHeader(ix):\n      return opts.sendGetBlockHeader ? opts.sendGetBlockHeader(ix, context, opts) : sendGetBlockHeader(ix, context, opts);\n    case context.ix.isGetCollection(ix):\n      return opts.sendGetCollection ? opts.sendGetCollection(ix, context, opts) : sendGetCollection(ix, context, opts);\n    case context.ix.isPing(ix):\n      return opts.sendPing ? opts.sendPing(ix, context, opts) : sendPing(ix, context, opts);\n    case context.ix.isGetNetworkParameters(ix):\n      return opts.sendGetNetworkParameters ? opts.sendGetNetworkParameters(ix, context, opts) : sendGetNetworkParameters(ix, context, opts);\n    case context.ix.isGetNodeVersionInfo?.(ix):\n      return opts.sendGetNodeVersionInfo ? opts.sendGetNodeVersionInfo(ix, context, opts) : sendGetNodeVersionInfo(ix, context, opts);\n    default:\n      return ix;\n  }\n};\n\nlet Action = /*#__PURE__*/function (Action) {\n  Action[\"LIST_SUBSCRIPTIONS\"] = \"list_subscriptions\";\n  Action[\"SUBSCRIBE\"] = \"subscribe\";\n  Action[\"UNSUBSCRIBE\"] = \"unsubscribe\";\n  return Action;\n}({});\nclass SocketError extends Error {\n  constructor(code, message) {\n    super(message);\n    this.name = \"SocketError\";\n    this.code = code;\n  }\n  static fromMessage(error) {\n    return new SocketError(error.code, error.message);\n  }\n}\n\nconst WS_OPEN = 1;\nclass SubscriptionManager {\n  counter = 0;\n  socket = null;\n  subscriptions = [];\n  reconnectAttempts = 0;\n  connectPromise = null;\n  closeConnection = null;\n  constructor(handlers, config) {\n    this.config = {\n      ...config,\n      reconnectOptions: {\n        initialReconnectDelay: 500,\n        reconnectAttempts: 5,\n        maxReconnectDelay: 5000,\n        ...config.reconnectOptions\n      }\n    };\n    this.handlers = handlers;\n  }\n  subscribe(opts) {\n    const idPromise = this._subscribe(opts);\n    return {\n      unsubscribe: () => {\n        // Unsubscribe when the ID is available\n        idPromise.then(id => id && this.unsubscribe(id));\n      }\n    };\n  }\n  async _subscribe(opts) {\n    // Get the data provider for the topic\n    const topicHandler = this.getHandler(opts.topic);\n    const subscriber = topicHandler.createSubscriber(opts.args, opts.onData, opts.onError);\n    let sub = null;\n    try {\n      // Connect the socket if it's not already open\n      await this.connect();\n\n      // Track the subscription locally\n      sub = {\n        id: String(this.counter++),\n        topic: opts.topic,\n        subscriber: subscriber\n      };\n      this.subscriptions.push(sub);\n\n      // Send the subscribe message\n      const response = await this.sendSubscribe(sub);\n      if (response.error) {\n        throw new Error(`Failed to subscribe to topic ${sub.topic}`, {\n          cause: SocketError.fromMessage(response.error)\n        });\n      }\n    } catch (e) {\n      // Unsubscribe if there was an error\n      subscriber.onError(e instanceof Error ? e : new Error(String(e)));\n      if (sub) this.unsubscribe(sub.id);\n      return null;\n    }\n    return sub.id;\n  }\n  unsubscribe(id) {\n    // Get the subscription\n    const sub = this.subscriptions.find(sub => sub.id === id);\n    if (!sub) return;\n\n    // Remove the subscription\n    this.subscriptions = this.subscriptions.filter(sub => sub.id !== id);\n\n    // Close the socket if there are no more subscriptions\n    if (this.subscriptions.length === 0) {\n      this.closeConnection?.();\n      return;\n    }\n\n    // Otherwise, the unsubscribe message\n    this.sendUnsubscribe(sub).catch(e => {\n      console.error(`Error while unsubscribing from topic: ${e}`);\n    });\n  }\n\n  // Lazy connect to the socket when the first subscription is made\n  async connect() {\n    if (this.connectPromise) {\n      return this.connectPromise;\n    }\n    this.connectPromise = new Promise((resolve, reject) => {\n      // If the socket is already open, do nothing\n      if (this.socket?.readyState === WS_OPEN) {\n        resolve();\n        return;\n      }\n      this.socket = new WebSocket(this.config.node);\n      const onMessage = event => {\n        const message = JSON.parse(event.data);\n\n        // Error message\n        if (\"action\" in message && message.error) {\n          const sub = this.subscriptions.find(sub => sub.id === message.subscription_id);\n          if (sub) {\n            sub.subscriber.onError(new Error(`Failed to subscribe to topic ${sub.topic}: ${message.error.message}`));\n            // Remove the subscription\n            this.subscriptions = this.subscriptions.filter(sub => sub.id !== message.subscription_id);\n          }\n          return;\n        }\n        const sub = this.subscriptions.find(sub => sub.id === message.subscription_id);\n        if (sub) {\n          if (!(\"action\" in message) && message.subscription_id === sub.id) {\n            sub.subscriber.onData(message.payload);\n          }\n        }\n      };\n      const onClose = () => {\n        this.handleSocketError(new Error(\"WebSocket closed\")).then(() => {\n          resolve();\n        }).catch(e => {\n          reject(e);\n        });\n      };\n      const onOpen = () => {\n        resolve();\n      };\n      this.socket.addEventListener(\"message\", onMessage);\n      this.socket.addEventListener(\"close\", onClose);\n      this.socket.addEventListener(\"open\", onOpen);\n      this.closeConnection = () => {\n        this.socket?.removeEventListener(\"message\", onMessage);\n        this.socket?.removeEventListener(\"close\", onClose);\n        this.socket?.removeEventListener(\"open\", onOpen);\n        this.socket?.close();\n        this.socket = null;\n        this.closeConnection = null;\n        this.connectPromise = null;\n      };\n    });\n    return this.connectPromise;\n  }\n  async handleSocketError(error) {\n    // Cleanup the connection\n    this.closeConnection?.();\n\n    // Validate the number of reconnection attempts\n    if (++this.reconnectAttempts >= this.config.reconnectOptions.reconnectAttempts) {\n      logger.log({\n        level: logger.LEVELS.error,\n        title: \"WebSocket Error\",\n        message: `Failed to reconnect to the server after ${this.reconnectAttempts + 1} attempts: ${error}`\n      });\n      this.subscriptions.forEach(sub => {\n        sub.subscriber.onError(new Error(`Failed to reconnect to the server after ${this.reconnectAttempts + 1} attempts: ${error}`));\n      });\n      this.subscriptions = [];\n      this.reconnectAttempts = 0;\n      throw error;\n    } else {\n      logger.log({\n        level: logger.LEVELS.warn,\n        title: \"WebSocket Error\",\n        message: `WebSocket error, reconnecting in ${this.backoffInterval}ms: ${error}`\n      });\n\n      // Delay the reconnection\n      await new Promise(resolve => setTimeout(resolve, this.backoffInterval));\n\n      // Try to reconnect\n      await this.connect();\n\n      // Restore subscriptions\n      await Promise.all(this.subscriptions.map(async sub => {\n        await this.sendSubscribe(sub).catch(e => {\n          sub.subscriber.onError(new Error(`Failed to restore subscription: ${e}`));\n          // Remove the subscription\n          this.subscriptions = this.subscriptions.filter(s => s.id !== sub.id);\n        });\n      }));\n      this.reconnectAttempts = 0;\n    }\n  }\n  async sendSubscribe(sub) {\n    // Send the subscription message\n    const request = {\n      action: Action.SUBSCRIBE,\n      topic: sub.topic,\n      arguments: sub.subscriber.getConnectionArgs(),\n      subscription_id: String(sub.id)\n    };\n    const response = await this.request(request);\n    if (response.error) {\n      throw new Error(`Failed to subscribe to topic ${sub.topic}`, {\n        cause: SocketError.fromMessage(response.error)\n      });\n    }\n    return response;\n  }\n  async sendUnsubscribe(sub) {\n    // Send the unsubscribe message if the subscription has a remote id\n    const request = {\n      action: Action.UNSUBSCRIBE,\n      subscription_id: sub.id\n    };\n    this.socket?.send(JSON.stringify(request));\n    const response = await this.request(request);\n    if (response.error) {\n      throw new Error(`Failed to unsubscribe from topic ${sub.topic}`, {\n        cause: SocketError.fromMessage(response.error)\n      });\n    }\n    return response;\n  }\n  async request(request) {\n    let cleanup = () => {};\n    return await new Promise((resolve, reject) => {\n      if (!this.socket) {\n        reject(new Error(\"WebSocket is not connected\"));\n        return;\n      }\n\n      // Set the cleanup function to remove the event listeners\n      cleanup = () => {\n        this.socket?.removeEventListener(\"error\", onError);\n        this.socket?.removeEventListener(\"message\", onMessage);\n        this.socket?.removeEventListener(\"close\", onClose);\n      };\n\n      // Bind event listeners\n      this.socket.addEventListener(\"error\", onError);\n      this.socket.addEventListener(\"message\", onMessage);\n      this.socket.addEventListener(\"close\", onClose);\n\n      // Send the request\n      this.socket.send(JSON.stringify(request));\n      function onError(e) {\n        reject(new Error(`WebSocket error: ${e}`));\n      }\n      function onClose() {\n        reject(new Error(\"WebSocket closed\"));\n      }\n      function onMessage(event) {\n        const data = JSON.parse(event.data);\n        if (data.subscription_id === request.subscription_id) {\n          resolve(data);\n        }\n      }\n    }).finally(() => {\n      cleanup();\n    });\n  }\n  getHandler(topic) {\n    const handler = this.handlers.find(handler => handler.topic === topic);\n    if (!handler) {\n      throw new Error(`No handler found for topic ${topic}`);\n    }\n    return handler;\n  }\n\n  /**\n   * Calculate the backoff interval for reconnection attempts\n   * @returns The backoff interval in milliseconds\n   */\n  get backoffInterval() {\n    return Math.min(this.config.reconnectOptions.maxReconnectDelay, this.config.reconnectOptions.initialReconnectDelay * 2 ** this.reconnectAttempts);\n  }\n}\n\nfunction createSubscriptionHandler(handler) {\n  return handler;\n}\n\nconst blocksHandler = createSubscriptionHandler({\n  topic: SubscriptionTopic.BLOCKS,\n  createSubscriber: (initialArgs, onData, onError) => {\n    let resumeArgs = {\n      ...initialArgs\n    };\n    return {\n      onData(data) {\n        // Parse the raw data\n        const parsedData = {\n          block: {\n            id: data.header.id,\n            parentId: data.header.parent_id,\n            height: Number(data.header.height),\n            timestamp: data.header.timestamp,\n            parentVoterSignature: data.header.parent_voter_signature,\n            collectionGuarantees: data.payload.collection_guarantees.map(guarantee => ({\n              collectionId: guarantee.collection_id,\n              signerIds: guarantee.signer_indices\n            })),\n            blockSeals: data.payload.block_seals.map(seal => ({\n              blockId: seal.block_id,\n              executionReceiptId: seal.result_id\n            }))\n          }\n        };\n\n        // Update the resume args\n        resumeArgs = {\n          blockStatus: resumeArgs.blockStatus,\n          startBlockHeight: Number(BigInt(data.header.height) + BigInt(1))\n        };\n        onData(parsedData);\n      },\n      onError(error) {\n        onError(error);\n      },\n      getConnectionArgs() {\n        let encodedArgs = {\n          block_status: resumeArgs.blockStatus\n        };\n        if (\"startBlockHeight\" in resumeArgs && resumeArgs.startBlockHeight) {\n          return {\n            ...encodedArgs,\n            start_block_height: String(resumeArgs.startBlockHeight)\n          };\n        }\n        if (\"startBlockId\" in resumeArgs && resumeArgs.startBlockId) {\n          return {\n            ...encodedArgs,\n            start_block_id: resumeArgs.startBlockId\n          };\n        }\n        return encodedArgs;\n      }\n    };\n  }\n});\n\nconst blockHeadersHandler = createSubscriptionHandler({\n  topic: SubscriptionTopic.BLOCK_HEADERS,\n  createSubscriber: (initialArgs, onData, onError) => {\n    let resumeArgs = {\n      ...initialArgs\n    };\n    return {\n      onData(data) {\n        // Parse the raw data\n        const parsedData = {\n          blockHeader: {\n            id: data.id,\n            parentId: data.parent_id,\n            height: Number(data.height),\n            timestamp: data.timestamp,\n            parentVoterSignature: data.parent_voter_signature\n          }\n        };\n\n        // Update the resume args\n        resumeArgs = {\n          blockStatus: resumeArgs.blockStatus,\n          startBlockHeight: Number(BigInt(data.height) + BigInt(1))\n        };\n        onData(parsedData);\n      },\n      onError(error) {\n        onError(error);\n      },\n      getConnectionArgs() {\n        let encodedArgs = {\n          block_status: resumeArgs.blockStatus\n        };\n        if (\"startBlockHeight\" in resumeArgs && resumeArgs.startBlockHeight) {\n          return {\n            ...encodedArgs,\n            start_block_height: resumeArgs.startBlockHeight\n          };\n        }\n        if (\"startBlockId\" in resumeArgs && resumeArgs.startBlockId) {\n          return {\n            ...encodedArgs,\n            start_block_id: resumeArgs.startBlockId\n          };\n        }\n        return encodedArgs;\n      }\n    };\n  }\n});\n\nconst blockDigestsHandler = createSubscriptionHandler({\n  topic: SubscriptionTopic.BLOCK_DIGESTS,\n  createSubscriber: (initialArgs, onData, onError) => {\n    let resumeArgs = {\n      ...initialArgs\n    };\n    return {\n      onData(data) {\n        // Parse the raw data\n        const parsedData = {\n          blockDigest: {\n            id: data.block_id,\n            height: Number(data.height),\n            timestamp: data.timestamp\n          }\n        };\n\n        // Update the resume args\n        resumeArgs = {\n          blockStatus: resumeArgs.blockStatus,\n          startBlockId: String(BigInt(data.height) + BigInt(1))\n        };\n        onData(parsedData);\n      },\n      onError(error) {\n        onError(error);\n      },\n      getConnectionArgs() {\n        let encodedArgs = {\n          block_status: resumeArgs.blockStatus\n        };\n        if (\"startBlockHeight\" in resumeArgs && resumeArgs.startBlockHeight) {\n          return {\n            ...encodedArgs,\n            start_block_height: resumeArgs.startBlockHeight\n          };\n        }\n        if (\"startBlockId\" in resumeArgs && resumeArgs.startBlockId) {\n          return {\n            ...encodedArgs,\n            start_block_id: resumeArgs.startBlockId\n          };\n        }\n        return encodedArgs;\n      }\n    };\n  }\n});\n\nconst accountStatusesHandler = createSubscriptionHandler({\n  topic: SubscriptionTopic.ACCOUNT_STATUSES,\n  createSubscriber: (initialArgs, onData, onError) => {\n    let resumeArgs = {\n      ...initialArgs\n    };\n    return {\n      onData(rawData) {\n        const data = [];\n        for (const [address, events] of Object.entries(rawData.account_events)) {\n          for (const event of events) {\n            // Parse the raw data\n            const parsedData = {\n              accountStatusEvent: {\n                accountAddress: address,\n                blockId: rawData.block_id,\n                blockHeight: Number(rawData.height),\n                type: event.type,\n                transactionId: event.transaction_id,\n                transactionIndex: Number(event.transaction_index),\n                eventIndex: Number(event.event_index),\n                payload: JSON.parse(Buffer.from(event.payload, \"base64\").toString())\n              }\n            };\n            data.push(parsedData);\n          }\n\n          // Sort the messages by increasing message index\n          data.sort((a, b) => {\n            const txIndexDiff = a.accountStatusEvent.transactionIndex - b.accountStatusEvent.transactionIndex;\n            if (txIndexDiff !== 0) return txIndexDiff;\n            return a.accountStatusEvent.eventIndex - b.accountStatusEvent.eventIndex;\n          });\n\n          // Emit the messages\n          for (const message of data) {\n            onData(message);\n          }\n\n          // Update the resume args\n          resumeArgs = {\n            ...resumeArgs,\n            startBlockHeight: Number(BigInt(rawData.height) + BigInt(1)),\n            startBlockId: undefined\n          };\n        }\n      },\n      onError(error) {\n        onError(error);\n      },\n      getConnectionArgs() {\n        let encodedArgs = {\n          event_types: resumeArgs.eventTypes,\n          addresses: resumeArgs.addresses,\n          account_addresses: resumeArgs.accountAddresses\n        };\n        if (\"startBlockHeight\" in resumeArgs && resumeArgs.startBlockHeight) {\n          return {\n            ...encodedArgs,\n            start_block_height: resumeArgs.startBlockHeight\n          };\n        }\n        if (\"startBlockId\" in resumeArgs && resumeArgs.startBlockId) {\n          return {\n            ...encodedArgs,\n            start_block_id: resumeArgs.startBlockId\n          };\n        }\n        return encodedArgs;\n      }\n    };\n  }\n});\n\nconst STATUS_MAP = {\n  UNKNOWN: 0,\n  PENDING: 1,\n  FINALIZED: 2,\n  EXECUTED: 3,\n  SEALED: 4,\n  EXPIRED: 5\n};\nconst transactionStatusesHandler = createSubscriptionHandler({\n  topic: SubscriptionTopic.TRANSACTION_STATUSES,\n  createSubscriber: (initialArgs, onData, onError) => {\n    let resumeArgs = {\n      ...initialArgs\n    };\n    return {\n      onData(data) {\n        // Parse the raw data\n        const parsedData = {\n          transactionStatus: {\n            blockId: data.transaction_result.block_id,\n            status: STATUS_MAP[data.transaction_result.status.toUpperCase()],\n            statusString: data.transaction_result.status.toUpperCase(),\n            statusCode: data.transaction_result.status_code,\n            errorMessage: data.transaction_result.error_message,\n            events: data.transaction_result.events.map(event => ({\n              type: event.type,\n              transactionId: event.transaction_id,\n              transactionIndex: Number(event.transaction_index),\n              eventIndex: Number(event.event_index),\n              payload: JSON.parse(Buffer$1.from(event.payload, \"base64\").toString())\n            }))\n          }\n        };\n        onData(parsedData);\n      },\n      onError(error) {\n        onError(error);\n      },\n      getConnectionArgs() {\n        return {\n          tx_id: resumeArgs.transactionId\n        };\n      }\n    };\n  }\n});\n\nconst eventsHandler = createSubscriptionHandler({\n  topic: SubscriptionTopic.EVENTS,\n  createSubscriber: (initialArgs, onData, onError) => {\n    let resumeArgs = {\n      ...initialArgs\n    };\n    return {\n      onData(rawData) {\n        for (const event of rawData.events) {\n          // Parse the raw data\n          const result = {\n            event: {\n              blockId: rawData.block_id,\n              blockHeight: Number(rawData.block_height),\n              blockTimestamp: rawData.block_timestamp,\n              type: event.type,\n              transactionId: event.transaction_id,\n              transactionIndex: Number(event.transaction_index),\n              eventIndex: Number(event.event_index),\n              payload: JSON.parse(Buffer.from(event.payload, \"base64\").toString())\n            }\n          };\n          onData(result);\n        }\n\n        // Update the resume args\n        resumeArgs = {\n          ...resumeArgs,\n          startHeight: Number(BigInt(rawData.block_height) + BigInt(1)),\n          startBlockId: undefined\n        };\n      },\n      onError(error) {\n        onError(error);\n      },\n      getConnectionArgs() {\n        let encodedArgs = {\n          event_types: resumeArgs.eventTypes,\n          addresses: resumeArgs.addresses,\n          contracts: resumeArgs.contracts\n        };\n        if (\"startBlockHeight\" in resumeArgs && resumeArgs.startBlockHeight) {\n          return {\n            ...encodedArgs,\n            start_block_height: resumeArgs.startBlockHeight\n          };\n        }\n        if (\"startBlockId\" in resumeArgs && resumeArgs.startBlockId) {\n          return {\n            ...encodedArgs,\n            start_block_id: resumeArgs.startBlockId\n          };\n        }\n        return encodedArgs;\n      }\n    };\n  }\n});\n\nconst SUBSCRIPTION_HANDLERS = [blocksHandler, blockHeadersHandler, blockDigestsHandler, accountStatusesHandler, transactionStatusesHandler, eventsHandler];\n\n// Map of SubscriptionManager instances by access node URL\nlet subscriptionManagerMap = new Map();\nfunction subscribe(_ref, opts) {\n  let {\n    topic,\n    args,\n    onData,\n    onError\n  } = _ref;\n  // Get the SubscriptionManager instance for the access node, or create a new one\n  const node = getWsUrl(opts.node);\n  const manager = subscriptionManagerMap.get(node) || new SubscriptionManager(SUBSCRIPTION_HANDLERS, {\n    node\n  });\n  subscriptionManagerMap.set(node, manager);\n  return manager.subscribe({\n    topic,\n    args,\n    onData: onData,\n    onError\n  });\n}\nfunction getWsUrl(node) {\n  const url = new URL(combineURLs(node, \"/v1/ws\"));\n  if (url.protocol === \"https:\") {\n    url.protocol = \"wss:\";\n  } else if (url.protocol === \"http:\") {\n    url.protocol = \"ws:\";\n  }\n  return url.toString();\n}\n\nconst httpTransport = {\n  send,\n  subscribe\n};\n\nexport { HTTPRequestError, WebsocketError, connectSubscribeEvents, httpTransport, send, sendExecuteScript, sendGetAccount, sendGetBlock, sendGetBlockHeader, sendGetCollection, sendGetEvents, sendGetNetworkParameters, sendGetNodeVersionInfo, sendGetTransaction, sendGetTransactionStatus, sendPing, sendTransaction };\n//# sourceMappingURL=index.module.js.map\n", "import * as logger from '@onflow/util-logger';\nimport { log, LEVELS } from '@onflow/util-logger';\nimport { invariant as invariant$1 } from '@onflow/util-invariant';\nimport { v4 } from 'uuid';\nimport { InteractionResolverKind, InteractionTag, InteractionStatus, TransactionRole } from '@onflow/typedefs';\nexport * from '@onflow/typedefs';\nimport { config } from '@onflow/config';\nexport { config } from '@onflow/config';\nimport { Buffer, encode } from '@onflow/rlp';\nimport { send as send$1, httpTransport } from '@onflow/transport-http';\nimport { sansPrefix, withPrefix } from '@onflow/util-address';\nimport EventEmitter from 'events';\nimport { SHA3 } from 'sha3';\nimport { template } from '@onflow/util-template';\nexport { template as cadence, template as cdc } from '@onflow/util-template';\nimport * as types from '@onflow/types';\nexport { types as t };\n\nconst ACCT = `{\n  \"kind\":\"${InteractionResolverKind.ACCOUNT}\",\n  \"tempId\":null,\n  \"addr\":null,\n  \"keyId\":null,\n  \"sequenceNum\":null,\n  \"signature\":null,\n  \"signingFunction\":null,\n  \"resolve\":null,\n  \"role\": {\n    \"proposer\":false,\n    \"authorizer\":false,\n    \"payer\":false,\n    \"param\":false\n  }\n}`;\nconst ARG = `{\n  \"kind\":\"${InteractionResolverKind.ARGUMENT}\",\n  \"tempId\":null,\n  \"value\":null,\n  \"asArgument\":null,\n  \"xform\":null,\n  \"resolve\": null,\n  \"resolveArgument\": null\n}`;\nconst IX = `{\n  \"tag\":\"${InteractionTag.UNKNOWN}\",\n  \"assigns\":{},\n  \"status\":\"${InteractionStatus.OK}\",\n  \"reason\":null,\n  \"accounts\":{},\n  \"params\":{},\n  \"arguments\":{},\n  \"message\": {\n    \"cadence\":null,\n    \"refBlock\":null,\n    \"computeLimit\":null,\n    \"proposer\":null,\n    \"payer\":null,\n    \"authorizations\":[],\n    \"params\":[],\n    \"arguments\":[]\n  },\n  \"proposer\":null,\n  \"authorizations\":[],\n  \"payer\":[],\n  \"events\": {\n    \"eventType\":null,\n    \"start\":null,\n    \"end\":null,\n    \"blockIds\":[]\n  },\n  \"subscribeEvents\": {\n    \"startBlockId\":null,\n    \"startHeight\":null,\n    \"eventTypes\":null,\n    \"addresses\":null,\n    \"contracts\":null,\n    \"heartbeatInterval\":null\n  },\n  \"transaction\": {\n    \"id\":null\n  },\n  \"block\": {\n    \"id\":null,\n    \"height\":null,\n    \"isSealed\":null\n  },\n  \"account\": {\n    \"addr\":null\n  },\n  \"collection\": {\n    \"id\":null\n  }\n}`;\nconst KEYS = new Set(Object.keys(JSON.parse(IX)));\nconst initInteraction = () => JSON.parse(IX);\n/**\n * @deprecated\n */\nconst interaction = () => {\n  log.deprecate({\n    pkg: \"FCL/SDK\",\n    message: `The interaction been deprecated from the Flow JS-SDK/FCL. use initInteraction instead`,\n    transition: \"https://github.com/onflow/flow-js-sdk/blob/master/packages/sdk/TRANSITIONS.md#0010-deprecate-interaction\",\n    level: LEVELS.warn\n  });\n  return initInteraction();\n};\nconst isNumber$1 = d => typeof d === \"number\";\nconst isArray$1 = d => Array.isArray(d);\nconst isObj = d => d !== null && typeof d === \"object\";\nconst isNull = d => d == null;\nconst isFn$3 = d => typeof d === \"function\";\nconst isInteraction = ix => {\n  if (!isObj(ix) || isNull(ix) || isNumber$1(ix)) return false;\n  for (let key of KEYS) if (!ix.hasOwnProperty(key)) return false;\n  return true;\n};\nconst Ok = ix => {\n  ix.status = InteractionStatus.OK;\n  return ix;\n};\nconst Bad = (ix, reason) => {\n  ix.status = InteractionStatus.BAD;\n  ix.reason = reason;\n  return ix;\n};\nconst makeIx = wat => ix => {\n  ix.tag = wat;\n  return Ok(ix);\n};\nconst prepAccountKeyId = acct => {\n  if (acct.keyId == null) return acct;\n  invariant$1(!isNaN(parseInt(acct.keyId.toString())), \"account.keyId must be an integer\");\n  return {\n    ...acct,\n    keyId: parseInt(acct.keyId.toString())\n  };\n};\nconst initAccount = () => JSON.parse(ACCT);\nconst prepAccount = function (acct) {\n  let opts = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : {};\n  return ix => {\n    invariant$1(typeof acct === \"function\" || typeof acct === \"object\", \"prepAccount must be passed an authorization function or an account object\");\n    invariant$1(opts.role != null, \"Account must have a role\");\n    const ACCOUNT = initAccount();\n    const role = opts.role;\n    const tempId = v4();\n    let account = {\n      ...acct\n    };\n    if (acct.authorization && isFn$3(acct.authorization)) account = {\n      resolve: acct.authorization\n    };\n    if (!acct.authorization && isFn$3(acct)) account = {\n      resolve: acct\n    };\n    const resolve = account.resolve;\n    if (resolve) {\n      account.resolve = function (acct) {\n        for (var _len = arguments.length, rest = new Array(_len > 1 ? _len - 1 : 0), _key = 1; _key < _len; _key++) {\n          rest[_key - 1] = arguments[_key];\n        }\n        return [resolve, prepAccountKeyId].reduce(async (d, fn) => fn(await d, ...rest), acct);\n      };\n    }\n    account = prepAccountKeyId(account);\n    ix.accounts[tempId] = {\n      ...ACCOUNT,\n      tempId,\n      ...account,\n      role: {\n        ...ACCOUNT.role,\n        ...(typeof acct.role === \"object\" ? acct.role : {}),\n        ...(role ? {\n          [role]: true\n        } : {})\n      }\n    };\n    if (role === TransactionRole.AUTHORIZER) {\n      ix.authorizations.push(tempId);\n    } else if (role === TransactionRole.PAYER) {\n      ix.payer.push(tempId);\n    } else if (role) {\n      ix[role] = tempId;\n    }\n    return ix;\n  };\n};\nconst makeArgument = arg => ix => {\n  let tempId = v4();\n  ix.message.arguments.push(tempId);\n  ix.arguments[tempId] = JSON.parse(ARG);\n  ix.arguments[tempId].tempId = tempId;\n  ix.arguments[tempId].value = arg.value;\n  ix.arguments[tempId].asArgument = arg.asArgument;\n  ix.arguments[tempId].xform = arg.xform;\n  ix.arguments[tempId].resolve = arg.resolve;\n  ix.arguments[tempId].resolveArgument = isFn$3(arg.resolveArgument) ? arg.resolveArgument.bind(arg) : arg.resolveArgument;\n  return Ok(ix);\n};\nconst makeUnknown /*                 */ = makeIx(InteractionTag.UNKNOWN);\nconst makeScript /*                  */ = makeIx(InteractionTag.SCRIPT);\nconst makeTransaction /*             */ = makeIx(InteractionTag.TRANSACTION);\nconst makeGetTransactionStatus /*    */ = makeIx(InteractionTag.GET_TRANSACTION_STATUS);\nconst makeGetTransaction /*          */ = makeIx(InteractionTag.GET_TRANSACTION);\nconst makeGetAccount /*              */ = makeIx(InteractionTag.GET_ACCOUNT);\nconst makeGetEvents /*               */ = makeIx(InteractionTag.GET_EVENTS);\nconst makePing /*                    */ = makeIx(InteractionTag.PING);\nconst makeGetBlock /*                */ = makeIx(InteractionTag.GET_BLOCK);\nconst makeGetBlockHeader /*          */ = makeIx(InteractionTag.GET_BLOCK_HEADER);\nconst makeGetCollection /*           */ = makeIx(InteractionTag.GET_COLLECTION);\nconst makeGetNetworkParameters /*    */ = makeIx(InteractionTag.GET_NETWORK_PARAMETERS);\nconst makeSubscribeEvents /*         */ = makeIx(InteractionTag.SUBSCRIBE_EVENTS);\nconst makeGetNodeVerionInfo /*       */ = makeIx(InteractionTag.GET_NODE_VERSION_INFO);\nconst is = wat => ix => ix.tag === wat;\nconst isUnknown /*                 */ = is(InteractionTag.UNKNOWN);\nconst isScript /*                  */ = is(InteractionTag.SCRIPT);\nconst isTransaction /*             */ = is(InteractionTag.TRANSACTION);\nconst isGetTransactionStatus /*    */ = is(InteractionTag.GET_TRANSACTION_STATUS);\nconst isGetTransaction /*          */ = is(InteractionTag.GET_TRANSACTION);\nconst isGetAccount /*              */ = is(InteractionTag.GET_ACCOUNT);\nconst isGetEvents /*               */ = is(InteractionTag.GET_EVENTS);\nconst isPing /*                    */ = is(InteractionTag.PING);\nconst isGetBlock /*                */ = is(InteractionTag.GET_BLOCK);\nconst isGetBlockHeader /*          */ = is(InteractionTag.GET_BLOCK_HEADER);\nconst isGetCollection /*           */ = is(InteractionTag.GET_COLLECTION);\nconst isGetNetworkParameters /*    */ = is(InteractionTag.GET_NETWORK_PARAMETERS);\nconst isGetNodeVersionInfo /*      */ = is(InteractionTag.GET_NODE_VERSION_INFO);\nconst isSubscribeEvents /*         */ = is(InteractionTag.SUBSCRIBE_EVENTS);\nconst isOk /*  */ = ix => ix.status === InteractionStatus.OK;\nconst isBad /* */ = ix => ix.status === InteractionStatus.BAD;\nconst why /*   */ = ix => ix.reason;\nconst isAccount /*  */ = account => account.kind === InteractionResolverKind.ACCOUNT;\nconst isArgument /* */ = argument => argument.kind === InteractionResolverKind.ARGUMENT;\nconst hardMode = ix => {\n  for (let key of Object.keys(ix)) {\n    if (!KEYS.has(key)) throw new Error(`\"${key}\" is an invalid root level Interaction property.`);\n  }\n  return ix;\n};\nconst recPipe = async function (ix) {\n  let fns = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : [];\n  try {\n    ix = hardMode(await ix);\n    if (isBad(ix)) throw new Error(`Interaction Error: ${ix.reason}`);\n    if (!fns.length) return ix;\n    const [hd, ...rest] = fns;\n    const cur = await hd;\n    if (isFn$3(cur)) return recPipe(cur(ix), rest);\n    if (isNull(cur) || !cur) return recPipe(ix, rest);\n    if (isInteraction(cur)) return recPipe(cur, rest);\n    throw new Error(\"Invalid Interaction Composition\");\n  } catch (e) {\n    throw e;\n  }\n};\n\n/**\n * @description Async pipe function to compose interactions\n * @returns An interaction object\n */\n\nfunction pipe() {\n  for (var _len2 = arguments.length, args = new Array(_len2), _key2 = 0; _key2 < _len2; _key2++) {\n    args[_key2] = arguments[_key2];\n  }\n  const [arg1, arg2] = args;\n  if (isArray$1(arg1)) return d => pipe(d, arg1);\n  const ix = arg1;\n  const fns = arg2;\n  return recPipe(ix, fns);\n}\nconst identity$1 = function (v) {\n  return v;\n};\nconst get = function (ix, key) {\n  let fallback = arguments.length > 2 && arguments[2] !== undefined ? arguments[2] : undefined;\n  return ix.assigns[key] == null ? fallback : ix.assigns[key];\n};\nconst put = (key, value) => ix => {\n  ix.assigns[key] = value;\n  return Ok(ix);\n};\nconst update = function (key) {\n  let fn = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : identity$1;\n  return ix => {\n    ix.assigns[key] = fn(ix.assigns[key], ix);\n    return Ok(ix);\n  };\n};\nconst destroy = key => ix => {\n  delete ix.assigns[key];\n  return Ok(ix);\n};\n\nvar ixModule = /*#__PURE__*/Object.freeze({\n  __proto__: null,\n  Bad: Bad,\n  Ok: Ok,\n  destroy: destroy,\n  get: get,\n  initAccount: initAccount,\n  initInteraction: initInteraction,\n  interaction: interaction,\n  isAccount: isAccount,\n  isArgument: isArgument,\n  isArray: isArray$1,\n  isBad: isBad,\n  isFn: isFn$3,\n  isGetAccount: isGetAccount,\n  isGetBlock: isGetBlock,\n  isGetBlockHeader: isGetBlockHeader,\n  isGetCollection: isGetCollection,\n  isGetEvents: isGetEvents,\n  isGetNetworkParameters: isGetNetworkParameters,\n  isGetNodeVersionInfo: isGetNodeVersionInfo,\n  isGetTransaction: isGetTransaction,\n  isGetTransactionStatus: isGetTransactionStatus,\n  isInteraction: isInteraction,\n  isNull: isNull,\n  isNumber: isNumber$1,\n  isObj: isObj,\n  isOk: isOk,\n  isPing: isPing,\n  isScript: isScript,\n  isSubscribeEvents: isSubscribeEvents,\n  isTransaction: isTransaction,\n  isUnknown: isUnknown,\n  makeArgument: makeArgument,\n  makeGetAccount: makeGetAccount,\n  makeGetBlock: makeGetBlock,\n  makeGetBlockHeader: makeGetBlockHeader,\n  makeGetCollection: makeGetCollection,\n  makeGetEvents: makeGetEvents,\n  makeGetNetworkParameters: makeGetNetworkParameters,\n  makeGetNodeVerionInfo: makeGetNodeVerionInfo,\n  makeGetTransaction: makeGetTransaction,\n  makeGetTransactionStatus: makeGetTransactionStatus,\n  makePing: makePing,\n  makeScript: makeScript,\n  makeSubscribeEvents: makeSubscribeEvents,\n  makeTransaction: makeTransaction,\n  makeUnknown: makeUnknown,\n  pipe: pipe,\n  prepAccount: prepAccount,\n  put: put,\n  update: update,\n  why: why\n});\n\n/**\n * @description A builder function that creates an interaction\n * @param fns The functions to apply to the interaction\n * @returns A promise of an interaction\n */\nfunction build() {\n  let fns = arguments.length > 0 && arguments[0] !== undefined ? arguments[0] : [];\n  return pipe(initInteraction(), fns);\n}\n\nconst DEFAULT_RESPONSE = {\n  tag: null,\n  transaction: null,\n  transactionStatus: null,\n  transactionId: null,\n  encodedData: null,\n  events: null,\n  event: null,\n  accountStatusEvent: null,\n  account: null,\n  block: null,\n  blockHeader: null,\n  blockDigest: null,\n  latestBlock: null,\n  collection: null,\n  networkParameters: null,\n  streamConnection: null,\n  heartbeat: null,\n  nodeVersionInfo: null\n};\nconst response = () => ({\n  ...DEFAULT_RESPONSE\n});\n\n/**\n * @description A builder function that returns the interaction to get the latest block\n * @param isSealed Whether or not the block should be sealed\n * @returns A function that processes an interaction object\n */\nfunction getBlock() {\n  let isSealed = arguments.length > 0 && arguments[0] !== undefined ? arguments[0] : null;\n  return pipe([makeGetBlock, ix => {\n    ix.block.isSealed = isSealed;\n    return Ok(ix);\n  }]);\n}\n\n/**\n * @description A builder function that returns the interaction to get an account by address\n * @param addr The address of the account to get\n * @returns A function that processes an interaction object\n */\nfunction getAccount(addr) {\n  return pipe([makeGetAccount, ix => {\n    ix.account.addr = sansPrefix(addr);\n    return Ok(ix);\n  }]);\n}\n\n/**\n * Pipes a generic stream of data into a granular stream of decoded data\n * The data is decoded per channel and emitted in order\n */\nconst decodeStream = (stream, decodeResponse, customDecoders) => {\n  const newStream = new EventEmitter();\n  let queue = taskQueue();\n\n  // Data is separated by topic & the decoded data is emitted in order\n  // All topics for a given message will be emitted synchronously before moving on to the next message\n  // The streamReady promise ensures that the data is emitted in order and avoids race conditions when decoding\n  stream.on(\"data\", async data => {\n    const topics = Object.keys(data).filter(key => data[key] != null && key !== \"tag\");\n    let newDataPromise = Promise.all(topics.map(async channel => {\n      const partialResponse = {\n        [channel]: data[channel]\n      };\n      const message = await decodeResponse(partialResponse, customDecoders);\n      return {\n        channel,\n        message\n      };\n    }));\n    queue.push(async () => {\n      // Emit the new data\n      const newData = await newDataPromise;\n      newData.forEach(_ref => {\n        let {\n          channel,\n          message\n        } = _ref;\n        newStream.emit(channel, message);\n      });\n    });\n  });\n\n  // Relay events from the original stream\n  // These events are delivered in order as well so that the stream will\n  // not emit more data after it has announced a contradictory state\n  function relayEvent(event) {\n    stream.on(event, message => {\n      queue.push(async () => {\n        newStream.emit(event, message);\n      });\n    });\n  }\n  relayEvent(\"close\");\n  relayEvent(\"error\");\n  return {\n    on(channel, callback) {\n      newStream.on(channel, callback);\n      return this;\n    },\n    off(channel, callback) {\n      newStream.off(channel, callback);\n      return this;\n    },\n    close: () => {\n      stream.close();\n    }\n  };\n};\nfunction taskQueue() {\n  let queue = [];\n  let running = false;\n  async function run() {\n    if (running) return;\n    running = true;\n    while (queue.length > 0) {\n      const task = queue.shift();\n      await task?.();\n    }\n    running = false;\n  }\n  return {\n    push: task => {\n      queue.push(task);\n      run();\n    }\n  };\n}\n\nconst latestBlockDeprecationNotice = () => {\n  log.deprecate({\n    pkg: \"@onflow/decode\",\n    subject: \"Operating upon data of the latestBlock field of the response object\",\n    transition: \"https://github.com/onflow/flow-js-sdk/blob/master/packages/decode/WARNINGS.md#0001-Deprecating-latestBlock-field\"\n  });\n};\nconst decodeImplicit = async i => i;\nconst decodeVoid = async () => null;\nconst decodeType = async type => {\n  return type.staticType;\n};\nconst decodeOptional = async (optional, decoders, stack) => optional ? await recurseDecode(optional, decoders, stack) : null;\nconst decodeArray = async (array, decoders, stack) => await Promise.all(array.map(v => new Promise(async res => res(await recurseDecode(v, decoders, [...stack, v.type])))));\nconst decodeDictionary = async (dictionary, decoders, stack) => await dictionary.reduce(async (acc, v) => {\n  acc = await acc;\n  acc[await recurseDecode(v.key, decoders, [...stack, v.key])] = await recurseDecode(v.value, decoders, [...stack, v.key]);\n  return acc;\n}, Promise.resolve({}));\nconst decodeComposite = async (composite, decoders, stack) => {\n  const decoded = await composite.fields.reduce(async (acc, v) => {\n    acc = await acc;\n    acc[v.name] = await recurseDecode(v.value, decoders, [...stack, v.name]);\n    return acc;\n  }, Promise.resolve({}));\n  const decoder = composite.id && decoderLookup(decoders, composite.id);\n  return decoder ? await decoder(decoded) : decoded;\n};\nconst decodeInclusiveRange = async (range, decoders, stack) => {\n  // Recursive decode for start, end, and step\n  // We don't do all fields just in case there are future API changes\n  // where fields added and are not Cadence values\n  const keys = [\"start\", \"end\", \"step\"];\n  const decoded = await Object.keys(range).reduce(async (acc, key) => {\n    acc = await acc;\n    if (keys.includes(key)) {\n      acc[key] = await recurseDecode(range[key], decoders, [...stack, key]);\n    }\n    return acc;\n  }, Promise.resolve({}));\n  return decoded;\n};\nconst defaultDecoders = {\n  UInt: decodeImplicit,\n  Int: decodeImplicit,\n  UInt8: decodeImplicit,\n  Int8: decodeImplicit,\n  UInt16: decodeImplicit,\n  Int16: decodeImplicit,\n  UInt32: decodeImplicit,\n  Int32: decodeImplicit,\n  UInt64: decodeImplicit,\n  Int64: decodeImplicit,\n  UInt128: decodeImplicit,\n  Int128: decodeImplicit,\n  UInt256: decodeImplicit,\n  Int256: decodeImplicit,\n  Word8: decodeImplicit,\n  Word16: decodeImplicit,\n  Word32: decodeImplicit,\n  Word64: decodeImplicit,\n  Word128: decodeImplicit,\n  Word256: decodeImplicit,\n  UFix64: decodeImplicit,\n  Fix64: decodeImplicit,\n  String: decodeImplicit,\n  Character: decodeImplicit,\n  Bool: decodeImplicit,\n  Address: decodeImplicit,\n  Void: decodeVoid,\n  Optional: decodeOptional,\n  Reference: decodeImplicit,\n  Array: decodeArray,\n  Dictionary: decodeDictionary,\n  Event: decodeComposite,\n  Resource: decodeComposite,\n  Struct: decodeComposite,\n  Enum: decodeComposite,\n  Type: decodeType,\n  Path: decodeImplicit,\n  Capability: decodeImplicit,\n  InclusiveRange: decodeInclusiveRange\n};\nconst decoderLookup = (decoders, lookup) => {\n  const found = Object.keys(decoders).find(decoder => {\n    if (/^\\/.*\\/$/.test(decoder)) {\n      const reg = new RegExp(decoder.substring(1, decoder.length - 1));\n      return reg.test(lookup);\n    }\n    return decoder === lookup;\n  });\n  return lookup && found && decoders[found];\n};\nconst recurseDecode = async (decodeInstructions, decoders, stack) => {\n  let decoder = decoderLookup(decoders, decodeInstructions.type);\n  if (!decoder) throw new Error(`Undefined Decoder Error: ${decodeInstructions.type}@${stack.join(\".\")}`);\n  return await decoder(decodeInstructions.value, decoders, stack);\n};\n\n/**\n * @description - Decodes a response from Flow into JSON\n * @param decodeInstructions - The response object from Flow\n * @param customDecoders - An object of custom decoders\n * @param stack - The stack of the current decoding\n * @returns - The decoded response\n */\nconst decode$1 = async function (decodeInstructions) {\n  let customDecoders = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : {};\n  let stack = arguments.length > 2 && arguments[2] !== undefined ? arguments[2] : [];\n  // Filter out all default decoders which are overridden by a custom decoder regex\n  const filteredDecoders = Object.keys(defaultDecoders).filter(decoder => !Object.keys(customDecoders).find(customDecoder => new RegExp(customDecoder).test(decoder))).reduce((decoders, decoderKey) => {\n    decoders[decoderKey] = defaultDecoders[decoderKey];\n    return decoders;\n  }, customDecoders);\n  const decoders = {\n    ...filteredDecoders,\n    ...customDecoders\n  };\n  return recurseDecode(decodeInstructions, decoders, stack);\n};\nconst decodeResponse = async function (response) {\n  let customDecoders = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : {};\n  if (response.encodedData) {\n    return decode$1(response.encodedData, customDecoders);\n  } else if (response.transactionStatus) {\n    return {\n      ...response.transactionStatus,\n      events: await Promise.all(response.transactionStatus.events.map(async function decodeEvents(e) {\n        return {\n          type: e.type,\n          transactionId: e.transactionId,\n          transactionIndex: e.transactionIndex,\n          eventIndex: e.eventIndex,\n          data: await decode$1(e.payload, customDecoders)\n        };\n      }))\n    };\n  } else if (response.transaction) {\n    return response.transaction;\n  } else if (response.events) {\n    return await Promise.all(response.events.map(async function decodeEvents(e) {\n      return {\n        blockId: e.blockId,\n        blockHeight: e.blockHeight,\n        blockTimestamp: e.blockTimestamp,\n        type: e.type,\n        transactionId: e.transactionId,\n        transactionIndex: e.transactionIndex,\n        eventIndex: e.eventIndex,\n        data: await decode$1(e.payload, customDecoders)\n      };\n    }));\n  } else if (response.event) {\n    const {\n      payload,\n      ...rest\n    } = response.event;\n    return {\n      ...rest,\n      data: await decode$1(payload, customDecoders)\n    };\n  } else if (response.accountStatusEvent) {\n    const {\n      payload,\n      ...rest\n    } = response.accountStatusEvent;\n    return {\n      ...rest,\n      data: await decode$1(payload, customDecoders)\n    };\n  } else if (response.account) {\n    return response.account;\n  } else if (response.block) {\n    return response.block;\n  } else if (response.blockHeader) {\n    return response.blockHeader;\n  } else if (response.blockDigest) {\n    return response.blockDigest;\n  } else if (response.latestBlock) {\n    latestBlockDeprecationNotice();\n    return response.latestBlock;\n  } else if (response.transactionId) {\n    return response.transactionId;\n  } else if (response.collection) {\n    return response.collection;\n  } else if (response.networkParameters) {\n    const prefixRegex = /^flow-/;\n    const rawChainId = response.networkParameters.chainId;\n    let formattedChainId;\n    if (rawChainId === \"flow-emulator\") {\n      formattedChainId = \"local\";\n    } else if (prefixRegex.test(rawChainId)) {\n      formattedChainId = rawChainId.replace(prefixRegex, \"\");\n    } else {\n      formattedChainId = rawChainId;\n    }\n    return {\n      chainId: formattedChainId\n    };\n  } else if (response.streamConnection) {\n    return decodeStream(response.streamConnection, decodeResponse, customDecoders);\n  } else if (response.heartbeat) {\n    return response.heartbeat;\n  } else if (response.nodeVersionInfo) {\n    return response.nodeVersionInfo;\n  }\n  return null;\n};\n\nconst isFn$2 = v => typeof v === \"function\";\nconst isString$1 = v => typeof v === \"string\";\nconst oldIdentifierPatternFn = () => /\\b(0x\\w+)\\b/g;\nfunction isOldIdentifierSyntax(cadence) {\n  return oldIdentifierPatternFn().test(cadence);\n}\nconst newIdentifierPatternFn = () => /import\\s+\"(\\w+)\"/g;\nfunction isNewIdentifierSyntax(cadence) {\n  return newIdentifierPatternFn().test(cadence);\n}\nfunction getContractIdentifierSyntaxMatches(cadence) {\n  return cadence.matchAll(newIdentifierPatternFn());\n}\nasync function resolveCadence(ix) {\n  if (!isTransaction(ix) && !isScript(ix)) return ix;\n  var cadence = get(ix, \"ix.cadence\");\n  invariant$1(isFn$2(cadence) || isString$1(cadence), \"Cadence needs to be a function or a string.\");\n  if (isFn$2(cadence)) cadence = await cadence({});\n  invariant$1(isString$1(cadence), \"Cadence needs to be a string at this point.\");\n  invariant$1(!isOldIdentifierSyntax(cadence) || !isNewIdentifierSyntax(cadence), \"Both account identifier and contract identifier syntax not simultaneously supported.\");\n  if (isOldIdentifierSyntax(cadence)) {\n    cadence = await config().where(/^0x/).then(d => Object.entries(d).reduce((cadence, _ref) => {\n      let [key, value] = _ref;\n      const regex = new RegExp(\"(\\\\b\" + key + \"\\\\b)\", \"g\");\n      return cadence.replace(regex, value);\n    }, cadence));\n  }\n  if (isNewIdentifierSyntax(cadence)) {\n    for (const [fullMatch, contractName] of getContractIdentifierSyntaxMatches(cadence)) {\n      const address = await config().get(`system.contracts.${contractName}`);\n      if (address) {\n        cadence = cadence.replace(fullMatch, `import ${contractName} from ${withPrefix(address)}`);\n      } else {\n        logger.log({\n          title: \"Contract Placeholder not found\",\n          message: `Cannot find a value for contract placeholder ${contractName}. Please add to your flow.json or explicitly add it to the config 'contracts.*' namespace.`,\n          level: logger.LEVELS.warn\n        });\n      }\n    }\n  }\n\n  // We need to move this over in any case.\n  ix.message.cadence = cadence;\n  return ix;\n}\n\nconst isFn$1 = v => typeof v === \"function\";\nfunction cast(arg) {\n  // prettier-ignore\n  invariant$1(typeof arg.xform != null, `No type specified for argument: ${arg.value}`);\n  if (isFn$1(arg.xform)) return arg.xform(arg.value);\n  if (isFn$1(arg.xform.asArgument)) return arg.xform.asArgument(arg.value);\n\n  // prettier-ignore\n  invariant$1(false, `Invalid Argument`, arg);\n}\nasync function handleArgResolution(arg) {\n  let depth = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : 3;\n  invariant$1(depth > 0, `Argument Resolve Recursion Limit Exceeded for Arg: ${arg.tempId}`);\n  if (isFn$1(arg.resolveArgument)) {\n    const resolvedArg = await arg.resolveArgument();\n    return handleArgResolution(resolvedArg, depth - 1);\n  } else {\n    return arg;\n  }\n}\nasync function resolveArguments(ix) {\n  if (isTransaction(ix) || isScript(ix)) {\n    for (let [id, arg] of Object.entries(ix.arguments)) {\n      const res = await handleArgResolution(arg);\n      ix.arguments[id].asArgument = cast(res);\n    }\n  }\n  return ix;\n}\n\nconst encodeTransactionPayload = tx => prependTransactionDomainTag(rlpEncode(preparePayload(tx)));\nconst encodeTransactionEnvelope = tx => prependTransactionDomainTag(rlpEncode(prepareEnvelope(tx)));\nconst encodeTxIdFromVoucher = voucher => sha3_256(rlpEncode(prepareVoucher(voucher)));\nconst rightPaddedHexBuffer = (value, pad) => Buffer.from(value.padEnd(pad * 2, \"0\"), \"hex\");\nconst leftPaddedHexBuffer = (value, pad) => Buffer.from(value.padStart(pad * 2, \"0\"), \"hex\");\nconst TRANSACTION_DOMAIN_TAG = rightPaddedHexBuffer(Buffer.from(\"FLOW-V0.0-transaction\").toString(\"hex\"), 32).toString(\"hex\");\nconst prependTransactionDomainTag = tx => TRANSACTION_DOMAIN_TAG + tx;\nconst addressBuffer = addr => leftPaddedHexBuffer(addr, 8);\nconst blockBuffer = block => leftPaddedHexBuffer(block, 32);\nconst argumentToString = arg => Buffer.from(JSON.stringify(arg), \"utf8\");\nconst scriptBuffer = script => Buffer.from(script, \"utf8\");\nconst signatureBuffer = signature => Buffer.from(signature, \"hex\");\nconst rlpEncode = v => {\n  return encode(v).toString(\"hex\");\n};\nconst sha3_256 = msg => {\n  const sha = new SHA3(256);\n  sha.update(Buffer.from(msg, \"hex\"));\n  return sha.digest().toString(\"hex\");\n};\nconst preparePayload = tx => {\n  validatePayload(tx);\n  return [scriptBuffer(tx.cadence || \"\"), tx.arguments.map(argumentToString), blockBuffer(tx.refBlock || \"\"), tx.computeLimit, addressBuffer(sansPrefix(tx.proposalKey.address || \"\")), tx.proposalKey.keyId, tx.proposalKey.sequenceNum, addressBuffer(sansPrefix(tx.payer)), tx.authorizers.map(authorizer => addressBuffer(sansPrefix(authorizer)))];\n};\nconst prepareEnvelope = tx => {\n  validateEnvelope(tx);\n  return [preparePayload(tx), preparePayloadSignatures(tx)];\n};\nconst preparePayloadSignatures = tx => {\n  const signers = collectSigners(tx);\n  return tx.payloadSigs?.map(sig => {\n    return {\n      signerIndex: signers.get(sansPrefix(sig.address)) || \"\",\n      keyId: sig.keyId,\n      sig: sig.sig\n    };\n  }).sort((a, b) => {\n    if (a.signerIndex > b.signerIndex) return 1;\n    if (a.signerIndex < b.signerIndex) return -1;\n    if (a.keyId > b.keyId) return 1;\n    if (a.keyId < b.keyId) return -1;\n    return 0;\n  }).map(sig => {\n    return [sig.signerIndex, sig.keyId, signatureBuffer(sig.sig)];\n  });\n};\nconst collectSigners = tx => {\n  const signers = new Map();\n  let i = 0;\n  const addSigner = addr => {\n    if (!signers.has(addr)) {\n      signers.set(addr, i);\n      i++;\n    }\n  };\n  if (tx.proposalKey.address) {\n    addSigner(tx.proposalKey.address);\n  }\n  addSigner(tx.payer);\n  tx.authorizers.forEach(addSigner);\n  return signers;\n};\nconst prepareVoucher = voucher => {\n  validateVoucher(voucher);\n  const signers = collectSigners(voucher);\n  const prepareSigs = sigs => {\n    return sigs.map(_ref => {\n      let {\n        address,\n        keyId,\n        sig\n      } = _ref;\n      return {\n        signerIndex: signers.get(sansPrefix(address)) || \"\",\n        keyId,\n        sig\n      };\n    }).sort((a, b) => {\n      if (a.signerIndex > b.signerIndex) return 1;\n      if (a.signerIndex < b.signerIndex) return -1;\n      if (a.keyId > b.keyId) return 1;\n      if (a.keyId < b.keyId) return -1;\n      return 0;\n    }).map(sig => {\n      return [sig.signerIndex, sig.keyId, signatureBuffer(sig.sig)];\n    });\n  };\n  return [[scriptBuffer(voucher.cadence), voucher.arguments.map(argumentToString), blockBuffer(voucher.refBlock), voucher.computeLimit, addressBuffer(sansPrefix(voucher.proposalKey.address)), voucher.proposalKey.keyId, voucher.proposalKey.sequenceNum, addressBuffer(sansPrefix(voucher.payer)), voucher.authorizers.map(authorizer => addressBuffer(sansPrefix(authorizer)))], prepareSigs(voucher.payloadSigs), prepareSigs(voucher.envelopeSigs)];\n};\nconst validatePayload = tx => {\n  payloadFields.forEach(field => checkField(tx, field));\n  proposalKeyFields.forEach(field => checkField(tx.proposalKey, field, \"proposalKey\"));\n};\nconst validateEnvelope = tx => {\n  payloadSigsFields.forEach(field => checkField(tx, field));\n  tx.payloadSigs?.forEach((sig, index) => {\n    payloadSigFields.forEach(field => checkField(sig, field, \"payloadSigs\", index));\n  });\n};\nconst validateVoucher = voucher => {\n  payloadFields.forEach(field => checkField(voucher, field));\n  proposalKeyFields.forEach(field => checkField(voucher.proposalKey, field, \"proposalKey\"));\n  payloadSigsFields.forEach(field => checkField(voucher, field));\n  voucher.payloadSigs.forEach((sig, index) => {\n    payloadSigFields.forEach(field => checkField(sig, field, \"payloadSigs\", index));\n  });\n  envelopeSigsFields.forEach(field => checkField(voucher, field));\n  voucher.envelopeSigs.forEach((sig, index) => {\n    envelopeSigFields.forEach(field => checkField(sig, field, \"envelopeSigs\", index));\n  });\n};\nconst isNumber = v => typeof v === \"number\";\nconst isString = v => typeof v === \"string\";\nconst isObject = v => v !== null && typeof v === \"object\";\nconst isArray = v => isObject(v) && v instanceof Array;\nconst payloadFields = [{\n  name: \"cadence\",\n  check: isString\n}, {\n  name: \"arguments\",\n  check: isArray\n}, {\n  name: \"refBlock\",\n  check: isString,\n  defaultVal: \"0\"\n}, {\n  name: \"computeLimit\",\n  check: isNumber\n}, {\n  name: \"proposalKey\",\n  check: isObject\n}, {\n  name: \"payer\",\n  check: isString\n}, {\n  name: \"authorizers\",\n  check: isArray\n}];\nconst proposalKeyFields = [{\n  name: \"address\",\n  check: isString\n}, {\n  name: \"keyId\",\n  check: isNumber\n}, {\n  name: \"sequenceNum\",\n  check: isNumber\n}];\nconst payloadSigsFields = [{\n  name: \"payloadSigs\",\n  check: isArray\n}];\nconst payloadSigFields = [{\n  name: \"address\",\n  check: isString\n}, {\n  name: \"keyId\",\n  check: isNumber\n}, {\n  name: \"sig\",\n  check: isString\n}];\nconst envelopeSigsFields = [{\n  name: \"envelopeSigs\",\n  check: isArray\n}];\nconst envelopeSigFields = [{\n  name: \"address\",\n  check: isString\n}, {\n  name: \"keyId\",\n  check: isNumber\n}, {\n  name: \"sig\",\n  check: isString\n}];\nconst checkField = (obj, field, base, index) => {\n  const {\n    name,\n    check,\n    defaultVal\n  } = field;\n  if (obj[name] == null && defaultVal != null) obj[name] = defaultVal;\n  if (obj[name] == null) throw missingFieldError(name, base, index);\n  if (!check(obj[name])) throw invalidFieldError(name, base, index);\n};\nconst printFieldName = (field, base, index) => {\n  if (!!base) return index == null ? `${base}.${field}` : `${base}.${index}.${field}`;\n  return field;\n};\nconst missingFieldError = (field, base, index) => new Error(`Missing field ${printFieldName(field, base, index)}`);\nconst invalidFieldError = (field, base, index) => new Error(`Invalid field ${printFieldName(field, base, index)}`);\n\nfunction findInsideSigners(ix) {\n  // Inside Signers Are: (authorizers + proposer) - payer\n  let inside = new Set(ix.authorizations);\n  if (ix.proposer) {\n    inside.add(ix.proposer);\n  }\n  if (Array.isArray(ix.payer)) {\n    ix.payer.forEach(p => inside.delete(p));\n  } else {\n    inside.delete(ix.payer);\n  }\n  return Array.from(inside);\n}\nfunction findOutsideSigners(ix) {\n  // Outside Signers Are: (payer)\n  let outside = new Set(Array.isArray(ix.payer) ? ix.payer : [ix.payer]);\n  return Array.from(outside);\n}\nconst createSignableVoucher = ix => {\n  const buildAuthorizers = () => {\n    const authorizations = ix.authorizations.map(cid => withPrefix(ix.accounts[cid].addr)).reduce((prev, current) => {\n      return prev.find(item => item === current) ? prev : [...prev, current];\n    }, []);\n    return authorizations;\n  };\n  const buildInsideSigners = () => findInsideSigners(ix).map(id => ({\n    address: withPrefix(ix.accounts[id].addr),\n    keyId: ix.accounts[id].keyId,\n    sig: ix.accounts[id].signature\n  }));\n  const buildOutsideSigners = () => findOutsideSigners(ix).map(id => ({\n    address: withPrefix(ix.accounts[id].addr),\n    keyId: ix.accounts[id].keyId,\n    sig: ix.accounts[id].signature\n  }));\n  const proposalKey = ix.proposer ? {\n    address: withPrefix(ix.accounts[ix.proposer].addr),\n    keyId: ix.accounts[ix.proposer].keyId,\n    sequenceNum: ix.accounts[ix.proposer].sequenceNum\n  } : {};\n  return {\n    cadence: ix.message.cadence,\n    refBlock: ix.message.refBlock || null,\n    computeLimit: ix.message.computeLimit,\n    arguments: ix.message.arguments.map(id => ix.arguments[id].asArgument),\n    proposalKey,\n    payer: withPrefix(ix.accounts[Array.isArray(ix.payer) ? ix.payer[0] : ix.payer].addr),\n    authorizers: buildAuthorizers(),\n    payloadSigs: buildInsideSigners(),\n    envelopeSigs: buildOutsideSigners()\n  };\n};\nconst voucherToTxId = voucher => {\n  return encodeTxIdFromVoucher(voucher);\n};\n\nconst MAX_DEPTH_LIMIT = 5;\nconst idof$1 = acct => `${withPrefix(acct.addr)}-${acct.keyId}`;\nconst isFn = v => v && (Object.prototype.toString.call(v) === \"[object Function]\" || \"function\" === typeof v || v instanceof Function);\nconst genAccountId = function () {\n  for (var _len = arguments.length, ids = new Array(_len), _key = 0; _key < _len; _key++) {\n    ids[_key] = arguments[_key];\n  }\n  return ids.join(\"-\");\n};\nvar ROLES = /*#__PURE__*/function (ROLES) {\n  ROLES[\"PAYER\"] = \"payer\";\n  ROLES[\"PROPOSER\"] = \"proposer\";\n  ROLES[\"AUTHORIZATIONS\"] = \"authorizations\";\n  return ROLES;\n}(ROLES || {});\nfunction debug$1() {\n  const SPACE = \" \";\n  const SPACE_COUNT_PER_INDENT = 4;\n  const DEBUG_MESSAGE = [];\n  return [function () {\n    let msg = arguments.length > 0 && arguments[0] !== undefined ? arguments[0] : \"\";\n    let indent = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : 0;\n    DEBUG_MESSAGE.push(Array(indent * SPACE_COUNT_PER_INDENT).fill(SPACE).join(\"-\") + msg);\n  }, function () {\n    return DEBUG_MESSAGE.reduce((prev, curr) => prev + \"\\n\" + curr);\n  }];\n}\nfunction recurseFlatMap(el) {\n  let depthLimit = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : 3;\n  if (depthLimit <= 0) return el;\n  if (!Array.isArray(el)) return el;\n  return recurseFlatMap(el.flatMap(e => e), depthLimit - 1);\n}\nfunction buildPreSignable(acct, ix) {\n  try {\n    return {\n      f_type: \"PreSignable\",\n      f_vsn: \"1.0.1\",\n      roles: acct.role,\n      cadence: ix.message.cadence,\n      args: ix.message.arguments.map(d => ix.arguments[d].asArgument),\n      data: {},\n      interaction: ix,\n      voucher: createSignableVoucher(ix)\n    };\n  } catch (error) {\n    console.error(\"buildPreSignable\", error);\n    throw error;\n  }\n}\nasync function removeUnusedIxAccounts(ix, opts) {\n  const payerTempIds = Array.isArray(ix.payer) ? ix.payer : [ix.payer];\n  const authorizersTempIds = Array.isArray(ix.authorizations) ? ix.authorizations : [ix.authorizations];\n  const proposerTempIds = ix.proposer === null ? [] : Array.isArray(ix.proposer) ? ix.proposer : [ix.proposer];\n  const ixAccountKeys = Object.keys(ix.accounts);\n  const uniqueTempIds = [...new Set(payerTempIds.concat(authorizersTempIds, proposerTempIds))];\n  for (const ixAccountKey of ixAccountKeys) {\n    if (!uniqueTempIds.find(id => id === ixAccountKey)) {\n      delete ix.accounts[ixAccountKey];\n    }\n  }\n}\nfunction addAccountToIx(ix, newAccount) {\n  if (typeof newAccount.addr === \"string\" && (typeof newAccount.keyId === \"number\" || typeof newAccount.keyId === \"string\")) {\n    newAccount.tempId = idof$1(newAccount);\n  } else {\n    newAccount.tempId = newAccount.tempId || v4();\n  }\n  const existingAccount = ix.accounts[newAccount.tempId] || newAccount;\n  if (!ix.accounts[newAccount.tempId]) {\n    ix.accounts[newAccount.tempId] = newAccount;\n  }\n  ix.accounts[newAccount.tempId].role.proposer = existingAccount.role.proposer || newAccount.role.proposer;\n  ix.accounts[newAccount.tempId].role.payer = existingAccount.role.payer || newAccount.role.payer;\n  ix.accounts[newAccount.tempId].role.authorizer = existingAccount.role.authorizer || newAccount.role.authorizer;\n  return ix.accounts[newAccount.tempId];\n}\nfunction uniqueAccountsFlatMap(accounts) {\n  const flatMapped = recurseFlatMap(accounts);\n  const seen = new Set();\n  const uniqueAccountsFlatMapped = flatMapped.map(account => {\n    const accountId = genAccountId(account.tempId, account.role.payer, account.role.proposer, account.role.authorizer, account.role.param);\n    if (seen.has(accountId)) return null;\n    seen.add(accountId);\n    return account;\n  }).filter(e => e !== null);\n  return uniqueAccountsFlatMapped;\n}\n\n// Resolve single account, returns new account tempIds (if they exist)\nasync function resolveSingleAccount(ix, currentAccountTempId) {\n  let depthLimit = arguments.length > 2 && arguments[2] !== undefined ? arguments[2] : MAX_DEPTH_LIMIT;\n  let {\n    debugLogger\n  } = arguments.length > 3 ? arguments[3] : undefined;\n  if (depthLimit <= 0) {\n    throw new Error(`recurseResolveAccount Error: Depth limit (${MAX_DEPTH_LIMIT}) reached. Ensure your authorization functions resolve to an account after ${MAX_DEPTH_LIMIT} resolves.`);\n  }\n  let account = ix.accounts[currentAccountTempId];\n  if (!account) return [[], false];\n  debugLogger(`account: ${account.tempId}`, Math.max(MAX_DEPTH_LIMIT - depthLimit, 0));\n  if (account?.resolve) {\n    if (isFn(account?.resolve)) {\n      debugLogger(`account: ${account.tempId} -- cache MISS`, Math.max(MAX_DEPTH_LIMIT - depthLimit, 0));\n      const {\n        resolve,\n        ...accountWithoutResolve\n      } = account;\n      let resolvedAccounts = await resolve(accountWithoutResolve, buildPreSignable(accountWithoutResolve, ix));\n      resolvedAccounts = Array.isArray(resolvedAccounts) ? resolvedAccounts : [resolvedAccounts];\n      let flatResolvedAccounts = recurseFlatMap(resolvedAccounts);\n      flatResolvedAccounts = flatResolvedAccounts.map(flatResolvedAccount => addAccountToIx(ix, flatResolvedAccount));\n      account.resolve = flatResolvedAccounts.map(flatResolvedAccount => flatResolvedAccount.tempId);\n      account = addAccountToIx(ix, account);\n      return [flatResolvedAccounts.map(flatResolvedAccount => flatResolvedAccount.tempId), true];\n    } else {\n      debugLogger(`account: ${account.tempId} -- cache HIT`, Math.max(MAX_DEPTH_LIMIT - depthLimit, 0));\n      return [account.resolve, false];\n    }\n  }\n  return [account.tempId ? [account.tempId] : [], false];\n}\nconst getAccountTempIDs = rawTempIds => {\n  if (rawTempIds === null) {\n    return [];\n  }\n  return Array.isArray(rawTempIds) ? rawTempIds : [rawTempIds];\n};\nasync function replaceRoles(ix, oldAccountTempId, newAccounts) {\n  // Replace roles in the interaction with any resolved accounts\n  // e.g. payer -> [oldAccountTempId, anotherId] => payer -> [newAccountTempId, anotherId]\n  for (let role of Object.values(ROLES)) {\n    if (role === ROLES.AUTHORIZATIONS || role === ROLES.PAYER) {\n      ix[role] = getAccountTempIDs(ix[role]).reduce((acc, acctTempId) => {\n        if (acctTempId === oldAccountTempId) {\n          return acc.concat(...newAccounts.filter(x => {\n            return role === ROLES.PAYER && x.role.payer || role === ROLES.AUTHORIZATIONS && x.role.authorizer;\n          }).map(acct => acct.tempId));\n        }\n        return acc.concat(acctTempId);\n      }, []);\n    } else if (role === ROLES.PROPOSER) {\n      const proposerAccts = newAccounts.filter(x => x.role.proposer);\n      if (proposerAccts.length > 1) {\n        throw new Error(`replaceRoles Error: Multiple proposer keys were resolved, but only one is allowed`);\n      }\n      ix[role] = proposerAccts[0]?.tempId ?? ix[role];\n    }\n  }\n}\nasync function resolveAccountsByIds(ix, accountTempIds) {\n  let depthLimit = arguments.length > 2 && arguments[2] !== undefined ? arguments[2] : MAX_DEPTH_LIMIT;\n  let {\n    debugLogger\n  } = arguments.length > 3 ? arguments[3] : undefined;\n  invariant$1(ix && typeof ix === \"object\", \"resolveAccountType Error: ix not defined\");\n  let newTempIds = new Set();\n  for (let accountId of accountTempIds) {\n    let account = ix.accounts[accountId];\n    invariant$1(Boolean(account), `resolveAccountType Error: account not found`);\n    const [resolvedAccountTempIds, foundNewAccounts] = await resolveSingleAccount(ix, accountId, depthLimit, {\n      debugLogger\n    });\n\n    // If new accounts were resolved, add them to the set so they can be explored next iteration\n    if (foundNewAccounts) {\n      const resolvedAccounts = resolvedAccountTempIds.map(resolvedAccountTempId => ix.accounts[resolvedAccountTempId]);\n      const flatResolvedAccounts = uniqueAccountsFlatMap(resolvedAccounts);\n\n      // Add new tempIds to the set so they can be used next iteration\n      flatResolvedAccounts.forEach(x => newTempIds.add(x.tempId));\n\n      // Update any roles in the interaction based on the new accounts\n      replaceRoles(ix, accountId, flatResolvedAccounts);\n    }\n  }\n\n  // Ensure all payers are of the same account\n  let payerAddress;\n  for (const payerTempID of ix[ROLES.PAYER]) {\n    let pAcct = ix.accounts[payerTempID];\n    if (!payerAddress) payerAddress = pAcct.addr;else if (payerAddress !== pAcct.addr) {\n      throw new Error(\"resolveAccountType Error: payers from different accounts detected\");\n    }\n  }\n  return newTempIds;\n}\nasync function resolveAccounts(ix) {\n  let opts = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : {};\n  if (isTransaction(ix)) {\n    if (!Array.isArray(ix.payer)) {\n      log.deprecate({\n        pkg: \"FCL\",\n        subject: '\"ix.payer\" must be an array. Support for ix.payer as a singular',\n        message: \"See changelog for more info.\"\n      });\n    }\n    let [debugLogger, getDebugMessage] = debug$1();\n    try {\n      // BFS, resolving one level of accounts at a time\n      let depthLimit = MAX_DEPTH_LIMIT;\n      let frontier = new Set([...getAccountTempIDs(ix[ROLES.PAYER]), ...getAccountTempIDs(ix[ROLES.PROPOSER]), ...getAccountTempIDs(ix[ROLES.AUTHORIZATIONS])]);\n      while (frontier.size > 0) {\n        if (depthLimit <= 0) {\n          throw new Error(`resolveAccounts Error: Depth limit (${MAX_DEPTH_LIMIT}) reached. Ensure your authorization functions resolve to an account after ${MAX_DEPTH_LIMIT} resolves.`);\n        }\n        frontier = await resolveAccountsByIds(ix, frontier, depthLimit, {\n          debugLogger\n        });\n        depthLimit--;\n      }\n      await removeUnusedIxAccounts(ix, {\n        debugLogger\n      });\n\n      // Ensure at least one account for each role is resolved (except for authorizations)\n      for (const role of Object.values(ROLES)) {\n        invariant$1(getAccountTempIDs(ix[role]).length > 0 || role === ROLES.AUTHORIZATIONS, `resolveAccountType Error: no accounts for role \"${role}\" found`);\n      }\n      if (opts.enableDebug) {\n        console.debug(getDebugMessage());\n      }\n    } catch (error) {\n      console.error(\"=== SAD PANDA ===\\n\\n\", error, \"\\n\\n=== SAD PANDA ===\");\n      throw error;\n    }\n  }\n  return ix;\n}\n\nasync function resolveSignatures(ix) {\n  if (isTransaction(ix)) {\n    try {\n      let insideSigners = findInsideSigners(ix);\n      const insidePayload = encodeTransactionPayload(prepForEncoding(ix));\n\n      // Promise.all could potentially break the flow if there are multiple inside signers trying to resolve at the same time\n      // causing multiple triggers of authz function that tries to render multiple auth iiframes/tabs/extensions\n      // as an alternative, use this:\n      // for(const insideSigner of insideSigners) {\n      //   await fetchSignature(ix, insidePayload)(insideSigner);\n      // }\n      await Promise.all(insideSigners.map(fetchSignature(ix, insidePayload)));\n      let outsideSigners = findOutsideSigners(ix);\n      const outsidePayload = encodeTransactionEnvelope({\n        ...prepForEncoding(ix),\n        payloadSigs: insideSigners.map(id => ({\n          address: ix.accounts[id].addr || \"\",\n          keyId: ix.accounts[id].keyId || 0,\n          sig: ix.accounts[id].signature || \"\"\n        }))\n      });\n\n      // Promise.all could potentially break the flow if there are multiple outside signers trying to resolve at the same time\n      // causing multiple triggers of authz function that tries to render multiple auth iframes/tabs/extensions\n      // as an alternative, use this:\n      // for(const outsideSigner of outsideSigners) {\n      //   await fetchSignature(ix, outsidePayload)(outsideSigner);\n      // }\n      await Promise.all(outsideSigners.map(fetchSignature(ix, outsidePayload)));\n    } catch (error) {\n      console.error(\"Signatures\", error, {\n        ix\n      });\n      throw error;\n    }\n  }\n  return ix;\n}\nfunction fetchSignature(ix, payload) {\n  return async function innerFetchSignature(id) {\n    const acct = ix.accounts[id];\n    if (acct.signature != null && acct.signature !== undefined) return;\n    const {\n      signature\n    } = await acct.signingFunction(buildSignable(acct, payload, ix));\n    ix.accounts[id].signature = signature;\n  };\n}\nfunction buildSignable(acct, message, ix) {\n  try {\n    return {\n      f_type: \"Signable\",\n      f_vsn: \"1.0.1\",\n      message,\n      addr: sansPrefix(acct.addr),\n      keyId: acct.keyId,\n      roles: acct.role,\n      cadence: ix.message.cadence,\n      args: ix.message.arguments.map(d => ix.arguments[d].asArgument),\n      data: {},\n      interaction: ix,\n      voucher: createSignableVoucher(ix)\n    };\n  } catch (error) {\n    console.error(\"buildSignable\", error);\n    throw error;\n  }\n}\nfunction prepForEncoding(ix) {\n  const payerAddress = sansPrefix((Array.isArray(ix.payer) ? ix.accounts[ix.payer[0]] : ix.accounts[ix.payer]).addr || \"\");\n  const proposalKey = ix.proposer ? {\n    address: sansPrefix(ix.accounts[ix.proposer].addr) || \"\",\n    keyId: ix.accounts[ix.proposer].keyId || 0,\n    sequenceNum: ix.accounts[ix.proposer].sequenceNum || 0\n  } : {};\n  return {\n    cadence: ix.message.cadence,\n    refBlock: ix.message.refBlock,\n    computeLimit: ix.message.computeLimit,\n    arguments: ix.message.arguments.map(id => ix.arguments[id].asArgument),\n    proposalKey,\n    payer: payerAddress,\n    authorizers: ix.authorizations.map(cid => sansPrefix(ix.accounts[cid].addr) || \"\").reduce((prev, current) => {\n      return prev.find(item => item === current) ? prev : [...prev, current];\n    }, [])\n  };\n}\n\nasync function resolveValidators(ix) {\n  const validators = get(ix, \"ix.validators\", []);\n  return pipe(ix, validators.map(cb => ix => cb(ix, {\n    Ok,\n    Bad\n  })));\n}\n\nasync function resolveFinalNormalization(ix) {\n  for (let key of Object.keys(ix.accounts)) {\n    ix.accounts[key].addr = sansPrefix(ix.accounts[key].addr);\n  }\n  return ix;\n}\n\nasync function resolveVoucherIntercept(ix) {\n  const fn = get(ix, \"ix.voucher-intercept\");\n  if (isFn$3(fn)) {\n    await fn(createSignableVoucher(ix));\n  }\n  return ix;\n}\n\nconst DEFAULT_COMPUTE_LIMIT = 100;\nasync function resolveComputeLimit(ix) {\n  if (isTransaction(ix)) {\n    ix.message.computeLimit = ix.message.computeLimit || (await config.get(\"fcl.limit\"));\n    if (!ix.message.computeLimit) {\n      logger.log.deprecate({\n        pkg: \"FCL/SDK\",\n        subject: \"The built-in default compute limit (DEFAULT_COMPUTE_LIMIT=10)\",\n        transition: \"https://github.com/onflow/flow-js-sdk/blob/master/packages/sdk/TRANSITIONS.md#0009-deprecate-default-compute-limit\"\n      });\n      ix.message.computeLimit = DEFAULT_COMPUTE_LIMIT;\n    }\n  }\n  return ix;\n}\n\nconst noop = v => v;\nconst debug = function (key) {\n  let fn = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : noop;\n  return async ix => {\n    const accts = ix => [\"\\nAccounts:\", {\n      proposer: ix.proposer,\n      authorizations: ix.authorizations,\n      payer: ix.payer\n    }, \"\\n\\nDetails:\", ix.accounts].filter(Boolean);\n    const log = function () {\n      for (var _len = arguments.length, msg = new Array(_len), _key = 0; _key < _len; _key++) {\n        msg[_key] = arguments[_key];\n      }\n      console.log(`debug[${key}] ---\\n`, ...msg, \"\\n\\n\\n---\");\n    };\n    if (await config.get(`debug.${key}`)) await fn(ix, log, accts);\n    return ix;\n  };\n};\nconst resolve = pipe([resolveCadence, debug(\"cadence\", (ix, log) => log(ix.message.cadence)), resolveComputeLimit, debug(\"compute limit\", (ix, log) => log(ix.message.computeLimit)), resolveArguments, debug(\"arguments\", (ix, log) => log(ix.message.arguments, ix.message)), resolveAccounts, debug(\"accounts\", (ix, log, accts) => log(...accts(ix))), /* special */execFetchRef, /* special */execFetchSequenceNumber, resolveSignatures, debug(\"signatures\", (ix, log, accts) => log(...accts(ix))), resolveFinalNormalization, resolveValidators, resolveVoucherIntercept, debug(\"resolved\", (ix, log) => log(ix))]);\nasync function execFetchRef(ix) {\n  if (isTransaction(ix) && ix.message.refBlock == null) {\n    const node = await config().get(\"accessNode.api\");\n    const sendFn = await config.first([\"sdk.transport\", \"sdk.send\"], send$1);\n    invariant$1(sendFn, `Required value for sdk.transport is not defined in config. See: ${\"https://github.com/onflow/fcl-js/blob/master/packages/sdk/CHANGELOG.md#0057-alpha1----2022-01-21\"}`);\n    ix.message.refBlock = (await sendFn(build([getBlock()]), {\n      config,\n      response,\n      Buffer,\n      ix: ixModule\n    }, {\n      node\n    }).then(decodeResponse)).id;\n  }\n  return ix;\n}\nasync function execFetchSequenceNumber(ix) {\n  if (isTransaction(ix)) {\n    var acct = Object.values(ix.accounts).find(a => a.role.proposer);\n    invariant$1(acct !== undefined, `Transactions require a proposer`);\n    if (acct && acct.sequenceNum == null) {\n      const node = await config().get(\"accessNode.api\");\n      const sendFn = await config.first([\"sdk.transport\", \"sdk.send\"], send$1);\n      invariant$1(sendFn, `Required value for sdk.transport is not defined in config. See: ${\"https://github.com/onflow/fcl-js/blob/master/packages/sdk/CHANGELOG.md#0057-alpha1----2022-01-21\"}`);\n      ix.accounts[acct.tempId].sequenceNum = await sendFn(await build([getAccount(acct.addr)]), {\n        config,\n        response,\n        Buffer,\n        ix: ixModule\n      }, {\n        node\n      }).then(decodeResponse).then(acctResponse => acctResponse.keys).then(keys => keys.find(key => key.index === acct.keyId)).then(key => key.sequenceNumber);\n    }\n  }\n  return ix;\n}\n\nfunction invariant() {\n  for (var _len = arguments.length, args = new Array(_len), _key = 0; _key < _len; _key++) {\n    args[_key] = arguments[_key];\n  }\n  if (args.length > 1) {\n    const [predicate, message] = args;\n    return invariant((ix, _ref) => {\n      let {\n        Ok,\n        Bad\n      } = _ref;\n      return predicate ? Ok(ix) : Bad(ix, message);\n    });\n  }\n  const [fn] = args;\n  return ix => fn(ix, {\n    Ok,\n    Bad\n  });\n}\n\nclass SubscriptionsNotSupportedError extends Error {\n  constructor() {\n    super(`The current transport does not support subscriptions.  If you have provided a custom transport (e.g. via \\`sdk.transport\\` configuration), ensure that it implements the subscribe method.`);\n    this.name = \"SubscriptionsNotSupportedError\";\n  }\n}\n\n/**\n * Get the SDK transport object, either from the provided override or from the global config.\n * @param overrides - Override default configuration with custom transport or send function.\n * @returns The SDK transport object.\n */\nasync function getTransport() {\n  let override = arguments.length > 0 && arguments[0] !== undefined ? arguments[0] : {};\n  invariant$1(override.send == null || override.transport == null, `SDK Transport Error: Cannot provide both \"transport\" and legacy \"send\" options.`);\n  const transportOrSend = override.transport || override.send || (await config().first([\"sdk.transport\", \"sdk.send\"], httpTransport));\n\n  // Backwards compatibility with legacy send function\n  if (!isTransportObject(transportOrSend)) {\n    return {\n      send: transportOrSend,\n      subscribe: () => {\n        throw new SubscriptionsNotSupportedError();\n      }\n    };\n  }\n  return transportOrSend;\n}\nfunction isTransportObject(transport) {\n  return transport.send !== undefined && transport.subscribe !== undefined && typeof transport.send === \"function\" && typeof transport.subscribe === \"function\";\n}\n\n/**\n * @description - Sends arbitrary scripts, transactions, and requests to Flow\n * @param args - An array of functions that take interaction and return interaction\n * @param opts - Optional parameters\n * @returns - A promise that resolves to a response\n */\nconst send = async function () {\n  let args = arguments.length > 0 && arguments[0] !== undefined ? arguments[0] : [];\n  let opts = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : {};\n  const transport = await getTransport(opts);\n  const sendFn = transport.send.bind(transport);\n  invariant(sendFn, `Required value for sdk.transport is not defined in config. See: ${\"https://github.com/onflow/fcl-js/blob/master/packages/sdk/CHANGELOG.md#0057-alpha1----2022-01-21\"}`);\n  const resolveFn = await config.first([\"sdk.resolve\"], opts.resolve || resolve);\n  opts.node = opts.node || (await config().get(\"accessNode.api\"));\n  if (Array.isArray(args)) args = pipe(initInteraction(), args);\n  return sendFn(await resolveFn(args), {\n    config,\n    response,\n    ix: ixModule,\n    Buffer\n  }, opts);\n};\n\n/**\n * Subscribe to a topic without decoding the data.\n * @param params - The parameters for the subscription.\n * @param opts - Additional options for the subscription.\n * @returns A promise that resolves once the subscription is active.\n */\nfunction subscribeRaw(_ref) {\n  let {\n    topic,\n    args,\n    onData,\n    onError\n  } = _ref;\n  let opts = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : {};\n  async function subscribe() {\n    let transport;\n    let node;\n    try {\n      transport = await getTransport(opts);\n      node = opts?.node || (await config().get(\"accessNode.api\"));\n      invariant$1(!!node, `SDK Send Error: Either opts.node or \"accessNode.api\" in config must be defined.`);\n    } catch (e) {\n      onError(e instanceof Error ? e : new Error(String(e)));\n      return;\n    }\n\n    // Subscribe using the resolved transport\n    return transport.subscribe({\n      topic,\n      args,\n      onData,\n      onError\n    }, {\n      node,\n      ...opts\n    });\n  }\n  let subscriptionPromise = subscribe();\n  return {\n    unsubscribe: () => {\n      subscriptionPromise.then(sub => sub?.unsubscribe?.());\n    }\n  };\n}\n\n/**\n * Subscribe to a topic and decode the data.\n * @param params - The parameters for the subscription.\n * @param opts - Additional options for the subscription.\n * @returns A promise that resolves when the subscription is active.\n */\nfunction subscribe(_ref) {\n  let {\n    topic,\n    args,\n    onData,\n    onError\n  } = _ref;\n  let opts = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : {};\n  const sub = subscribeRaw({\n    topic,\n    args,\n    onData: data => {\n      decodeResponse(data).then(onData).catch(e => {\n        onError(new Error(`Failed to decode response: ${e.message}`));\n        sub?.unsubscribe?.();\n      });\n    },\n    onError\n  }, opts);\n  return sub;\n}\n\nasync function decode(response) {\n  const decodersFromConfig = await config().where(/^decoder\\./);\n  const decoders = Object.entries(decodersFromConfig).map(_ref => {\n    let [pattern, xform] = _ref;\n    pattern = `/${pattern.replace(/^decoder\\./, \"\")}$/`;\n    return [pattern, xform];\n  });\n  return decodeResponse(response, Object.fromEntries(decoders));\n}\n\nconst findPayloadSigners = voucher => {\n  // Payload Signers Are: (authorizers + proposer) - payer\n  const payload = new Set(voucher.authorizers);\n  payload.add(voucher.proposalKey.address);\n  payload.delete(voucher.payer);\n  return Array.from(payload).map(addr => withPrefix(addr));\n};\nconst findEnvelopeSigners = voucher => {\n  // Envelope Signers Are: (payer)\n  const envelope = new Set([voucher.payer]);\n  return Array.from(envelope).map(addr => withPrefix(addr));\n};\nclass UnableToDetermineMessageEncodingTypeForSignerAddress extends Error {\n  constructor(signerAddress) {\n    const msg = `\n        Encode Message From Signable Error: Unable to determine message encoding for signer addresss: ${signerAddress}. \n        Please ensure the address: ${signerAddress} is intended to sign the given transaction as specified by the transaction signable.\n      `.trim();\n    super(msg);\n    this.name = \"Unable To Determine Message Encoding For Signer Addresss\";\n  }\n}\nconst encodeMessageFromSignable = (signable, signerAddress) => {\n  let payloadSigners = findPayloadSigners(signable.voucher);\n  let envelopeSigners = findEnvelopeSigners(signable.voucher);\n  const isPayloadSigner = payloadSigners.includes(withPrefix(signerAddress));\n  const isEnvelopeSigner = envelopeSigners.includes(withPrefix(signerAddress));\n  if (!isPayloadSigner && !isEnvelopeSigner) {\n    throw new UnableToDetermineMessageEncodingTypeForSignerAddress(signerAddress);\n  }\n  const message = {\n    cadence: signable.voucher.cadence,\n    refBlock: signable.voucher.refBlock,\n    computeLimit: signable.voucher.computeLimit,\n    arguments: signable.voucher.arguments,\n    proposalKey: {\n      ...signable.voucher.proposalKey,\n      address: sansPrefix(signable.voucher.proposalKey.address)\n    },\n    payer: sansPrefix(signable.voucher.payer),\n    authorizers: signable.voucher.authorizers.map(sansPrefix),\n    payloadSigs: signable.voucher.payloadSigs.map(ps => ({\n      ...ps,\n      address: sansPrefix(ps.address)\n    }))\n  };\n  return isPayloadSigner ? encodeTransactionPayload(message) : encodeTransactionEnvelope(message);\n};\n\n/**\n * @description A builder function that adds a validator to a transaction\n * @param cb The validator function\n * @returns A function that processes an interaction object\n */\nfunction validator(cb) {\n  return update(\"ix.validators\", validators => Array.isArray(validators) ? [...validators, cb] : [cb]);\n}\n\n/**\n * @description A builder function that returns a partial interaction to a block at a specific height\n * @param height The height of the block to get\n * @returns A function that processes a partial interaction object\n */\nfunction atBlockHeight(height) {\n  return pipe([ix => {\n    ix.block.height = height;\n    return ix;\n  }, validator(ix => {\n    if (typeof ix.block.isSealed === \"boolean\") throw new Error(\"Unable to specify both block height and isSealed.\");\n    if (ix.block.id) throw new Error(\"Unable to specify both block height and block id.\");\n    return ix;\n  })]);\n}\n\nfunction atBlockId(id) {\n  return pipe([ix => {\n    ix.block.id = id;\n    return Ok(ix);\n  }, validator((ix, _ref) => {\n    let {\n      Ok,\n      Bad\n    } = _ref;\n    if (isGetAccount(ix)) return Bad(ix, \"Unable to specify a block id with a Get Account interaction.\");\n    if (typeof ix.block.isSealed === \"boolean\") return Bad(ix, \"Unable to specify both block id and isSealed.\");\n    if (ix.block.height) return Bad(ix, \"Unable to specify both block id and block height.\");\n    return Ok(ix);\n  })]);\n}\n\n/**\n * @description A builder function that returns a partial interaction to query the latest block with the given finality state\n * @param isSealed Block finality state, defaults to latest executed block (\"soft-finality\"), set to true for sealed blocks (\"hard-finality\")\n * @returns A function that processes a partial interaction object\n */\nfunction atLatestBlock() {\n  let isSealed = arguments.length > 0 && arguments[0] !== undefined ? arguments[0] : false;\n  return pipe([ix => {\n    ix.block.isSealed = isSealed;\n    return ix;\n  }, validator(ix => {\n    if (ix.block.id) throw new Error(\"Unable to specify both block finality and block id.\");\n    if (ix.block.height) throw new Error(\"Unable to specify both block finality and block height.\");\n    return ix;\n  })]);\n}\n\n/**\n * @description Returns the details of an account from their public address\n * @param address Address of the account\n * @param queryOptions Query parameters\n * @param queryOptions.height Block height to query\n * @param queryOptions.id Block ID to query\n * @param queryOptions.isSealed Block finality\n * @param opts Optional parameters\n * @returns A promise that resolves to an account response\n */\nasync function account(address) {\n  let {\n    height,\n    id,\n    isSealed\n  } = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : {};\n  let opts = arguments.length > 2 ? arguments[2] : undefined;\n  invariant$1(!(id && height || id && isSealed || height && isSealed), `Method: account -- Only one of the following parameters can be provided: id, height, isSealed`);\n\n  // Get account by ID\n  if (id) return await send([getAccount(address), atBlockId(id)], opts).then(decodeResponse);\n\n  // Get account by height\n  if (height) return await send([getAccount(address), atBlockHeight(height)], opts).then(decodeResponse);\n\n  // Get account by latest block\n  return await send([getAccount(address), atLatestBlock(isSealed ?? false)], opts).then(decodeResponse);\n}\n\n/**\n * @description Returns the latest block (optionally sealed or not), by id, or by height\n * @param queryOptions Query parameters\n * @param queryOptions.sealed Whether to query for a sealed block\n * @param queryOptions.height Block height to query\n * @param queryOptions.id Block ID to query\n * @param opts Optional parameters\n * @returns A promise that resolves to a block response\n */\nasync function block() {\n  let {\n    sealed = false,\n    id,\n    height\n  } = arguments.length > 0 && arguments[0] !== undefined ? arguments[0] : {};\n  let opts = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : {};\n  invariant$1(!(sealed && id || sealed && height), `Method: block -- Cannot pass \"sealed\" with \"id\" or \"height\"`);\n  invariant$1(!(id && height), `Method: block -- Cannot pass \"id\" and \"height\" simultaneously`);\n\n  // Get block by ID\n  if (id) return await send([getBlock(), atBlockId(id)], opts).then(decodeResponse);\n\n  // Get block by height\n  if (height) return await send([getBlock(), atBlockHeight(height)], opts).then(decodeResponse);\n\n  // Get latest block\n  return await send([getBlock(sealed)], opts).then(decodeResponse);\n}\n\n/**\n * @description A builder function for the Get Node Version Info interaction\n * @returns An interaction object\n */\nfunction getNodeVersionInfo() {\n  return pipe([makeGetNodeVerionInfo, ix => {\n    return Ok(ix);\n  }]);\n}\n\n/**\n * @description Returns the version information from to connected node\n * @returns A promise that resolves to a block response\n */\nasync function nodeVersionInfo() {\n  let opts = arguments.length > 0 && arguments[0] !== undefined ? arguments[0] : {};\n  return await send([getNodeVersionInfo()], opts).then(decodeResponse);\n}\n\nfunction authorizations() {\n  let ax = arguments.length > 0 && arguments[0] !== undefined ? arguments[0] : [];\n  return pipe(ax.map(authz => {\n    return prepAccount(authz, {\n      role: TransactionRole.AUTHORIZER\n    });\n  }));\n}\nfunction authorization(addr, signingFunction, keyId, sequenceNum) {\n  return {\n    addr,\n    signingFunction,\n    keyId,\n    sequenceNum\n  };\n}\n\n/**\n * @description A builder function that returns the interaction to get events\n * @param eventType The type of event to get\n * @param start The start block ID or height\n * @param end The end block ID or height\n * @returns A function that processes an interaction object\n */\nfunction getEvents(eventType, start, end) {\n  return pipe([makeGetEvents, ix => {\n    ix.events.eventType = eventType;\n    ix.events.start = start;\n    ix.events.end = end;\n    return Ok(ix);\n  }]);\n}\n\n/**\n * @description A builder function that returns the interaction to get events at a block height range\n * @param eventType The type of event to get\n * @param startHeight The start height of the block range\n * @param endHeight The end height of the block range\n * @returns A function that processes an interaction object\n */\nfunction getEventsAtBlockHeightRange(eventType, startHeight, endHeight) {\n  return pipe([makeGetEvents, ix => {\n    ix.events.eventType = eventType;\n    ix.events.start = startHeight;\n    ix.events.end = endHeight;\n    return Ok(ix);\n  }]);\n}\n\n/**\n * @description A builder function that returns the interaction to get events at specific block IDs\n * @param eventType The type of event to get\n * @param blockIds The block IDs to get events from\n * @returns A function that processes an interaction object\n */\nfunction getEventsAtBlockIds(eventType, blockIds) {\n  return pipe([makeGetEvents, ix => {\n    ix.events.eventType = eventType;\n    ix.events.blockIds = blockIds;\n    return Ok(ix);\n  }]);\n}\n\n/**\n * @description A builder function that returns the interaction to get a block header\n * @param isSealed Whether or not the block should be sealed\n * @returns A function that processes an interaction object\n */\nfunction getBlockHeader() {\n  let isSealed = arguments.length > 0 && arguments[0] !== undefined ? arguments[0] : null;\n  return pipe([makeGetBlockHeader, ix => {\n    ix.block.isSealed = isSealed;\n    return Ok(ix);\n  }]);\n}\n\n/**\n * @description A builder function that returns the interaction to get a collection by ID\n * @param id The ID of the collection to get\n * @returns A function that processes an interaction object\n */\nfunction getCollection() {\n  let id = arguments.length > 0 && arguments[0] !== undefined ? arguments[0] : null;\n  return pipe([makeGetCollection, ix => {\n    ix.collection.id = id;\n    return ix;\n  }]);\n}\n\n/**\n * @description A builder function that returns the status of transaction\n * NOTE: The transactionID provided must be from the current spork.\n * @param transactionId The id of the transaction to get status\n * @returns An interaction object\n */\nfunction getTransactionStatus(transactionId) {\n  return pipe([makeGetTransactionStatus, ix => {\n    ix.transaction.id = transactionId;\n    return Ok(ix);\n  }]);\n}\n\n/**\n * @description A builder function that returns the interaction to get a transaction by ID\n * @param id The ID of the transaction to get\n * @returns A function that processes an interaction object\n */\nfunction getTransaction(id) {\n  return pipe([makeGetTransaction, ix => {\n    ix.transaction.id = id;\n    return Ok(ix);\n  }]);\n}\n\n/**\n * @description A builder function that returns the interaction to get network parameters\n * @returns A function that processes an interaction object\n */\nfunction getNetworkParameters() {\n  return pipe([makeGetNetworkParameters, ix => {\n    return Ok(ix);\n  }]);\n}\n\n/**\n * @description A builder function that sets the compute limit for a transaction\n * @param limit The compute limit to set\n * @returns A function that processes an interaction object\n */\nfunction limit(limit) {\n  return ix => {\n    ix.message.computeLimit = limit;\n    return ix;\n  };\n}\n\n/**\n * @description A utility builder to be used with other builders to pass in arguments with a value and supported type\n * @param ax An array of arguments\n * @returns An interaction object\n */\nfunction args(ax) {\n  return pipe(ax.map(makeArgument));\n}\n\n/**\n * @description A utility builder to be used with fcl.args[...] to create FCL supported arguments for interactions\n * @param value The value of the argument\n * @param xform A function to transform the value\n * @returns An argument object\n */\nfunction arg(value, xform) {\n  return {\n    value,\n    xform\n  };\n}\n\nfunction proposer(authz) {\n  return prepAccount(authz, {\n    role: TransactionRole.PROPOSER\n  });\n}\n\n/**\n * @description A builder function that adds payer account(s) to a transaction\n * @param ax An account address or array of account addresses\n * @returns A function that takes an interaction and returns a new interaction with the payer(s) added\n */\nfunction payer() {\n  let ax = arguments.length > 0 && arguments[0] !== undefined ? arguments[0] : [];\n  if (!Array.isArray(ax)) ax = [ax];\n  return pipe(ax.map(authz => {\n    return prepAccount(authz, {\n      role: TransactionRole.PAYER\n    });\n  }));\n}\n\n/**\n * @description A builder function that creates a ping interaction\n * @returns A function that processes an interaction object\n */\nfunction ping() {\n  return makePing;\n}\n\n/**\n * @description A builder function that sets the reference block for a transaction\n * @param refBlock The reference block ID\n * @returns A function that processes an interaction object\n */\nfunction ref(refBlock) {\n  return pipe([ix => {\n    ix.message.refBlock = refBlock;\n    return Ok(ix);\n  }]);\n}\n\n/**\n * @description A builder function that creates a script interaction\n * @returns A function that processes an interaction object\n */\nfunction script() {\n  return pipe([makeScript, put(\"ix.cadence\", template(...arguments))]);\n}\n\nconst DEFAULT_SCRIPT_ACCOUNTS = [];\nconst DEFAULT_REF = null;\n\n/**\n * @description A template builder to use a Cadence transaction for an interaction\n * @param args The arguments to pass\n * @returns A function that processes an interaction object\n */\nfunction transaction() {\n  return pipe([makeTransaction, put(\"ix.cadence\", template(...arguments)), ix => {\n    ix.message.refBlock = ix.message.refBlock || DEFAULT_REF;\n    ix.authorizations = ix.authorizations || DEFAULT_SCRIPT_ACCOUNTS;\n    return Ok(ix);\n  }]);\n}\n\n/**\n * @description A builder function that intercepts and modifies a voucher\n * @param fn The function to intercept and modify the voucher\n * @returns A function that processes an interaction object\n */\nfunction voucherIntercept(fn) {\n  return put(\"ix.voucher-intercept\", fn);\n}\n\n/**\n * @description Subscribe to events with the given filter & parameters\n * @param filter The filter to subscribe to events with\n * @returns A function that processes an interaction object\n */\nfunction subscribeEvents(_ref) {\n  let {\n    startBlockId,\n    startHeight,\n    eventTypes,\n    addresses,\n    contracts,\n    heartbeatInterval\n  } = _ref;\n  invariant$1(!(startBlockId && startHeight), `SDK Subscribe Events Error: Cannot set both startBlockId and startHeight.`);\n  return pipe([makeSubscribeEvents, ix => {\n    ix.subscribeEvents.startBlockId = startBlockId ?? null;\n    ix.subscribeEvents.startHeight = startHeight ?? null;\n    ix.subscribeEvents.eventTypes = eventTypes ?? null;\n    ix.subscribeEvents.addresses = addresses ?? null;\n    ix.subscribeEvents.contracts = contracts ?? null;\n    ix.subscribeEvents.heartbeatInterval = heartbeatInterval ?? null;\n    return Ok(ix);\n  }]);\n}\n\nconst resolveProposerSequenceNumber = _ref => {\n  let {\n    node\n  } = _ref;\n  return async ix => {\n    if (!isTransaction(ix)) return Ok(ix);\n    if (ix.accounts[ix.proposer].sequenceNum) return Ok(ix);\n    const sendFn = await config.first([\"sdk.transport\", \"sdk.send\"], send$1);\n    invariant$1(sendFn, `Required value for sdk.transport is not defined in config. See: ${\"https://github.com/onflow/fcl-js/blob/master/packages/sdk/CHANGELOG.md#0057-alpha1----2022-01-21\"}`);\n    const response$1 = await sendFn(await build([getAccount(ix.accounts[ix.proposer].addr)]), {\n      config,\n      response: response,\n      Buffer,\n      ix: ixModule\n    }, {\n      node\n    });\n    const decoded = await decodeResponse(response$1);\n    ix.accounts[ix.proposer].sequenceNum = decoded.keys[ix.accounts[ix.proposer].keyId].sequenceNumber;\n    return Ok(ix);\n  };\n};\n\nasync function getRefId(opts) {\n  const node = await config().get(\"accessNode.api\");\n  const sendFn = await config.first([\"sdk.transport\", \"sdk.send\"], send$1);\n  invariant$1(sendFn, `Required value for sdk.transport is not defined in config. See: ${\"https://github.com/onflow/fcl-js/blob/master/packages/sdk/CHANGELOG.md#0057-alpha1----2022-01-21\"}`);\n  var ix;\n  ix = await pipe(initInteraction(), [getBlock()]);\n  ix = await sendFn(ix, {\n    config,\n    response,\n    Buffer,\n    ix: ixModule\n  }, {\n    node\n  });\n  ix = await decodeResponse(ix);\n  return ix.id;\n}\nfunction resolveRefBlockId(opts) {\n  return async ix => {\n    if (!isTransaction(ix)) return Ok(ix);\n    if (ix.message.refBlock) return Ok(ix);\n    ix.message.refBlock = await getRefId();\n    return Ok(ix);\n  };\n}\n\nfunction mockAccountResponse(ix) {\n  let numberOfKeys = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : 5;\n  // prettier-ignore\n  invariant$1(ix.account, \"mockAccountResponse(ix) -- ix.account is missing\", ix);\n  // prettier-ignore\n  invariant$1(ix.account.addr, \"mockAccountResponse(ix) -- ix.account.addr is missing\", ix);\n  const address = ix.account.addr;\n  return {\n    account: {\n      addr: address,\n      keys: Array.from({\n        length: numberOfKeys\n      }, (_, i) => ({\n        index: i,\n        sequenceNumber: 42\n      }))\n    }\n  };\n}\nfunction mockGetBlockResponse(ix) {\n  return {\n    tag: \"GET_BLOCK\",\n    block: {\n      id: \"32\"\n    }\n  };\n}\nconst identity = v => v;\nfunction mockSend() {\n  let fallback = arguments.length > 0 && arguments[0] !== undefined ? arguments[0] : identity;\n  return async function execSend(ix) {\n    ix = await ix;\n    switch (true) {\n      case isGetAccount(ix):\n        return mockAccountResponse(ix);\n      case isGetBlock(ix):\n        return mockGetBlockResponse();\n      default:\n        return fallback(ix);\n    }\n  };\n}\n\nconst idof = acct => `${withPrefix(acct.addr)}-${acct.keyId}`;\nfunction sig(opts) {\n  return [\"SIGNATURE\", opts.addr, opts.keyId].join(\".\");\n}\nfunction authzFn() {\n  let opts = arguments.length > 0 && arguments[0] !== undefined ? arguments[0] : {};\n  return function (account) {\n    const acct = {\n      ...account,\n      ...opts,\n      resolve: null,\n      signingFunction: opts.signingFunction || account.signingFunction || fallbackSigningFunction\n    };\n    return acct;\n    function fallbackSigningFunction(_signable) {\n      return {\n        addr: acct.addr,\n        keyId: acct.keyId,\n        signature: sig(acct)\n      };\n    }\n  };\n}\nfunction authzResolve() {\n  let opts = arguments.length > 0 && arguments[0] !== undefined ? arguments[0] : {};\n  return function (account) {\n    const {\n      tempId,\n      ...rest\n    } = opts;\n    return {\n      ...account,\n      tempId: tempId || \"WITH_RESOLVE\",\n      resolve: authzFn(rest)\n    };\n  };\n}\nconst ROLE = {\n  proposer: false,\n  authorizer: false,\n  payer: false\n};\nfunction authzResolveMany() {\n  let opts = arguments.length > 0 && arguments[0] !== undefined ? arguments[0] : {\n    authorizations: []\n  };\n  return function (account) {\n    const tempId = opts.tempId || \"AUTHZ_RESOLVE_MANY\";\n    return {\n      ...account,\n      tempId,\n      resolve: () => [opts.proposer && authzFn(opts.proposer)({\n        role: {\n          ...ROLE,\n          proposer: true\n        }\n      }), ...opts.authorizations.map(authzFn).map(d => d({\n        role: {\n          ...ROLE,\n          authorizer: true\n        }\n      })), opts.payer && authzFn(opts.payer)({\n        role: {\n          ...ROLE,\n          payer: true\n        }\n      })].filter(Boolean)\n    };\n  };\n}\nfunction authzDeepResolveMany() {\n  let opts = arguments.length > 0 && arguments[0] !== undefined ? arguments[0] : {\n    authorizations: []\n  };\n  let depth = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : 1;\n  return function (account) {\n    const tempId = opts.tempId || \"AUTHZ_DEEP_RESOLVE_MANY\";\n    return {\n      ...account,\n      tempId,\n      resolve: depth > 0 ? authzDeepResolveMany(opts, depth - 1)(account).resolve : authzResolveMany(opts)(account).resolve\n    };\n  };\n}\n\nconst run = function () {\n  let fns = arguments.length > 0 && arguments[0] !== undefined ? arguments[0] : [];\n  return build([ref(\"123\"), ...fns]).then(resolve);\n};\n\nvar index = /*#__PURE__*/Object.freeze({\n  __proto__: null,\n  authzDeepResolveMany: authzDeepResolveMany,\n  authzFn: authzFn,\n  authzResolve: authzResolve,\n  authzResolveMany: authzResolveMany,\n  idof: idof,\n  mockSend: mockSend,\n  run: run,\n  sig: sig\n});\n\nconst VERSION = \"1.9.0\";\n\nconst flowMainnet = {\n  \"flow.network\": \"mainnet\",\n  \"accessNode.api\": \"https://rest-mainnet.onflow.org\",\n  \"discovery.wallet\": \"https://fcl-discovery.onflow.org/authn\"\n};\nconst flowTestnet = {\n  \"flow.network\": \"testnet\",\n  \"accessNode.api\": \"https://rest-testnet.onflow.org\",\n  \"discovery.wallet\": \"https://fcl-discovery.onflow.org/testnet/authn\"\n};\nconst flowEmulator = {\n  \"flow.network\": \"local\",\n  \"accessNode.api\": \"http://127.0.0.1:8888\",\n  \"discovery.wallet\": \"http://localhost:8701/fcl/authn\"\n};\n\n// Deprecated\nconst params = params => logger.log.deprecate({\n  pkg: \"FCL/SDK\",\n  message: `The params builder has been removed from the Flow JS-SDK/FCL.`,\n  transition: \"https://github.com/onflow/flow-js-sdk/blob/master/packages/sdk/TRANSITIONS.md#0001-deprecate-params\",\n  level: logger.LEVELS.error\n});\nconst param = params => logger.log.deprecate({\n  pkg: \"FCL/SDK\",\n  message: `The param builder has been removed from the Flow JS-SDK/FCL.`,\n  transition: \"https://github.com/onflow/flow-js-sdk/blob/master/packages/sdk/TRANSITIONS.md#0001-deprecate-params\",\n  level: logger.LEVELS.error\n});\n\nexport { SubscriptionsNotSupportedError, index as TestUtils, VERSION, account, arg, args, atBlockHeight, atBlockId, atLatestBlock, authorization, authorizations, block, build, createSignableVoucher, decode, destroy, encodeMessageFromSignable, encodeTransactionEnvelope, encodeTransactionPayload, encodeTxIdFromVoucher, flowEmulator, flowMainnet, flowTestnet, get, getAccount, getBlock, getBlockHeader, getCollection, getEvents, getEventsAtBlockHeightRange, getEventsAtBlockIds, getNetworkParameters, getNodeVersionInfo, getTransaction, getTransactionStatus, initInteraction, interaction, invariant, isBad, isGetAccount, isGetBlock, isGetBlockHeader, isGetCollection, isGetEvents, isGetNetworkParameters, isGetNodeVersionInfo, isGetTransaction, isGetTransactionStatus, isOk, isPing, isScript, isTransaction, isUnknown, limit, nodeVersionInfo, param, params, payer, ping, pipe, proposer, put, ref, resolve, resolveAccounts, resolveArguments, resolveCadence, resolveFinalNormalization, resolveProposerSequenceNumber, resolveRefBlockId, resolveSignatures, resolveValidators, resolveVoucherIntercept, response, script, send, subscribe, subscribeEvents, subscribeRaw, transaction, update, validator, voucherIntercept, voucherToTxId, why };\n//# sourceMappingURL=sdk.module.js.map\n"], "names": ["native", "InteractionTag", "InteractionStatus", "TransactionRole", "InteractionResolverKind", "config", "LEVELS", "log", "async", "options", "title", "message", "level", "always", "configLoggerLevel", "get", "warn", "loggerMessageArgs", "replace", "trim", "buildLoggerMessageArgs", "debug", "console", "info", "error", "invariant", "fact", "msg", "Error", "stack", "split", "filter", "d", "test", "join", "_len", "arguments", "length", "rest", "Array", "_key", "getRandomValues", "deprecate", "pkg", "subject", "transition", "callback", "logMessage", "str", "char<PERSON>t", "toUpperCase", "slice", "rnds8", "Uint8Array", "rng", "crypto", "bind", "byteToHex", "i", "push", "toString", "randomUUID", "v4", "buf", "offset", "rnds", "random", "arr", "unsafeStringify", "mailbox", "queue", "next", "deliver", "shift", "undefined", "receive", "Promise", "resolve", "pipe", "funcs", "v", "reduce", "res", "func", "isObject", "value", "isArray", "mergeDeep", "target", "_len3", "sources", "_key3", "source", "key", "Object", "assign", "mergeFlowJSONs", "filterContracts", "obj", "contracts", "filterDependencies", "dependencies", "getContracts", "jsons", "network", "_len2", "_key2", "mergePipe", "_ref3", "deployments", "accounts", "networkDeployment", "entries", "c", "_ref4", "accountAddress", "address", "contract", "mapDeploymentsToNetworkAddress", "_ref", "networkContractAlias", "aliases", "mapContractAliasesToNetworkAddress", "_ref2", "networkDependencyAlias", "mapDependencyAliasesToNetworkAddress", "hasPrivateKeys", "flowJSON", "hasPrivateKey", "_ref5", "prototype", "hasOwnProperty", "call", "cleanedNetwork", "toLowerCase", "some", "encode", "input", "output", "<PERSON><PERSON><PERSON>", "concat", "encodeLength", "inputBuf", "<PERSON><PERSON><PERSON><PERSON>", "isHexPrefixed", "from", "a", "integer", "hex", "intToHex", "intToBuffer", "<PERSON><PERSON><PERSON><PERSON>", "len", "hex<PERSON><PERSON><PERSON>", "firstByte", "sansPrefix", "withPrefix", "safeParseJSON", "data", "JSON", "parse", "ACCT", "ACCOUNT", "ARG", "ARGUMENT", "IX", "UNKNOWN", "OK", "KEYS", "Set", "keys", "initInteraction", "isNumber", "isObj", "isNull", "isFn", "isInteraction", "ix", "Ok", "status", "Bad", "reason", "BAD", "makeIx", "wat", "tag", "prepAccountKeyId", "acct", "keyId", "isNaN", "parseInt", "initAccount", "makeUnknown", "makeScript", "SCRIPT", "makeTransaction", "TRANSACTION", "makeGetTransactionStatus", "GET_TRANSACTION_STATUS", "makeGetTransaction", "GET_TRANSACTION", "makeGetAccount", "GET_ACCOUNT", "makeGetEvents", "GET_EVENTS", "makePing", "PING", "makeGetBlock", "GET_BLOCK", "makeGetBlockHeader", "GET_BLOCK_HEADER", "makeGetCollection", "GET_COLLECTION", "makeGetNetworkParameters", "GET_NETWORK_PARAMETERS", "makeSubscribeEvents", "SUBSCRIBE_EVENTS", "makeGetNodeVerionInfo", "GET_NODE_VERSION_INFO", "is", "isUnknown", "isScript", "isTransaction", "isGetTransactionStatus", "isGetTransaction", "isGetAccount", "isGetEvents", "isPing", "isGetBlock", "isGetBlockHeader", "isGetCollection", "isGetNetworkParameters", "isGetNodeVersionInfo", "isSubscribeEvents", "isBad", "rec<PERSON><PERSON>e", "fns", "has", "hardMode", "hd", "cur", "e", "args", "arg1", "arg2", "identity", "fallback", "assigns", "interaction", "account", "kind", "argument", "arg", "tempId", "uuidv4", "asArgument", "xform", "resolveArgument", "opts", "role", "authorization", "fn", "AUTHORIZER", "authorizations", "PAYER", "payer", "put"], "mappings": "IACeA,QCDHC,gBAiBAC,mBAKAC,iBAMAC;;;;;;;;;;ACrBZ,IAAIC,WAAiB;;AAQTC,IAAAA,SAAM,SAANA;EAAM,OAANA,OAAAA,OAAM,QAAA,KAAA,SAANA,OAAAA,OAAM,OAAA,KAAA,QAANA,OAAAA,OAAM,MAAA,KAAA;EAANA,OAAAA,OAAM,OAAA,KAAA,QAANA,OAAAA,OAAM,QAAA,KAAA,SAANA;AAAM,CAAA,CAAA;;AAiBlB,MA8BaC,MAAMC,MAAOC;EAMxB,OAAMC,OAACA,OAAKC,SAAEA,SAAOC,OAAEA,OAAKC,QAAEA,UAAUJ,SAClCK,2BACGT,cAAYU,IAAY,oBAAoBT,OAAOU;EAG5D,KAAKH,UAAUC,oBAAoBF,OAAO;EAE1C,MAAMK,oBA3CwBR,CAAAA;IAI9B,OAAMC,OAACA,OAAKC,SAAEA,WAAWF;IACzB,OAAO,EACL,WACIC,kDAGFC,oDAICO,QAAQ,iBAAiB,MACzBC,QACH;IA2BwBC,CAAuB;IAACV;IAAOC;;EAEzD,QAAQC;GACN,KAAKN,OAAOe;IACVC,QAAQD,SAASJ;IACjB;;GACF,KAAKX,OAAOiB;IACVD,QAAQC,QAAQN;IAChB;;GACF,KAAKX,OAAOU;IACVM,QAAQN,QAAQC;IAChB;;GACF,KAAKX,OAAOkB;IACVF,QAAQE,SAASP;IACjB;;GACF;IACEK,QAAQf,OAAOU;;;;ACrFd,SAASQ,UACdC,MACAC;EAGA,KAAKD,MAAM;IACT,MAAMF,QAAQ,IAAII,MAAM,aAAaD;IACrCH,MAAMK,QAAQL,MAAMK,OAChBC,MAAM,OACNC,QAAOC,MAAM,eAAeC,KAAKD,MACjCE,KAAK;IAAK,KAAAC,IAAAA,OAAAC,UAAAC,QAPbC,OAAI,IAAAC,MAAAJ,OAAAA,IAAAA,OAAA,IAAA,IAAAK,OAAA,GAAAA,OAAAL,MAAAK,QAAJF,KAAIE,OAAAJ,KAAAA,UAAAI;IASL,MADAlB,QAAQE,MAAM,eAAeA,OAAO,WAAWc,MAAM,gBAC/Cd;;AAEV;;ACjBA,IAAIiB;;AF8GJlC,IAAImC,YAAmBjC;EAQrB,OAAMkC,KACJA,KAAGC,SACHA,SAAOC,YACPA,YAAUjC,OACVA,QAAQN,OAAOU,MAAIL,SACnBA,UAAU,IAAEmC,UACZA,WAAW,QACTrC,SAMEsC,aAAaA;IACjBxC,WAAI;MACFG,QAAUiC,MAAMA,MAAM,MAAM,MAArB;MACPhC,SAAS,WAEPiC,UACI,GAVqBI,MAWnBJ,SAVHI,IAAIC,OAAO,GAAGC,gBAAgBF,IAAIG,MAAM,6DAYrCR,MAAM,SAASA,MAAM,QAEvB,KACHhC,UAAU,OAAOA,UAAU,KAC5BkC,aACI,6EAA6EA,eAC7E,WAEN1B;MACAP;;IAtB2BoC,IAAAA;;EAyB/B,OAAwB,qBAAbF,WACFtC;IAEL,aADMuC,oBACOD,YAASV;AACvB,MAEIW;;;AE7JT,MAAMK,QAAQ,IAAIC,WAAW;;AACd,SAASC;EAEtB,KAAKb,oBAEHA,kBAAoC,sBAAXc,UAA0BA,OAAOd,mBAAmBc,OAAOd,gBAAgBe,KAAKD;GAEpGd,kBACH,MAAM,IAAIb,MAAM;EAIpB,OAAOa,gBAAgBW;AACzB;;ACXA,MAAMK,YAAY;;AAElB,KAAK,IAAIC,IAAI,GAAGA,IAAI,OAAOA,GACzBD,UAAUE,MAAMD,IAAI,KAAOE,SAAS,IAAIT,MAAM;;ALThD,MAAMU,aAA+B,sBAAXN,UAA0BA,OAAOM,cAAcN,OAAOM,WAAWL,KAAKD;;AMIhG,SAASO,GAAGrD,SAASsD,KAAKC;EACxB,IAAIhE,OAAO6D,eAAuBpD,SAChC,OAAOT,OAAO6D;EAIhB,MAAMI,QADNxD,UAAUA,WAAW,CAAC,GACDyD,WAAWzD,QAAQ6C,OAAOA;EAe/C,OAbAW,KAAK,KAAe,KAAVA,KAAK,KAAY,IAC3BA,KAAK,KAAe,KAAVA,KAAK,KAAY,KDDtB,SAAyBE,KAAKH,SAAS;IAG5C,OAAOP,UAAUU,IAAIH,SAAS,MAAMP,UAAUU,IAAIH,SAAS,MAAMP,UAAUU,IAAIH,SAAS,MAAMP,UAAUU,IAAIH,SAAS,MAAM,MAAMP,UAAUU,IAAIH,SAAS,MAAMP,UAAUU,IAAIH,SAAS,MAAM,MAAMP,UAAUU,IAAIH,SAAS,MAAMP,UAAUU,IAAIH,SAAS,MAAM,MAAMP,UAAUU,IAAIH,SAAS,MAAMP,UAAUU,IAAIH,SAAS,MAAM,MAAMP,UAAUU,IAAIH,SAAS,OAAOP,UAAUU,IAAIH,SAAS,OAAOP,UAAUU,IAAIH,SAAS,OAAOP,UAAUU,IAAIH,SAAS,OAAOP,UAAUU,IAAIH,SAAS,OAAOP,UAAUU,IAAIH,SAAS;AAChf,GCSSI,CAAgBH;AACzB;;ANzBejE,SAAA;EACb6D;GCFU5D,iBAAc,SAAdA;EAAc,OAAdA,eAAc,UAAA,WAAdA,eAAc,SAAA,UAAdA,eAAc,cAAA;EAAdA,eAAc,yBAAA,0BAAdA,eAAc,cAAA;EAAdA,eAAc,aAAA,cAAdA,eAAc,OAAA,QAAdA,eAAc,kBAAA;EAAdA,eAAc,YAAA,aAAdA,eAAc,mBAAA;EAAdA,eAAc,iBAAA,kBAAdA,eAAc,yBAAA;EAAdA,eAAc,mBAAA,oBAAdA,eAAc,wBAAA;EAAdA;AAAc,CAAA,CAAA,KAiBdC,oBAAiB,SAAjBA;EAAiB,OAAjBA,kBAAiB,MAAA,OAAjBA,kBAAiB,KAAA,MAAjBA;AAAiB,CAAA,CAAA,KAKjBC,kBAAe,SAAfA;EAAe,OAAfA,gBAAe,aAAA,cAAfA,gBAAe,QAAA;EAAfA,gBAAe,WAAA,YAAfA;AAAe,CAAA,CAAA,KAMfC,0BAAuB,SAAvBA;EAAuB,OAAvBA,wBAAuB,WAAA,YAAvBA,wBAAuB,UAAA;EAAvBA;AAAuB,CAAA,CAAA;;AMxB5B,MAAMiE,UAAUA;EACrB,MAAMC,QAAa;EACnB,IAAIC;EAEJ,OAAO;IACL,aAAMC,CAAQ7C;MACZ2C,MAAMX,KAAKhC,MACP4C,SACFA,KAAKD,MAAMG,UACXF,YAAOG;AAEV;IAEDC,SAAsB,MACb,IAAIC,SAAW,SAAsBC;MAC1C,MAAMlD,MAAM2C,MAAMG;MAClB,IAAI9C,KAAK,OAAOkD,QAAQlD;MACxB4C,OAAOM;AACT;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;ACaN,MAAMC,SACJ;EAAA,KAAA,IAAA3C,OAAAC,UAAAC,QAAI0C,QAAKxC,IAAAA,MAAAJ,OAAAK,OAAA,GAAAA,OAAAL,MAAAK,QAALuC,MAAKvC,QAAAJ,UAAAI;EAAA,OACRwC,KACQD,MAAME,QAAO,CAACC,KAAKC,SACjBA,KAAKD,OACXF;AACJ,GAoBGI,aAAeC,SACnBA,SAA0B,mBAAVA,UAAuB9C,MAAM+C,QAAQD,QAQjDE,YAAY,SAACC;EAAwC,KAAAC,IAAAA,QAAArD,UAAAC,QAAxBqD,UAAO,IAAAnD,MAAAkD,QAAAA,IAAAA,QAAA,IAAA,IAAAE,QAAA,GAAAA,QAAAF,OAAAE,SAAPD,QAAOC,QAAAvD,KAAAA,UAAAuD;EACxC,KAAKD,QAAQrD,QAAQ,OAAOmD;EAC5B,MAAMI,SAASF,QAAQjB;EAEvB,IAAIW,WAASI,WAAWJ,WAASQ,SAC/B,KAAK,MAAMC,OAAOD,QACZR,WAASQ,OAAOC,SACbL,OAAOK,QAAMC,OAAOC,OAAOP,QAAQ;IAACK,CAACA,MAAM,CAAA;MAChDN,UAAUC,OAAOK,MAAMD,OAAOC,SAE9BC,OAAOC,OAAOP,QAAQ;IAACK,CAACA,MAAMD,OAAOC;;EAK3C,OAAON,UAAUC,WAAWE;AAC9B,GAmBMM,iBAAkBX,SACtB9C,MAAM+C,QAAQD,SAASE,UAAU,CAAA,MAAOF,SAASA,OAO7CY,kBAAmBC,OAAmBA,IAAIC,YAAYD,IAAIC,YAAY,CAAG,GAOzEC,qBAAsBF,OAC1BA,IAAIG,eAAeH,IAAIG,eAAe,CAAE,GAqE7BC,eAAeA,CAC1BC,OACAC,YAEO1B,OACLkB,gBAlJF;EAAA,KAAA,IAAAS,QAAArE,UAAAC,QAAI0C,QAAKxC,IAAAA,MAAAkE,QAAAC,QAAA,GAAAA,QAAAD,OAAAC,SAAL3B,MAAK2B,SAAAtE,UAAAsE;EAAA,OACR1B,KACQD,MAAME,QAAO,CAACC,KAAKC,UACjB;OAAID;OAAQC,KAAKH;OACvB;AACJ,CA8IC2B,CAjCgC,CACjCH,WACDI;EAGkD,KAHjDC,aACCA,cAAc,CAAA,GAAEC,UAChBA,WAAW,CAAA,KACgCF;EAC3C,MAAMG,oBAAoBF,cAAcL;EACxC,OAAKO,oBAEEjB,OAAOkB,QAAQD,mBAAmB9B,QAAO,CAACgC,GAACC;IAAmB,KAAhBrB,KAAKR,SAAM6B;IAE9D,MAAMC,iBAAiBL,SAASjB,MAAMuB;IACtC,OAAKD,iBAGE9B,MAAMJ,QAAO,CAACgC,GAAGI,cACf;SAAIJ;MAAGI,CAACA,WAAWF;SACzB,MALyBF;MAM3B,MAX4B,CAAE;EA2B/BK,CAA+Bd,UAC/B1B,OAAKmB,iBAtE6B,CACrCO,WAAqBL,aACbL,OAAOkB,QAAQb,WAAWlB,QAC/B,CAACgC,GAACM;EAAmB,KAAhB1B,KAAKR,SAAMkC;EACd,MAAMC,uBAAuBnC,OAAOoC,UAAUjB;EAK9C,OAJIgB,yBACFP,EAAEpB,OAAO2B,uBAGJP;IAET,IA2DsBS,CAAmClB,WACzD1B,OAAKsB,oBAnD+B,CACvCI,WAAqBH,gBACbP,OAAOkB,QAAQX,cAAcpB,QAClC,CAACgC,GAACU;EAAmB,KAAhB9B,KAAKR,SAAMsC;EACd,MAAMC,yBAAyBvC,OAAOoC,UAAUjB;EAKhD,OAJIoB,2BACFX,EAAEpB,OAAO+B,yBAGJX;IAET,IAwCyBY,CAAqCrB,WAL3D1B,CAOLyB,QAmBEuB,iBAAkBC,YACfjC,OAAOkB,QAAQe,UAAUjB,YAAY,IAAI7B,QAC9C,CAAC+C,eAAaC;EAAgB,KAAd,EAAG5C,SAAM4C;EACvB,SAAID,iBAEF3C,SACAS,OAAOoC,UAAUC,eAAeC,KAAK/C,OAAO,WAjB7BrC,MAkBDqC,OAAOQ;EAhBR,mBAAR7C,OACJ,iBAAiBf,KAAKe;EAHRA,IAAAA;KAqBnB;;AN7NF3C;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;yBMgP2BmG,CAAAA;IAC3B,MAAM6B,iBACuB,YAA3B7B,SAAS8B,gBAA4B,aAAa9B,SAAS8B;IAC7D,IACqB,eAAnBD,kBACmB,cAAnBA,kBACmB,cAAnBA,kBACmB,iBAAnBA,gBAEA,OAAOA;IAET,MAAM,IAAIzG,MACR,oBAAoB4E;;;EAtBUnB,kBAC5B9C,MAAM+C,QAAQD,SAAeA,MAAMkD,KAAKT,kBACrCA,eAAezC;;;;;;;;EAFUA,IAAAA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;ACpN3B,SAASmD,OAAOC;EACrB,IAAIlG,MAAM+C,QAAQmD,QAAQ;IACxB,MAAMC,SAAS;IACf,KAAK,IAAIhF,IAAI,GAAGA,IAAI+E,MAAMpG,QAAQqB,KAChCgF,OAAO/E,KAAK6E,OAAOC,MAAM/E;IAE3B,MAAMK,MAAM4E,OAAOC,OAAOF;IAC1B,OAAOC,OAAOC,OAAO,EAACC,aAAa9E,IAAI1B,QAAQ,MAAM0B;AACvD;EAAO;IACL,MAAM+E,WAyMH,SACL9D;MAEA,KAAK2D,OAAOI,SAAS/D,IAAI;QACvB,IAAiB,mBAANA,GACT,OAAIgE,cAAchE,KACT2D,OAAOM,MAhBHC,IAdE,oBADGlG,MA+B4BgC,KA7BzChC,MAEFgG,cAAchG,OAAOA,IAAIG,MAAM,KAAKH,KAYlCX,SAAS,IAAI,MAAM6G,IAAIA,GAeuB,SAE1CP,OAAOM,KAAKjE;QAEhB,IAAiB,mBAANA,GAChB,OAAKA,IAjBX,SAAqBmE;UACnB,MAAMC,MAAMC,SAASF;UACrB,OAAOR,OAAOM,KAAKG,KAAK;AAC1B,SAiBeE,CAAYtE,KAFZ2D,OAAOM,KAAK;QAIhB,IAAIjE,WACT,OAAO2D,OAAOM,KAAK;QACd,IAAIjE,aAAa3B,YACtB,OAAOsF,OAAOM,KAAKjE;QAEnB,MAAM,IAAIpD,MAAM;;MA9CtB,IAAwBoB;MAexB,IAAmBkG;MAkCjB,OAAOlE;AACT,KAlOqBuE,CAASd;IAC1B,OAA2B,MAApBK,SAASzG,UAAgByG,SAAS,KAAK,MAC1CA,WACAH,OAAOC,OAAO,EAACC,aAAaC,SAASzG,QAAQ,MAAMyG;;AAE3D;;AAaA,SAASD,aAAaW,KAAaxF;EACjC,IAAIwF,MAAM,IACR,OAAOb,OAAOM,KAAK,EAACO,MAAMxF;EACrB;IACL,MAAMyF,YAAYJ,SAASG,MAErBE,YAAYL,SAASrF,SAAS,KADpByF,UAAUpH,SAAS;IAEnC,OAAOsG,OAAOM,KAAKS,YAAYD,WAAW;;AAE9C;;AAiJA,SAAST,cAAchG;EACrB,OAA2B,SAApBA,IAAIG,MAAM,GAAG;AACtB;;AASA,SAASkG,SAASF;EAChB,IAAIA,UAAU,GACZ,MAAM,IAAIvH,MAAM;EAElB,MAAMwH,MAAMD,QAAQvF,SAAS;EAC7B,OAAOwF,IAAI/G,SAAS,IAAI,MAAM+G,MAAMA;AACtC;;ACzNO,SAASO,WAAWvC;EACzB,OAAe,QAAXA,UAAwB,OACrBA,QAAQlG,QAAQ,OAAO,IAAIA,QAAQ,OAAO;AACnD;;AAUO,SAAS0I,aAAWxC;EACzB,OAAe,QAAXA,UAAwB,OACrB,OAAOuC,WAAWvC;AAC3B;;ACxBO,SAASyC,cAAcC;EAC5B;IACE,OAAOC,KAAKC,MAAMF;AACpB,IAAE;IACA,OAAO;;AAEX;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;GCwBMG,OAAO,gBACD7J,wBAAwB8J,sQAgB9BC,MAAM,gBACA/J,wBAAwBgK,0IAS9BC,KAAK,eACApK,eAAeqK,2CAEZpK,kBAAkBqK,6yBAgD1BC,OAAO,IAAIC,IAAI3E,OAAO4E,KAAKX,KAAKC,MAAMK,OAE/BM,kBAAkBA,MAAmBZ,KAAKC,MAAMK,KAehDO,aAAY5I,KAAqC,mBAANA,GAC3CsD,YAAWtD,KAAuBO,MAAM+C,QAAQtD,IAChD6I,QAAS7I,KACd,SAANA,KAA2B,mBAANA,GACV8I,SAAU9I,KAA2B,QAALA,GAChC+I,SAAc/I,KAAiC,qBAANA,GAEzCgJ,gBAAiBC;EAC5B,KAAKJ,MAAMI,OAAOH,OAAOG,OAAOL,WAASK,KAAK,QAAO;EACrD,KAAK,IAAIpF,OAAO2E,MAAM,KAAKS,GAAG9C,eAAetC,MAAM,QAAO;EAC1D,QAAO;GAGIqF,KAAMD,OACjBA,GAAGE,SAASjL,kBAAkBqK,IACvBU,KAGIG,MAAMA,CAACH,IAAiBI,YACnCJ,GAAGE,SAASjL,kBAAkBoL;AAC9BL,GAAGI,SAASA,QACLJ,KAGHM,SAAUC,OAAyBP,OACvCA,GAAGQ,MAAMD,KACFN,GAAGD,MAGNS,mBAAoBC,QACN,QAAdA,KAAKC,QAAsBD,QAE/BlK,WACGoK,MAAMC,SAASH,KAAKC,MAAMhI,cAC3B;AAGK;KACF+H;EACHC,OAAOE,SAASH,KAAKC,MAAMhI;IAQlBmI,cAAcA,MAA0BhC,KAAKC,MAAMC,OAqEnD+B,cAAoCT,OAAOtL,eAAeqK,UAC1D2B,aAAoCV,OAAOtL,eAAeiM,SAC1DC,kBAAoCZ,OAC/CtL,eAAemM,cAEJC,2BAAoCd,OAC/CtL,eAAeqM,yBAEJC,qBAAoChB,OAC/CtL,eAAeuM,kBAEJC,iBAAoClB,OAC/CtL,eAAeyM,cAEJC,gBAAoCpB,OAC/CtL,eAAe2M,aAEJC,WAAoCtB,OAAOtL,eAAe6M,OAC1DC,eAAoCxB,OAC/CtL,eAAe+M,YAEJC,qBAAoC1B,OAC/CtL,eAAeiN,mBAEJC,oBAAoC5B,OAC/CtL,eAAemN,iBAEJC,2BAAoC9B,OAC/CtL,eAAeqN,yBAEJC,sBAAoChC,OAC/CtL,eAAeuN,mBAEJC,wBAAoClC,OAC/CtL,eAAeyN,wBAGXC,KAAMnC,OAAyBP,MAAoBA,GAAGQ,QAAQD,KAEvDoC,YAAkCD,GAAG1N,eAAeqK,UACpDuD,WAAkCF,GAAG1N,eAAeiM,SACpD4B,gBAAkCH,GAAG1N,eAAemM,cACpD2B,yBAAkCJ,GAC7C1N,eAAeqM,yBAEJ0B,mBAAkCL,GAC7C1N,eAAeuM,kBAEJyB,eAAkCN,GAAG1N,eAAeyM,cACpDwB,cAAkCP,GAAG1N,eAAe2M,aACpDuB,SAAkCR,GAAG1N,eAAe6M,OACpDsB,aAAkCT,GAAG1N,eAAe+M,YACpDqB,mBAAkCV,GAC7C1N,eAAeiN,mBAEJoB,kBAAkCX,GAAG1N,eAAemN,iBACpDmB,yBAAkCZ,GAC7C1N,eAAeqN,yBAEJkB,uBAAkCb,GAC7C1N,eAAeyN,wBAEJe,oBAAkCd,GAC7C1N,eAAeuN,mBAKJkB,QAAezD,MAC1BA,GAAGE,WAAWjL,kBAAkBoL,KAkB5BqD,UAAUnO,eACdyK;EAEyB,IADzB2D,MAAiExM,UAAAC,SAAA,UAAAqC,MAAAtC,UAAA,KAAAA,UAAA,KAAG;EAEpE;IAEE,IADA6I,KAfcA,CAAAA;MAChB,KAAK,IAAIpF,OAAOC,OAAO4E,KAAKO,KAC1B,KAAKT,KAAKqE,IAAIhJ,MACZ,MAAM,IAAIjE,MAAM,IAAIiE;MAExB,OAAOoF;MAUA6D,OAAe7D,KAChByD,MAAMzD,KAAK,MAAM,IAAIrJ,MAAM,sBAAsBqJ,GAAGI;IACxD,KAAKuD,IAAIvM,QAAQ,OAAO4I;IACxB,OAAO8D,OAAOzM,QAAQsM,KAChBI,YAAYD;IAClB,IAAIhE,OAAKiE,MAAM,OAAOL,QAAQK,IAAI/D,KAAK3I;IACvC,IAAIwI,OAAOkE,SAASA,KAAK,OAAOL,QAAQ1D,IAAI3I;IAC5C,IAAI0I,cAAcgE,MAAM,OAAOL,QAAQK,KAAK1M;IAC5C,MAAM,IAAIV,MAAM;AACjB,IAAC,OAAOqN;IACP,MAAMA;;AAEV;;AAWA,SAASnK;EAIsC,KAAA,IAAA2B,QAAArE,UAAAC,QAH1C6M,OAAI3M,IAAAA,MAAAkE,QAAAC,QAAA,GAAAA,QAAAD,OAAAC,SAAJwI,KAAIxI,SAAAtE,UAAAsE;EAIP,OAAOyI,MAAMC,QAAQF;EACrB,IAAI5J,UAAQ6J,OAAO,OAAQnN,KAAmB8C,KAAK9C,GAAGmN;EAItD,OAAOR,QAFIQ,MACCC;AAEd;;AAGA,MAAMC,aAAW,SAAIrK;EAAI,OAAkBA;AAAC,GAE/BjE,MAAM,SACjBkK,IACApF;EAEG,IADHyJ,WAAalN,UAAAC,SAAA,UAAAqC,MAAAtC,UAAA,KAAAA,UAAA,UAAGsC;EAEhB,OAA0B,QAAnBuG,GAAGsE,QAAQ1J,OAAeyJ,WAAWrE,GAAGsE,QAAQ1J;AACzD;;;;;;;;;;;WAcwBA,OAAiBoF,cAChCA,GAAGsE,QAAQ1J,MACXqF,GAAGD;;;;eAxReuE,OACzBjP,IAAImC,UAAU;IACZC,KAAK;IACLhC,SAAS;IACTkC,YACE;IACFjC,OAAON,OAAOU;MAET2J;aA+LoD8E,WAC3DA,QAAQC,SAAStP,wBAAwB8J;cACVyF,YAC/BA,SAASD,SAAStP,wBAAwBgK;;;;;;;;;;;;;;;;;QAThBa,MAC1BA,GAAGE,WAAWjL,kBAAkBqK;;;;;;gBApFmBqF,OAAM3E;IACzD,IAAI4E,SAASC;IAab,OAZA7E,GAAGtK,QAAQyB,UAAUuB,KAAKkM,SAE1B5E,GAAG7I,UAAUyN,UAAU9F,KAAKC,MAAMG;IAClCc,GAAG7I,UAAUyN,QAAQA,SAASA,QAC9B5E,GAAG7I,UAAUyN,QAAQxK,QAAQuK,IAAIvK,OACjC4F,GAAG7I,UAAUyN,QAAQE,aAAaH,IAAIG;IACtC9E,GAAG7I,UAAUyN,QAAQG,QAAQJ,IAAII,OACjC/E,GAAG7I,UAAUyN,QAAQhL,UAAU+K,IAAI/K;IACnCoG,GAAG7I,UAAUyN,QAAQI,kBAAkBlF,OAAK6E,IAAIK,mBAC5CL,IAAIK,gBAAgBzM,KAAKoM,OACzBA,IAAIK;IAED/E,GAAGD;;;;;;;;;;;;;;;;;eA/DV,SAACU;IAA0B,IAAEuE,OAAsB9N,UAAAC,SAAA,UAAAqC,MAAAtC,UAAA,KAAAA,UAAA,KAAG,CAAE;IAAA;MAEtDX,UACkB,qBAATkK,QAAuC,mBAATA,MACrC;MAEFlK,UAAuB,QAAbyO,KAAKC,MAAc;MAE7B,MAAMjG,UAAU6B,eACVoE,OAAOD,KAAKC,MACZN,SAASC;MACf,IAAIL,UAAuC;WAAI9D;;MAE3CA,KAAKyE,iBAAiBrF,OAAKY,KAAKyE,mBAClCX,UAAU;QAAC5K,SAAS8G,KAAKyE;WACtBzE,KAAKyE,iBAAiBrF,OAAKY,UAAO8D,UAAU;QAAC5K,SAAS8G;;MAE3D,MAAM9G,UAAU4K,QAAQ5K;MA6BxB,OA5BIA,YACF4K,QAAQ5K,UAAU,SAAC8G;QAAwB,KAAAxJ,IAAAA,OAAAC,UAAAC,QAAKC,OAAI,IAAAC,MAAAJ,OAAAA,IAAAA,OAAA,IAAA,IAAAK,OAAA,GAAAA,OAAAL,MAAAK,QAAJF,KAAIE,OAAAJ,KAAAA,UAAAI;QAAA,OAClD,EAACqC,SAAS6G,mBAAkBzG,QAC1BzE,OAAOwB,GAAGqO,OAAOA,SAASrO,MAAMM,QAChCqJ;AACD,UAEL8D,UAAU/D,iBAAiB+D,UAE3BxE,GAAGnE,SAAS+I,UAAU;WACjB3F;QACH2F;WACGJ;QACHU,MAAM;aACDjG,QAAQiG;aACc,mBAAdxE,KAAKwE,OAAoBxE,KAAKwE,OAAO,CAAE;aAC9CA,OAAO;YAACA,CAACA,QAAO;cAAQ,CAAA;;SAI5BA,SAAShQ,gBAAgBmQ,aAC3BrF,GAAGsF,eAAe5M,KAAKkM,UACdM,SAAShQ,gBAAgBqQ,QAClCvF,GAAGwF,MAAM9M,KAAKkM,UACLM,SACTlF,GAAGkF,QAAQN;MAGN5E;;AACR;OA8JgByF,CAAC7K,KAAaR,UAAgB4F,OAC/CA,GAAGsE,QAAQ1J,OAAOR,OACX6F,GAAGD;UAIV,SAAIpF;IAAW,IAAEwK,KAA2CjO,UAAAC,SAAA,UAAAqC,MAAAtC,UAAA,KAAAA,UAAA,KAAGiN;IAAQ,OACtEpE,OACCA,GAAGsE,QAAQ1J,OAAOwK,GAAGpF,GAAGsE,QAAQ1J,MAAMoF,KAC/BC,GAAGD;AACX;OA/EyBA,MAAoBA,GAAGI;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;", "x_google_ignoreList": [0, 4, 5, 6]}